/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-18 11:46:13
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-19 18:00:14
 * @FilePath: /vite-element-components/src/utils/index.js
 * @Description:
 */
export const isObject = (val) => typeof val === 'object';
export const isFunction = (val) => typeof val === 'function';
/**
 * 生成自定义长度的随机字符串
 * @param {*} length
 * @returns string
 */
export const generateRandomString = (length) => {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};
