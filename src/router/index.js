/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-24 17:02:36
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-03 16:26:41
 * @FilePath: /vite-element-components/src/router/index.js
 * @Description:
 */
import VueRouter from 'vue-router';
import routerPages from './routerPage/pages.js';

const createRouter = () =>
  new VueRouter({
    mode: 'history',
    base: '/element-component',
    routes: routerPages,
    scrollBehavior() {
      return { x: 0, y: 0 };
    },
  });
const router = createRouter();

export default router;
