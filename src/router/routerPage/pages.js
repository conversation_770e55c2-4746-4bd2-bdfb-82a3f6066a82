/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2022-07-02 22:48:49
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-10 16:46:27
 * @FilePath: /vite-element-components/src/router/routerPage/pages.js
 * @Description:
 */

export default [
  {
    path: '/',
    name: 'Home',
    redirect: '/index',
    component: () => import('@/views/home.vue'),
    children: [
      {
        path: 'index',
        name: '首页',
        title: '首页',
        component: () => import('@/views/index.vue'),
      },
      {
        path: 'changelog',
        name: '更新日志',
        title: '更新日志',
        component: () => import('@/views/changelog.vue'),
      },
      {
        path: 'button',
        name: 'button按钮',
        title: 'Button',
        component: () => import('../../../packages/button/doc/doc.md'),
      },
      {
        path: 'dialog',
        name: 'dialog弹出层',
        title: 'Dialog',
        component: () => import('../../../packages/dialog/doc/doc.md'),
      },
      {
        path: 'editTable',
        name: 'editTable可编辑表格',
        title: 'editTable',
        component: () => import('../../../packages/editTable/doc/doc.md'),
      },
      {
        path: 'empty',
        name: 'empty空内容',
        title: 'Empty',
        component: () => import('../../../packages/empty/doc/doc.md'),
      },
      {
        path: 'form',
        name: 'form表单',
        title: 'Form',
        component: () => import('../../../packages/form/doc/doc.md'),
      },
      {
        path: 'formItems',
        name: 'formItems表单项',
        title: 'FormItems',
        component: () => import('../../../packages/formItems/doc/doc.md'),
      },

      {
        path: 'input',
        name: 'input输入框',
        title: 'Input',
        component: () => import('../../../packages/input/doc/doc.md'),
      },
      {
        path: 'inputNumber',
        name: 'inputNumber数字输入',
        title: 'InputNumber',
        component: () => import('../../../packages/inputNumber/doc/doc.md'),
      },
      {
        path: 'inputNumberRange',
        name: 'inputNumberRange数字区间',
        title: 'InputNumberRange',
        component: () => import('../../../packages/inputNumberRange/doc/doc.md'),
      },
      {
        path: 'radio',
        name: 'radio单选框',
        title: 'Radio',
        component: () => import('../../../packages/radio/doc/doc.md'),
      },
      {
        path: 'select',
        name: 'select下拉选择',
        title: 'Select',
        component: () => import('../../../packages/select/doc/doc.md'),
      },
      {
        path: 'selectTree',
        name: 'selectTree下拉树',
        title: 'SelectTree',
        component: () => import('../../../packages/selectTree/doc/doc.md'),
      },
      {
        path: 'switch',
        name: 'switch开关',
        title: 'Switch',
        component: () => import('../../../packages/switch/doc/doc.md'),
      },
      {
        path: 'cascader',
        name: 'cascader级联',
        title: 'Cascader',
        component: () => import('../../../packages/cascader/doc/doc.md'),
      },
      {
        path: 'checkbox',
        name: 'checkbox复选框',
        title: 'Checkbox',
        component: () => import('../../../packages/checkbox/doc/doc.md'),
      },
      {
        path: 'custom',
        name: 'custom自定义内容',
        title: 'Custom',
        component: () => import('../../../packages/custom/doc/doc.md'),
      },
      {
        path: 'datePicker',
        name: 'datePicker日期时间选择器',
        title: 'DatePicker',
        component: () => import('../../../packages/datePicker/doc/doc.md'),
      },
      {
        path: 'transfer',
        name: 'transfer穿梭框',
        title: 'Transfer',
        component: () => import('../../../packages/transfer/doc/doc.md'),
      },
      {
        path: 'upload',
        name: 'upload上传',
        title: 'Upload',
        component: () => import('../../../packages/upload/doc/doc.md'),
      },
      {
        path: 'pagination',
        name: 'pagination分页',
        title: 'Pagination',
        component: () => import('../../../packages/pagination/doc/doc.md'),
      },
      {
        path: 'search',
        name: 'search搜索',
        title: 'Search',
        component: () => import('../../../packages/search/doc/doc.md'),
      },
      {
        path: 'table',
        name: 'table表格',
        title: 'Table',
        component: () => import('../../../packages/table/doc/doc.md'),
      },
      {
        path: 'searchTable',
        name: 'searchTable搜索表格',
        title: 'SearchTable',
        component: () => import('../../../packages/searchTable/doc/doc.md'),
      },
      {
        path: 'text',
        name: 'text文本',
        title: 'Text',
        component: () => import('../../../packages/text/doc/doc.md'),
      },
      {
        path: 'toolbar',
        name: 'toolbar工具栏',
        title: 'Toolbar',
        component: () => import('../../../packages/toolbar/doc/doc.md'),
      },
      {
        path: 'tooltip',
        name: 'tooltip文字提示',
        title: 'Tooltip',
        component: () => import('../../../packages/tooltip/doc/doc.md'),
      },
    ],
  },
];
