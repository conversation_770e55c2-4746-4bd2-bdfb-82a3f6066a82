.markdown-body {
  color: #24292e;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
  padding: 20px 20px 40px;
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;

  * {
    margin: 0;
    padding: 0;
  }

  & > *:not(.component-view) {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }
  & > h1,
  & > h2,
  & > h3,
  & > h4,
  & > h5,
  & > h6 {
    font-weight: 600;
    line-height: 1.25;
    margin-top: 24px;
    margin-bottom: 16px;
  }

  & > h1 {
    font-size: 2em;
    padding-bottom: 0.3em;
    border-bottom: 1px solid #eaecef;
  }

  & > h2 {
    font-size: 1.5em;
    padding-bottom: 0.3em;
    border-bottom: 1px solid #eaecef;
  }

  & > h3 {
    font-size: 1.25em;
  }

  & > h4 {
    font-size: 1em;
  }

  & > h5 {
    font-size: 0.875em;
  }

  & > h6 {
    font-size: 0.85em;
    color: #6a737d;
  }

  & > p {
    margin-top: 0;
    margin-bottom: 16px;
  }

  & > ul,
  & > ol {
    padding-left: 2em;
    margin-top: 0;
    margin-bottom: 16px;

    ul,
    ol {
      margin-top: 8px;
      margin-bottom: 8px;
      padding-left: 16px;
    }
  }

  & > code {
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    background-color: rgba(27, 31, 35, 0.05);
    border-radius: 3px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  }

  & > pre {
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    background-color: #f6f8fa;
    border-radius: 3px;

    code {
      padding: 0;
      margin: 0;
      font-size: 100%;
      word-break: normal;
      white-space: pre;
      background: transparent;
      border: 0;
    }
  }

  & > blockquote {
    margin: 0;
    padding: 0 1em;
    color: #6a737d;
    border-left: 0.25em solid #dfe2e5;
  }

  & > hr {
    height: 0.25em;
    padding: 0;
    margin: 24px 0;
    background-color: #e1e4e8;
    border: 0;
  }

  & > img {
    max-width: 100%;
    box-sizing: content-box;
    background-color: #fff;
  }
  & > table {
    border-spacing: 0;
    border-collapse: collapse;
    margin-top: 0;
    margin-bottom: 16px;
    width: 100%;
    overflow: auto;

    th,
    td {
      padding: 10px 13px;
      border: 1px solid #dfe2e5;
      word-break: break-word;
    }

    tr {
      background-color: #fff;
      border-top: 1px solid #c6cbd1;

      &:nth-child(2n) {
        background-color: #f6f8fa;
      }
    }
  }
  // 组件样式
  .component-view {
    background-color: #fff;
    padding: 12px;
    margin-top: 10px;
    border-radius: 4px;
    box-shadow:
      0 3px 14px 2px rgba(0, 0, 0, 0.05),
      0 8px 10px 1px rgba(0, 0, 0, 0.06),
      0 5px 5px -3px rgba(0, 0, 0, 0.1);
  }
}
