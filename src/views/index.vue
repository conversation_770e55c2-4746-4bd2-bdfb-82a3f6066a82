<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-24 17:02:36
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-17 10:01:52
 * @FilePath: /vite-element-components/src/views/index.vue
 * @Description:
-->
<template>
  <div class="home-container">
    <el-card class="intro-card">
      <div class="header-wrap">
        <h2 class="brand-title">
          <i class="el-icon-s-platform"></i>
          Vite Element Components
        </h2>
        <p class="sub-title">基于 Vue 2.6 + Element-eoss 2.14 构建的企业级中后台组件库</p>
      </div>

      <el-alert title="快速开始" type="success" :closable="false">
        <div class="install-box mt-12">
          <h3>安装</h3>
          <pre>
            <code>
              // 安装基础组件库
              // 必须安装以下 peer dependencies：
              npm install vue@2.6.14 element-eoss@^2.14.3

              // 通过本地路径引入组件库
              import Vue from 'vue'
              import ElementComponents from '../lib/scic-element-components'; // 自己的目录
              import '../lib/scic-element-components/style.css';

              // 或npm 安装
              import ElementComponents from 'scic-element-components'
              import 'scic-element-components/lib/style.css';

              Vue.use(ElementComponents);
            </code>
          </pre>
        </div>
      </el-alert>

      <div class="component-nav mt-12">
        <h3>组件导航</h3>
        <el-row :gutter="32" class="mt-12">
          <el-col :span="6" v-for="(item, index) in navItems" :key="index">
            <el-link :href="item.path" class="nav-card" :underline="false" type="primary">
              <i class="el-icon-arrow-right link-icon"></i>
              <span class="link-text">{{ item.title }}</span>
            </el-link>
          </el-col>
        </el-row>
      </div>

      <div class="notice mt-12">
        <el-alert title="开发规范" type="info">
          <ul>
            <li>1. 所有组件需在 packages 目录下按规范创建</li>
            <li>2. 组件文档使用 Markdown 格式编写</li>
            <li>3. 保持与 Element UI 原有 API 兼容</li>
          </ul>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      navItems: [
        { title: '表单组件', path: '/form' },
        { title: '数据展示', path: '/table' },
        { title: '导航组件', path: '/pagination' },
        { title: '反馈组件', path: '/dialog' },
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
.home-container {
  padding: 32px;

  .intro-card {
    .header-wrap {
      text-align: center;
      margin-bottom: 40px;

      .brand-title {
        font-size: 28px;
        color: #303133;
        margin: 0 0 16px;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 32px;
          margin-right: 12px;
          color: var(--brand-6, #0f45ea);
        }
      }

      .sub-title {
        font-size: 16px;
        color: #606266;
        margin: 0;
      }
    }

    .install-box,
    .usage-box {
      pre {
        background-color: #f5f7fa;
        border-radius: 4px;
        padding: 10px;
        margin: 10px 0;
        code {
          color: #606266;
          font-family: Monaco, Menlo, Consolas, 'Courier New', monospace;
          font-size: 14px;
          line-height: 1.8;
        }
      }
    }

    .component-nav {
      .nav-card {
        display: block;
        background: #f5f7fa;
        border-radius: 4px;
        padding: 16px;
        transition: all 0.3s ease;

        &:hover {
          background: #ecf5ff;
          transform: translateY(-2px);
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .link-icon {
          margin-right: 8px;
          color: var(--brand-6, #0f45ea);
        }

        .link-text {
          font-size: 15px;
        }
      }
    }

    .notice {
      margin-top: 32px;
      ul {
        margin: 16px 0 0;
        padding-left: 20px;
        li {
          line-height: 1.8;
          color: #606266;
        }
      }
    }
  }
}
.mt-12 {
  margin-top: 12px;
}
</style>
