<template>
  <el-container class="container">
    <transition name="fade">
      <el-aside class="left-menu" v-show="showMenu">
        <li
          v-for="(item, index) in menuList[0].children"
          :key="index"
          :class="{ active: activeIndex === index }"
          @click="switchMenu(item, index)"
        >
          {{ item.name }}
        </li>
      </el-aside>
    </transition>
    <div class="container-right">
      <div class="router-view">
        <div class="markdown">
          <router-view v-slot="{ Component, route }">
            <transition name="fade" mode="out-in">
              <el-container :key="route.name">
                <component :is="Component" />
              </el-container>
            </transition>
          </router-view>
        </div>
        <markdown-anchor class="anchor" />
      </div>
    </div>
  </el-container>
</template>

<script>
import menuList from '@/router/routerPage/pages.js';
import MarkdownAnchor from '@/components/anchor.vue';

export default {
  components: {
    MarkdownAnchor,
  },
  data() {
    return {
      showMenu: true,
      menuList,
    };
  },
  computed: {
    activeIndex: {
      get() {
        return (
          this.menuList[0].children.findIndex((menu) => `${this.menuList[0].path}${menu.path}` === this.$route.path) ||
          0
        );
      },
      set(val) {},
    },
  },
  created() {
    if (this.$route.path === '/') {
      this.showMenu = false;
    }
    if (this.$route.query.showMenu === 'true') {
      this.showMenu = true;
    }
  },
  methods: {
    switchMenu(item, index) {
      if (index === this.activeIndex) return;
      this.activeIndex = index;
      this.$router.push(item.path).then(() => {
        if (this.$el.querySelector('.container-right')) {
          this.$el.querySelector('.container-right').scrollTop = 0;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.15s ease-in;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transition: opacity 0.15s ease-in;
}

.container {
  width: 100%;
  height: 100vh;

  .left-menu {
    width: 260px;
    height: auto;
    overflow-y: auto;
    border-right: 1px solid #f0f0f0;

    .title-logo {
      img {
        width: 22px;
        height: 22px;
        padding: 0 20px;
        margin-top: 10px;
        vertical-align: -3px;
      }

      span {
        display: inline-block;
        font-size: 18px;
        font-weight: 700;
        line-height: 22px;
        color: #4a5264;
      }
    }

    li {
      box-sizing: border-box;
      padding: 0 20px;
      line-height: 40px;
      color: #606266;
      list-style: none;
      cursor: pointer;

      &:hover {
        color: var(--brand-6);
      }
    }

    li.active {
      font-weight: 600;
      color: var(--brand-6);
      background: rgb(14 128 235 / 10%);
      border-right: 4px solid var(--brand-6, #0f45ea);
    }
  }
  &-right {
    flex-grow: 1;
    overflow-y: auto;
    .router-view {
      display: flex;
      .markdown {
        overflow: hidden;
        &-body {
          width: 100%;
        }
        flex-grow: 1;
      }
      .anchor {
        flex-shrink: 0;
      }
    }
  }
}
</style>
