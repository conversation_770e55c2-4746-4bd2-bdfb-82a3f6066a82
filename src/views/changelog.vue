<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-10 16:42:12
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-04 14:58:34
 * @FilePath: /vite-element-components/src/views/changelog.vue
 * @Description:
-->
<template>
  <div class="changelog-page">
    <el-card shadow="hover" class="changelog-card">
      <div class="changelog-header">
        <div class="changelog-title-section">
          <i class="el-icon-notebook-2 changelog-icon"></i>
          <span class="changelog-title">更新日志</span>
        </div>
        <div class="changelog-release-info" v-if="latestRelease">
          <div class="release-time">
            <i class="el-icon-time"></i>
            <span>最新发布：{{ formatReleaseTime(latestRelease.date) }}</span>
          </div>
          <div class="release-version">
            <i class="el-icon-price-tag"></i>
            <span>当前版本：{{ latestRelease.version }}</span>
          </div>
        </div>
      </div>
      <el-timeline class="changelog-timeline">
        <el-timeline-item
          v-for="item in changelogList"
          :key="item.version"
          :timestamp="formatVersionTimestamp(item)"
          placement="top"
          color="#409EFF"
        >
          <el-card class="changelog-version-card" shadow="never">
            <div class="version-header">
              <h3 class="version-title">{{ item.version }}</h3>
              <span class="version-date" v-if="item.date">{{ formatReleaseTime(item.date) }}</span>
            </div>
            <vue-markdown :source="item.content" class="changelog-md" />
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script>
import VueMarkdown from 'vue-markdown';

export default {
  name: 'ChangelogPage',
  components: { VueMarkdown },
  data() {
    return {
      changelogList: [],
      latestRelease: null, // 最新发布信息
    };
  },
  created() {
    this.loadChangelog();
  },
  methods: {
    async loadChangelog() {
      try {
        const isDev = import.meta.env.MODE === 'development';
        let res;
        if (isDev) {
          res = await fetch('/CHANGELOG.md');
        } else {
          res = await fetch('/element-component/CHANGELOG.md');
        }
        const text = await res.text();
        this.changelogList = this.parseChangelog(text);
      } catch (e) {
        this.changelogList = [{ version: '加载失败', content: e.message }];
      }
    },
    parseChangelog(md) {
      // 按 ## [version] 分割
      const blocks = md.split(/## \[/).filter(Boolean);
      const changelogList = blocks.map((block) => {
        const match = block.match(/^(.*?)\]/);
        const version = match ? match[1] : '未知版本';

        // 尝试解析发布时间，格式可能是：## [1.0.0](link) (2025-08-04) 或 ## [1.0.0] - 2025-08-04
        const dateMatch = block.match(/\)\s*\((\d{4}-\d{2}-\d{2})\)/) || block.match(/\]\s*-\s*(\d{4}-\d{2}-\d{2})/);
        const date = dateMatch ? dateMatch[1] : null;

        // 只保留每个版本的内容部分
        const content = block.replace(/^(.*?)\](.*?\n)/, '').trim();
        return { version, content, date };
      });

      // 设置最新发布信息（第一个有日期的版本）
      const latestWithDate = changelogList.find((item) => item.date);
      if (latestWithDate) {
        this.latestRelease = {
          version: latestWithDate.version,
          date: latestWithDate.date,
        };
      } else if (changelogList.length > 0) {
        // 如果没有日期信息，使用第一个版本
        this.latestRelease = {
          version: changelogList[0].version,
          date: new Date().toISOString().split('T')[0], // 使用当前日期
        };
      }

      return changelogList;
    },
    formatVersionTimestamp(item) {
      // timeline的timestamp显示版本号
      return `v${item.version}`;
    },
    formatReleaseTime(dateString) {
      if (!dateString) return '未知时间';

      try {
        const date = new Date(`${dateString}T00:00:00`); // 确保使用本地时区
        const now = new Date();

        // 只比较日期部分，忽略时间
        const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        const nowOnly = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const diffTime = nowOnly - dateOnly;
        const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

        // 格式化为中文日期
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const formattedDate = `${year}年${month}月${day}日`;

        // 添加相对时间提示
        if (diffDays === 0) {
          return `${formattedDate} (今天)`;
        }
        if (diffDays === -1) {
          return `${formattedDate} (明天)`;
        }
        if (diffDays === 1) {
          return `${formattedDate} (昨天)`;
        }
        if (diffDays > 0 && diffDays <= 7) {
          return `${formattedDate} (${diffDays}天前)`;
        }
        if (diffDays < 0 && diffDays >= -7) {
          return `${formattedDate} (${Math.abs(diffDays)}天后)`;
        }
        if (diffDays > 0 && diffDays <= 30) {
          const weeks = Math.floor(diffDays / 7);
          return `${formattedDate} (${weeks}周前)`;
        }
        if (diffDays > 0 && diffDays <= 365) {
          const months = Math.floor(diffDays / 30);
          return `${formattedDate} (${months}个月前)`;
        }
        if (diffDays > 0) {
          const years = Math.floor(diffDays / 365);
          return `${formattedDate} (${years}年前)`;
        }
        return formattedDate;
      } catch (error) {
        return dateString;
      }
    },
  },
};
</script>

<style scoped>
.changelog-page {
  max-width: 900px;
  margin: 0 auto;
  padding: 32px 16px;
  background: var(--el-bg-color, #f6f8fa);
  min-height: 100vh;
}
.changelog-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);
  padding: 32px 24px 24px 24px;
  background: var(--el-bg-color, #fff);
}
.changelog-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
}
.changelog-title-section {
  display: flex;
  align-items: center;
}
.changelog-title {
  font-size: 2rem;
  font-weight: bold;
  margin-left: 12px;
  color: var(--el-text-color-primary, #222);
}
.changelog-icon {
  font-size: 2.2rem;
  color: #409eff;
}
.changelog-release-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular, #606266);
}
.release-time,
.release-version {
  display: flex;
  align-items: center;
  gap: 6px;
}
.release-time i,
.release-version i {
  font-size: 16px;
  color: #409eff;
}
.release-time span,
.release-version span {
  font-weight: 500;
}
.changelog-timeline {
  margin-left: 8px;
}
.changelog-version-card {
  margin-bottom: 8px;
  border-radius: 8px;
  background: var(--el-bg-color-overlay, #f9f9f9);
  border: none;
}
.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter, #ebeef5);
}
.version-title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--el-text-color-primary, #303133);
}
.version-date {
  font-size: 14px;
  color: var(--el-text-color-regular, #606266);
  font-weight: 500;
  background: var(--el-color-primary-light-9, #ecf5ff);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid var(--el-color-primary-light-7, #a0cfff);
}
.changelog-md {
  font-size: 15px;
  color: var(--el-text-color-regular, #333);
  line-height: 1.8;
  word-break: break-all;
}
/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .changelog-page {
    background: #181818;
  }
  .changelog-card {
    background: #232323;
    color: #eee;
  }
  .changelog-version-card {
    background: #232323;
    color: #eee;
  }
  .changelog-title {
    color: #fff;
  }
  .changelog-md {
    color: #ccc;
  }
  .changelog-release-info {
    color: #aaa;
  }
  .release-time span,
  .release-version span {
    color: #ddd;
  }
  .version-title {
    color: #fff;
  }
  .version-date {
    background: #2c3e50;
    border-color: #34495e;
    color: #bdc3c7;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .changelog-header {
    flex-direction: column;
    align-items: flex-start;
  }
  .changelog-release-info {
    align-items: flex-start;
    width: 100%;
  }
  .changelog-title {
    font-size: 1.5rem;
  }
  .changelog-icon {
    font-size: 1.8rem;
  }
  .changelog-page {
    padding: 16px 8px;
  }
  .changelog-card {
    padding: 20px 16px 16px 16px;
  }
  .version-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  .version-title {
    font-size: 1.1rem;
  }
  .version-date {
    align-self: flex-start;
  }
}
</style>
