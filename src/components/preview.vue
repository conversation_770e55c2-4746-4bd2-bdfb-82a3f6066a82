<template>
  <div class="pre-code-box">
    <span class="m-copy" v-if="showCode" @click="copyCode">
      <i class="el-icon-copy-document" size="16" />
    </span>
    <div class="showCode" @click="showOrHideCode">
      <span>{{ showCode ? '隐藏代码' : '显示代码' }}</span>
      <i :class="showCode ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" size="16" />
    </div>
    <transition name="slide-fade">
      <pre class="language-html" v-if="showCode" v-highlight><code class="language-html">{{ sourceCode }}</code></pre>
    </transition>

    <textarea id="inputCopy" />
  </div>
</template>

<script>
export default {
  props: {
    compName: {
      type: String,
      default: '',
      require: true,
    },
    demoName: {
      type: String,
      default: '',
      require: true,
    },
  },
  data() {
    return {
      showCode: false,
      border: '1px solid rgba(0,0,0,.06)',
      sourceCode: '',
    };
  },
  mounted() {
    this.getSourceCode();
  },
  methods: {
    showOrHideCode() {
      this.showCode = !this.showCode;
      if (this.showCode) {
        this.border = '0';
      } else {
        this.border = '1px solid rgba(0,0,0,.06)';
      }
    },
    async getSourceCode() {
      const isDev = import.meta.env.MODE === 'development';
      if (isDev) {
        this.sourceCode = (
          await import(/* @vite-ignore */ `../../packages/${this.compName}/doc/${this.demoName}.vue?raw`)
        ).default;
      } else {
        this.sourceCode = await fetch(`/element-component/packages/${this.compName}/doc/${this.demoName}.vue`).then(
          (res) => res.text(),
        );
      }
    },
    copyCode() {
      const input = document.getElementById('inputCopy');
      input.value = this.sourceCode;
      input.select();
      if (document.execCommand('copy')) {
        document.execCommand('copy');
        this.$message.success('代码复制成功');
      } else {
        this.$message.success('代码复制失败');
      }
    },
  },
};
</script>

<style scoped lang="scss">
#inputCopy {
  position: fixed;
  z-index: -99999999;
  height: 0;
  border: 0;
  outline: none;
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.1s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.1s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0.5;
  transform: translateY(-10px);
}

pre {
  margin: 0;
}

.pre-code-box {
  position: relative;
  width: 100%;
  height: auto;
  // min-height: 100%;
  margin: 0 0 15px;
  overflow: hidden;
  background-color: #f3f3f3;
  // border: 1px solid  #eee;
  border-top: 0;
  border-radius: 5px;
  transition: all 0.15s ease-out;

  .m-copy {
    position: absolute;
    top: 8px;
    right: 10px;
    cursor: pointer;

    i {
      font-size: 22px;
      color: #b7b3b3;

      &:hover {
        color: #000;
      }
    }
  }

  .showCode {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 42px;
    font-size: 14px;
    color: #505050;
    text-align: center;
    cursor: pointer;
    background: #f9f9f9;
    box-shadow: 0 16px 15px -16px rgb(0 0 0 / 10%);
    position: sticky;

    i {
      margin-left: 10px;
    }

    i.rotate {
      transform: rotate(180deg);
    }

    &:hover {
      color: #0e80eb;
      background: #f9f9f9;
    }
  }

  &:hover {
    box-shadow: 0 16px 15px -16px rgb(0 0 0 / 10%);
  }
}
/* 自定义 tab 宽度为 2 个空格 */
.hljs {
  tab-size: 2;
}
</style>
