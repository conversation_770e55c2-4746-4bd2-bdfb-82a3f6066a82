<template>
  <div class="markdown-anchor">
    <div class="anchor-link" v-for="(item, index) in anchors" :key="index" @click="scrollToAnchor(item.id)">
      <span
        :class="['anchor-text', { 'is-active': activeAnchor === item.id }]"
        :style="{ paddingLeft: item.level * 12 + 'px' }"
      >
        {{ item.text }}
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MarkdownAnchor',
  data() {
    return {
      anchors: [],
      activeAnchor: '',
      observer: null,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initAnchors();
      this.initIntersectionObserver();
      window.addEventListener('hashchange', this.handleHashChange);
    });
    this.$watch(
      () => this.$route.path,
      () => {
        this.$nextTick(() => {
          this.initAnchors();
          this.initIntersectionObserver();
        });
      },
    );
  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
    window.removeEventListener('hashchange', this.handleHashChange);
  },
  methods: {
    initAnchors() {
      const content = document.querySelector('.markdown-body');
      if (!content) {
        this.anchors = [];
        return false;
      }
      const headings = content.querySelectorAll('h1, h2, h3, h4, h5, h6');
      const usedIds = new Set();

      this.anchors = Array.from(headings).map((heading) => {
        const id = heading.textContent.toLowerCase();

        // 确保ID唯一
        let uniqueId = id;
        let counter = 1;
        while (usedIds.has(uniqueId)) {
          uniqueId = `${id}-${counter}`;
          counter += 1;
        }
        usedIds.add(uniqueId);

        heading.id = uniqueId;
        return {
          id: uniqueId,
          text: heading.textContent,
          level: parseInt(heading.tagName.charAt(1), 10),
        };
      });
    },
    scrollToAnchor(id) {
      const element = document.getElementById(id);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
        this.activeAnchor = id;
        window.history.pushState(null, null, `#${id}`);
      }
    },
    handleHashChange() {
      const hash = window.location.hash.slice(1);
      if (hash) {
        this.scrollToAnchor(hash);
      }
    },
    initIntersectionObserver() {
      let debounceTimer;
      this.observer = new IntersectionObserver(
        (entries) => {
          clearTimeout(debounceTimer);
          debounceTimer = setTimeout(() => {
            const intersectingEntries = entries.filter((entry) => entry.isIntersecting);
            if (intersectingEntries.length > 0) {
              // 选择最接近视口顶部的标题
              const topEntry = intersectingEntries.reduce((prev, curr) =>
                Math.abs(curr.boundingClientRect.top) < Math.abs(prev.boundingClientRect.top) ? curr : prev,
              );
              this.activeAnchor = topEntry.target.id;
            }
          }, 100);
        },
        {
          rootMargin: '-20px 0px -80% 0px',
          threshold: [0, 1],
        },
      );

      this.anchors.forEach((anchor) => {
        const element = document.getElementById(anchor.id);
        if (element) {
          this.observer.observe(element);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.markdown-anchor {
  position: sticky;
  top: 100px;
  right: 20px;
  width: 200px;
  margin-left: 15px;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  background-color: #fff;
  border-left: 1px solid #eaecef;
  padding: 8px 0;

  .anchor-link {
    cursor: pointer;
    padding: 4px 0;

    .anchor-text {
      display: block;
      color: #666;
      font-size: 13px;
      line-height: 1.5;
      transition: color 0.3s;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        color: var(--brand-6, #0f45ea);
      }

      &.is-active {
        color: var(--brand-6, #0f45ea);
      }
    }
  }
}
</style>
