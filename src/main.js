/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-24 17:02:36
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-16 09:03:41
 * @FilePath: /vite-element-components/src/main.js
 * @Description:
 */
import Vue from 'vue';
import App from './App.vue';
import VueRouter from 'vue-router';
import ElementUI from 'element-eoss';
import router from '@/router/index.js';
// import AppComponents from 'scic-element-components';
// import 'scic-element-components/lib/style.css';
import AppComponents from '../packages/index';
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-dark-reasonable.css';
import './styles/variables.scss';

Vue.use(ElementUI)
  .use(AppComponents, {
    serveUrl: 'aaa',
  })
  .use(router);

Vue.directive('highlight', (el) => {
  const blocks = el.querySelectorAll('pre code');
  blocks.forEach((block) => {
    hljs.highlightBlock(block);
  });
});

Vue.use(VueRouter);

new Vue({
  render: (h) => h(App),
  router,
}).$mount('#app');
