/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-28 14:22:21
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-06 16:14:35
 * @FilePath: /vite-element-components/vite.config.js
 * @Description:
 */
import { defineConfig } from 'vite';
import { createVuePlugin } from 'vite-plugin-vue2';
import { visualizer } from 'rollup-plugin-visualizer';
import { lazyImport, VxeResolver } from 'vite-plugin-lazy-import';
import Markdown from 'vite-plugin-md';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isBuildLib = mode === 'lib';
  const baseConfig = {
    base: './',
    server: {
      proxy: {
        '/api': {
          target: 'http://dayuding.eimm.wisesoft.net.cn:8099',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
      },
    },
    plugins: [
      createVuePlugin({
        jsx: true,
        include: [/\.md$/, /\.vue$/, /\.setup\.[cm]?[jt]sx?$/],
      }),
      Markdown(),
      // 打包分析 - 在构建时启用
      ...(process.env.ANALYZE
        ? [
            visualizer({
              open: true,
              gzipSize: true,
              brotliSize: true,
              filename: 'dist/stats.html',
            }),
          ]
        : []),
      lazyImport({
        resolvers: [
          VxeResolver({
            libraryName: 'vxe-pc-ui',
          }),
          VxeResolver({
            libraryName: 'vxe-table',
          }),
        ],
      }),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    build: {
      outDir: isBuildLib ? './lib' : './docs',
      minify: 'esbuild',
      target: 'es2018', // 设置构建目标为 ES2018
      cssCodeSplit: false,
      cssMinify: true,
      rollupOptions: isBuildLib
        ? {
            external: ['vue', 'element-eoss', 'lodash', 'uuid'],
            output: {
              globals: {
                vue: 'Vue',
                'element-eoss': 'ElementEoss',
                lodash: '_',
                uuid: 'uuid',
              },
            },
            treeshake: {
              moduleSideEffects: false,
              propertyReadSideEffects: false,
              tryCatchDeoptimization: false,
            },
          }
        : {
            output: {
              manualChunks: {
                vxeTable: ['vxe-table'],
                vxeUI: ['vxe-pc-ui'],
                elementUI: ['element-eoss'],
                vueCore: ['vue'],
                vueRouter: ['vue-router'],
                utils: ['lodash', 'dayjs', 'uuid'],
              },
              chunkFileNames: 'chunks/[name]-[hash].js',
            },
          },
      esbuild: {
        target: 'es2018', // 设置 esbuild 目标为 ES2018
        drop: ['console', 'debugger'], // 移除调试和console
      },
      ...(isBuildLib
        ? {
            lib: {
              entry: './packages/index.js',
              name: 'scic-element-components',
              formats: ['es'],
            },
          }
        : {}),
    },
    define: {
      'process.env': JSON.stringify({}),
    },
  };

  return baseConfig;
});
