## [1.0.27](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.26...v1.0.27) (2025-08-13)

### Bug Fixes

- 🐛 下拉树复选场景默认选中值问题修复 ([fe71de7](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/fe71de70fb9d460a04007c81867cbb9bb7c4bf71))
- 🐛 修复表单部分初始值错误的bug。修复search更多条件时样式错误。 ([13b8063](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/13b8063038e63ccc4629ef9a2e65a573ad454e4e))

### Features

- ✨ 按钮支持指令 ([ee9d692](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/ee9d692996e3500517a1880c963ff61a27126362))
- ✨ 日期区间选择时，允许值时间携带自定义，修复searchTable组件重置的bug。 ([a69f8c1](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/a69f8c1f193b5f1401fa45c1542aba6a0d220082))
- ✨ cascader级联组件支持任意层级节点选中时，输出选中节点value。 ([ede00b0](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/ede00b0464eed5d05fb65dbe95a5732d667ebfdd))
- ✨ date组件支持自定义，比如至今、长期有效等 ([4dac1d8](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/4dac1d83d1193ad860cb129196068c059d63698c))
- ✨ table相关组件支持个性化列，并调整相关交互样式。 ([ca9ae3b](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/ca9ae3ba3a4f506fafed069709ca0d9bb5dda448))
- ✨ upload组件coos模式支持list-type，selectTree组件label显示优化。 ([44c244b](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/44c244ba65244b06e27d352b10f272c823416a31))

## [1.0.26](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.25...v1.0.26) (2025-08-07)

### Bug Fixes

- 🐛 修复搜索重置参数错误的bug。 ([d7162e3](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/d7162e3be0594b69d6e8b26f150cb9cc0bc6998a))
- 🐛 修复search重置后，无法正常输入的bug ([e6f3a04](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/e6f3a0422d505d01a61f0346c7776e1eac4840c8))
- 🐛 优化search布局样式。修复placeholder的bug ([a54403f](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/a54403fb038e1a44043499de8367110af9a0ebe2))

### Features

- ✨ 表单的title组件支持tips ([5e17b22](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/5e17b22683b018c7f628d471ea2749871a3ddf38))
- ✨ 可编辑表格的文本展示和输入框内容展示的tooltips均支持\n换行符。 ([b2a9687](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/b2a968757793b3e87e43d88bdc0458212b2077c5))
- ✨ 优化表单、表格代码，修复一些潜在问题。 ([db5c8c9](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/db5c8c90594913434beddb0641f8a28125978013))
- ✨ form、search相关组件支持默认值。修复一些bug ([c9ed03a](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/c9ed03a5c4f7142d7e07b88738129c35d83bfaee))

## [1.0.25](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.24...v1.0.25) (2025-08-04)

### Bug Fixes

- 🐛 同步表单selectTree默认值 ([7ca16c2](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/7ca16c27582d8c71aa2e81b32480bddff7f227fa))
- 🐛 修复上传组件默认模式，fileList未更新的bug ([a453ddf](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/a453ddfca9222fdee8ebc6bf3e72c9bfdd9f322d))
- 🐛 修复inputNumber组件传入negative:true不能输入负数的bug ([469a9a2](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/469a9a27cc536476956123b59a13d1e9712f4d92))
- 🐛 修复placeholderbug，优化dialog中loading ([999c1df](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/999c1df12214224acf40ab10f490259e1551f580))

### Features

- ✨ selectTree组件支持指定类型节点选择，支持传入valueLabel。 ([5f7b1f0](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/5f7b1f0ecd50d465047903aaa704722cfe8865bc))

## [1.0.24](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.23...v1.0.24) (2025-07-29)

### Bug Fixes

- 🐛 修复分页组件切换size无效的bug ([67e0260](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/67e0260aad1606a1b024e47075f59885eb967182))

### Features

- ✨ 表单相关、可编辑表格配合date组件更新至今场景的校验方法。 ([28944df](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/28944dfaf601b80396ec09741c52c14887eb927c))
- ✨ 时间选择器支持至今选择 ([aa92ee8](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/aa92ee822b4b949856084f537feec5170a3cac88))

## [1.0.23](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.22...v1.0.23) (2025-07-28)

### Bug Fixes

- 🐛 修复表单相关下拉树组件初始化数据错误的bug ([b38591d](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/b38591d842a5e081695e2aa48eef047cd5e194bc))
- 🐛 修复时间选择器区间选择时，重置数据无效的bug ([6d4a6da](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/6d4a6da5ac684c888116fb0168db117e224f8e76))
- 🐛 修复upload组件因文件后缀大小写导致无法通过的bug ([980a50f](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/980a50fd32b777442968e2a6d66b687ed2ce65c6))

### Features

- ✨ form组件新增init方法进行初始化数据 ([4472d2c](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/4472d2c987b5f0c2a6d70253491b9e0ff769bf61))

## [1.0.22](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.21...v1.0.22) (2025-07-24)

### Bug Fixes

- 🐛 修复search组件响应式的bug ([4bd8b21](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/4bd8b21d7e6176d756b9a844715d1c036a6ba2f2))

### Features

- ✨ 让搜索组件根据浏览器宽度来展示每行的搜索项个数 ([b634585](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/b6345855d31ab3353cbcbc5cdea6102fbe7c0ad9))

## [1.0.21](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.20...v1.0.21) (2025-07-23)

### Features

- ✨ dialog组件新增loading配置。修复select值为空或0时选中数据有误的bug。 ([176d665](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/176d665a3e3a1f5d5dad0b7bfe097e93c96bd412))

## [1.0.20](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.19...v1.0.20) (2025-07-23)

### Bug Fixes

- 🐛 修复上传组件coos模式成功回调未处理异常的问题 ([75ba784](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/75ba784f430b5d6210acb7b135959f6982b63695))
- 🐛 修复下拉组件等icon未垂直居中的bug ([1a4277a](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/1a4277ad0618bc83fe1432ba3cf5c1a2a4bc4192))

## [1.0.19](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.18...v1.0.19) (2025-07-21)

### Bug Fixes

- 🐛 设置dialog弹窗中滚动条样式。 ([8ed9769](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/8ed9769d6221efa187d1acd21d6337a1ce0bd68e))
- 🐛 修复form、formItems、input等表单元素在禁用状态表现不一致的bug ([4307056](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/43070568b9ea97509a130a901a0434c37166e2a1))

## [1.0.18](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.17...v1.0.18) (2025-07-21)

### Bug Fixes

- 🐛 修复cascader组件样式问题 ([7c6ac3e](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/7c6ac3eae06c024c932a01eb56306cdda1bc212a))
- 🐛 修复editTable拖拽的bug ([28d03a8](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/28d03a8e926cc3a18051cbd0dfdf2747ceb89a35))
- 🐛 修复table组件h方法报错的问题 ([a6992e3](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/a6992e325a1129bcb332f55d22009e628683a36e))

### Features

- ✨ custom组件新增valueType支持。修复editTable、custom、form的bug及样式优化。 ([6020089](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/60200890c97c5b33c4f16be5fa09b587089ea004))

## [1.0.17](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.16...v1.0.17) (2025-07-17)

### Bug Fixes

- 🐛 修复button组件参数传递的bug。editTable组件新增分页模式，并支持表格仅行编辑。 ([f87d3b6](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/f87d3b62407df9b5d1398a5cbfc3acccaca78f94))

## [1.0.16](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.15...v1.0.16) (2025-07-17)

### Features

- ✨ 表格组件新增自定义滚动条样式。 ([9bc438b](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/9bc438bc50770fd470f329661a5e9008d8195604))
- ✨ 修改dialog组件为垂直居中 ([cde14f0](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/cde14f014bcbeddb1f15bb072a246f7a843a603f))

## [1.0.15](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.14...v1.0.15) (2025-07-15)

## [1.0.14](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.13...v1.0.14) (2025-07-14)

### Bug Fixes

- 🐛 修复表单form组件在某些特殊用法场景显示为0的bug。 ([016ef44](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/016ef44e5f0f7f94f7c4092c6c59ef1492f651dd))

## [1.0.13](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.12...v1.0.13) (2025-07-14)

### Bug Fixes

- 🐛 调整搜索、搜索表格组件排版，调整搜索项为居左。 ([3e33e98](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/3e33e98883d0a4b414a8aecc99d01818d5c57e95))
- 🐛 修复可编辑表格placeholder在查看模式仍然显示的bug。 ([f0359c2](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/f0359c20d6dc8145363abf6751c322de2338bb31))

## [1.0.12](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.11...v1.0.12) (2025-07-11)

### Bug Fixes

- 🐛 修复inputNumber、inputNumberRange、form、formItems等初始化数据的bug ([74d0bdb](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/74d0bdb5c9cd9832dbc500782c8cb71a028ae725))

## [1.0.11](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.10...v1.0.11) (2025-07-11)

## [1.0.10](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.9...v1.0.10) (2025-07-11)

## [1.0.9](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.8...v1.0.9) (2025-07-11)

### Features

- ✨ 优化vxe相关安装方式 ([a21face](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/a21face07b43886ba69e6d55062548e709f795e2))

## [1.0.8](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.7...v1.0.8) (2025-07-11)

## [1.0.7](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.6...v1.0.7) (2025-07-11)

### Features

- ✨ 全局移除??语法 ([9ff745d](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/9ff745daa07130e310b881da3b984e5745b44dcc))

## [1.0.6](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.5...v1.0.6) (2025-07-11)

### Features

- ✨ 对??语法进行降级 ([49c486d](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/49c486d555902844a6ed53bfd4b655f98e435647))
- ✨ 构建打包样式 ([a764ba3](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/a764ba36394598290cc6c2ea2c27f54becb8d041))

## [1.0.5](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/v1.0.4...v1.0.5) (2025-07-10)

## [1.0.4](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/1.0.1...v1.0.4) (2025-07-10)

### Bug Fixes

- 🐛 表单修复错误提示显示问题，可编辑表格默认不显示字数限制 ([39ae9fb](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/39ae9fb480b21eab6d6710c0b9f824b7ff2cb949))
- 🐛 删除多余文件并打包 ([4f89549](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/4f89549c0543d4bb40a6fe1ee42a6edf93cb6aaa))
- 🐛 下拉树组件修复懒加载+搜索的bug。 ([3864f41](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/3864f41040002df70b2b6ded7d59158acfab2c96))
- 🐛 修复表单、下拉等组件的校验问题 ([5691868](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/5691868af180f092398d4323c538733b8caeba6b))
- 🐛 修复表单组件在动态宽度场景的显示问题 ([188c68d](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/188c68d950326781b75d4891132df3963ca4123f))
- 🐛 修复表格系组件在调用vxe-table原生方法时，无返回值的bug。 ([d368cd1](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/d368cd1ccd4fc0d5b493a933c7e3dd509424cdc2))
- 🐛 修复上传组件多文件上传的bug ([c8a3e02](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/c8a3e021e8209d1a70ed92ffab510a488d9363c1))
- 🐛 修复下拉树组件本地搜索和远程搜索功能的bug，修正示例。 ([b31d751](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/b31d7516138944b9d89ae0c02fa69364fe5e0b49))
- 🐛 修复dialog组件reset内部表单的bug ([3edf731](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/3edf7319f947ecd715b05a0905cb166b6813d526))
- 🐛 修复pagination、searchTable、table组件分页相关的bug ([6c8b97f](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/6c8b97f23ac69c24f80cea09ce46a2422e434005))
- 🐛 优化表单样式，修复一些样式bug ([7431526](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/7431526ce6911bd01b07ceb3ec9bb75a5bfd0151))

### Features

- ✨ 备份中 ([6a7b4ca](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/6a7b4ca55daa3eb871d2477d798a73aaf605c510))
- ✨ 表单、搜索、搜索表单等组件规范化备份 ([8c0caa7](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/8c0caa7d4c606114ea14038e53874786b6b134a3))
- ✨ 调整编辑表格的组件方法名，去除原方法名前的on- ([b8751c5](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/b8751c58068c2cccf6dd38cd0326bf486c96df2b))
- ✨ 搜索、表单组件按照规范更新完成 ([9377f9f](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/9377f9f87424f46f23792e5e3a15ee444f1e5627))
- ✨ 文档构建 ([ac3cc80](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/ac3cc806f6ee56e406a7f1a01c0a5ec5ef635f97))
- ✨ 下拉树扩展，支持仅允许选择叶子节点 ([625c27d](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/625c27db143e8aaab8c6e7b76c83b67c9d221b28))
- ✨ 下拉树组件开发备份 ([6158fd7](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/6158fd79a6be0fbdfd553477665b80a367d8cc65))
- ✨ 下拉树组件懒加载开发 ([7a2b604](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/7a2b604db5cf04862be0e86e179cd767eb5bdc0a))
- ✨ 下拉树组件移除懒加载+过滤场景，仅支持懒加载+搜索 ([e1a3867](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/e1a3867c1e05bed505bf02bf06356ee114cde87d))
- ✨ 下拉树组件优化，修复下拉树组件bug，参数优化 ([c911e47](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/c911e4745e33d870841a75585bc68be4d921adc7))
- ✨ 新增更新日志查看 ([a6948af](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/a6948afd32d5f47cd29199e283252542ff745c3e))
- ✨ 新增inputNumberRange 数字区间组件 ([48f0d8c](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/48f0d8cfbc0d49e56ba39135f3f91cb3f7946231))
- ✨ 修改custom自定义组件，调整部分示例 ([af8a57d](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/af8a57dfaedd5b3b963cf9034155c9fa8cf48933))
- ✨ 优化git-cz，优化edittable ([26a8137](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/26a8137357e5af2d3d4ce6f4d02c861b4155494e))
- 🎸 版本备份 ([984044b](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/984044b27f28e38619d85f47f679b2488d0c212f))
- 🎸 编辑表格组件优化 ([1f98191](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/1f981914d2555eb20640a6c137d96184ec27f8c2))
- 🎸 表单、表格组件优化，表格组件文档示例优化 ([7361d29](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/7361d2900aab799cc64182233264384bfaddd38e))
- 🎸 表格组件优化 ([facf618](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/facf618462e31bd6224b19bb2c5e546b63fe93c0))
- 🎸 打印组件新增 ([fca6469](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/fca646984a9a0803f610724f1866374dfb28a610))
- 🎸 打印组件优化更新 ([a613a90](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/a613a90e9b9d3c0ad36a2a19ba2929244541fa87))
- 🎸 更新empty组件的空图标，与coos保持一致。 ([25d5a33](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/25d5a3341664181c8289edd6e1c5acabe1f97c0f))
- 🎸 上传组件更新 ([7cd798a](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/7cd798a186f20656b784219f9272dcc98f022a54))
- 🎸 上传组件更新，支持coos模式，带预览、下载 ([d4fe5e0](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/d4fe5e09dfa145aa5b1401e128f7a862a0167579))
- 🎸 上传组件取消自定义的文件格式校验 ([6e16a04](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/6e16a04dcfce209de0a30ad9c857470b916a73a1))
- 🎸 上传组件文件预览调整为coos预览。部分优化 ([90db5bc](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/90db5bc5c7c6096812131a2dc564db5fc2cf65ab))
- 🎸 上传组件新增超出限制提示 ([3899d2a](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/3899d2a3e8fd3c8838eed8d40ce1d022af71e340))
- 🎸 修复表单 ([d0eafa0](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/d0eafa026d1335c64dc193dbf873b5a1f534d82b))
- 🎸 修复formItems组件样式bug，更新部分组件示例。 ([53ae011](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/53ae0111570e275058dae452996386926a027fc4))
- 🎸 修改可编辑表格、表单等组件bug及优化。 ([22718c6](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/22718c69aa619e0b70ac2136f3e9818e3f46e17f))
- 🎸 修改上传组件上传文件大小限制及上传文件格式限制 ([87adf47](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/87adf479858cf4cd6f3f8772a477c4ae65d815a7))
- 🎸 移除upload组件coos模式测试代码 ([2b6068c](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/2b6068cd4d87aa384494c4ead5b848717e9f2579))
- 🎸 优化表格、表单组件，修复上传组件bug。 ([74ede47](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/74ede478e3ad920ad1f02545927c9b711967f388))
- 🎸 优化上传组件列表显示 ([4997b73](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/4997b732baef216dcb4523a5e40b8906197ffb76))
- 🎸 优化下表单项组件 ([7d9422e](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/7d9422ede7cbcaa5eab6d434069cd2b995718641))
- 🎸 优化一下组件 ([03d4654](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/03d4654e97ef6cff9fd2483ac5ea2bc3abef29d5))
- 🎸 优化dialog组件交互 ([d315190](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/d315190dc910e536ba1202ae03a14f4b10bdea2d))
- 🎸 重构formItems组件的布局交互 ([019abe9](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/019abe9c99a93aad38f14914a768c0aff7b1b13d))
- 🎸 dialog、form、formItems等组件优化 ([1276448](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/12764481e71d2949397c1f8f4ee2df6f60270f1d))
- 🎸 dialog组件新增高度设置。优化部分组件默认设置 ([26d9da7](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/26d9da78bfec546498cc2c7c8dba479c5a7cd836))
- 🎸 formItems和upload组件优化 ([df8893a](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/df8893a9f1d0677118c163106e952682672d20f6))
- 🎸 searchTable、dialog组件优化 ([7ddf705](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/7ddf705e72f3155297659575b1e09787254f861d))
- **upload:** 添加文件下载和预览的配置支持 ([6231ece](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/6231ececaf79545647f312c91f6f276707d7986d))

## [1.0.1](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/1.0.0...1.0.1) (2025-04-03)

### Features

- 🎸 优化表单中输入类型组件的校验方式，默认支持blur和change ([169e8f7](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/169e8f73f75f23deb732cf2d24ab9470217ec9ac))
- 🎸 dialog组件新增关闭弹窗时，移除表单校验的内置函数。 ([6131894](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/6131894e24987543263fae01d6f1aa3c21f8131f))

# [1.0.0](http://git.wisesoft.net.cn/denglianglin/vite-element-components/compare/9e798f4fc068782dcd7dcffd6f725b3fe3cb22ba...1.0.0) (2025-04-03)

### Bug Fixes

- 🐛 修复可编辑表格编辑状态的bug，修复部分文档错误 ([d6db71b](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/d6db71b5e43d92866cd2356fcb43d3c74661d485))
- 🐛 修复日期组件bug ([3632b4b](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/3632b4bf46a9c31598e846e8d3a65ccbc62a2251))
- 🐛 修复input、searchTable等组件bug ([72a8b5f](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/72a8b5f5f19379f987a2528fbca7b83607f200c6))
- **component:** 调整可编辑表格、表格的样式交互 ([f940466](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/f940466c3933bcef873e06eccc9016d2da5c1b28))
- **component:** 修复表格组件操作按钮显示隐藏bug ([04ec7f5](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/04ec7f575d0411a8f82a4ba747b7dbff2b00233b))

### Features

- 🎸 按钮组件开发 ([4f07c2a](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/4f07c2ab56fe9b567a9d05ea667d6850bcec4577))
- 🎸 按照规范调整组件 ([b0d7800](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/b0d78006a42fd119bf16467652aaa52b6f35eaa2))
- 🎸 表单相关mode全部更改 ([5ab7a2d](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/5ab7a2d264687173718a34e3bbd8e3b38e4a9486))
- 🎸 表单项组件优化，修复组件bug，测试组件兼容性 ([630f90e](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/630f90e9d20ba174d258a1545d2e7690ceae765c))
- 🎸 表单组件、上传组件开发 ([f2ff1d0](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/f2ff1d06b4a186ce68caf0a906dda507c986a1fd))
- 🎸 表格、搜索表格开发 ([02345f0](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/02345f068e91892b0579f6eb5b370475f06cdc2c))
- 🎸 表格组件示例调整 ([b16612f](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/b16612f1d03a66e50642da9aaa50d134179dd59a))
- 🎸 补充input、formItems组件示例 ([e4e9b4f](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/e4e9b4fb327addd9d1ac8786deeb305e05ed3fcc))
- 🎸 部分组件调整 ([bde0cc8](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/bde0cc800c04c7af776d2fa7830ba3ded735506a))
- 🎸 部分组件优化，及文档更新 ([8325247](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/8325247983315d97b09971ba6e6c7fb23bbdd06a))
- 🎸 穿梭框和分页组件开发完成。 ([8445f2d](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/8445f2dc7ee3a47fea267088e5f13c022cc5e479))
- 🎸 调整表单符合coosUI设计规范 ([e996de1](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/e996de1e2a3927f18f778895e01b1b89eb3354f9))
- 🎸 调整表单相关组件 ([f5bfd1d](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/f5bfd1defb848c2c4b5eea81c1c69eb76664e763))
- 🎸 调整表单相关组件formItemConfig层级移除，原所属配置项移至外面一层 ([2929c98](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/2929c983e230f7b1d9084708e79369ac2adc0f06))
- 🎸 调整表格、搜索表格组件，新增表格示例 ([41b379d](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/41b379d02fb84967caf166abd1ba415a0f16f82d))
- 🎸 调整级联、复选框、单选框、自定义、下拉框等组件 ([54df032](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/54df03289c5a29ea723e47589ed08dd40bd35026))
- 🎸 调整组件参数及文档 ([a56e733](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/a56e733f0d127d679800e52edf66ac69f7314337))
- 🎸 调整组件库主题色。使之读取变量。 ([6a64ff4](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/6a64ff4c4c00440e90770fa402aeea340bbbf23f))
- 🎸 更新打包 ([ac204f8](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/ac204f83f3f5c06611ccecd58cd263c3546a580f))
- 🎸 更新打包后文件 ([5686585](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/5686585492573622c5e2a755bba29fcbe2652321))
- 🎸 更新弹窗组件示例 ([ddeab54](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/ddeab54c1a751ed068feb6c42067f2dbd3c55caf))
- 🎸 更新上传组件示例 ([beb63f7](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/beb63f79f4ce337f04d93f28aaf6dd66ace83349))
- 🎸 更新文档首页 ([2d11e11](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/2d11e11d2944ab1642f5961a8415affa7baa5d57))
- 🎸 更新input、inputNumber、radio、pagination、search等文档 ([111719b](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/111719be7f57b03d1dd5eb719b513f7e3bd82ad2))
- 🎸 更新readme ([69f2220](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/69f222017f147a487d4a71184ec92ffad8400511))
- 🎸 可编辑表格、下拉组件调整 ([748a9da](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/748a9da397d73953074abc79b693663d87583cca))
- 🎸 可编辑表格优化，下拉框组件优化 ([25f78e1](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/25f78e1b12eeac15f58c8c7191af02b33e9d9ec5))
- 🎸 可编辑表格支持size。表单相关组件、empty组件调整。 ([756e0fa](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/756e0fa1b422501fcd18915b4cabdf37c9414254))
- 🎸 可编辑表格组件，表格组件、表单组件等调整。 ([5711474](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/5711474b4c31d0c37c65370131ac5a6d0cf2f049))
- 🎸 扩展dialog组件支持操作按钮定义，并完善demo ([de54986](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/de54986af6041792216cb7273a244607b90fe4f0))
- 🎸 扩展table、editTable、searchTable支持筛选 ([64a15f2](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/64a15f2910998613cbb85250779c0a6cc23c0a64))
- 🎸 日期时间选择器开发完成 ([b4ce498](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/b4ce4983fc0250207bfb578d89de2e7af60ea591))
- 🎸 升级vxe-table、vxe-pc-ui、xe-utils为最新版 ([5cb6738](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/5cb67380b4430e4aef88eefc1e781ad360e22838))
- 🎸 提交打包后的文件供下载 ([d1cd8b2](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/d1cd8b2fd5818464125593ab6c13163279b0fce7))
- 🎸 完善剩余组件文档 ([31e35ed](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/31e35ed7c6fd096a6e44665f5e988fb407f71cbd))
- 🎸 完善button、checkbox、datePicker组件示例 ([b95dc2d](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/b95dc2daed19d3d33d077fd8e1dd0093c3fe616d))
- 🎸 完善editTable、form、cascader组件示例 ([206139c](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/206139c2fc00699315e2bf2c74b991a1666f32c7))
- 🎸 新增主题色变更，修改sass变量相关。 ([047c818](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/047c81815f6bc2bca30dfac5b9b7f4e00d371a18))
- 🎸 新增checkbox和custom组件的文档及注释 ([7dac52a](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/7dac52a7d2f7cd4f6539e20f58c561dbcc729c6c))
- 🎸 新增dialog和表格的示例优化及样式调整。 ([4a582d5](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/4a582d522f32c9c0ef6fd744764fa28508515c84))
- 🎸 新增inputNumber、pagination、search、radio组件的示例 ([d11a706](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/d11a706f90e1f816f3a382a36db3c8d8d45fd254))
- 🎸 新增selectTree组件 ([bef65b6](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/bef65b65c5675b0a403d1f3be7e4c2f70775f9d6))
- 🎸 新增table的合并单元格示例，新增toolbar示例 ([355b1b9](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/355b1b9bd539c4c78e1439df3f020f9bb516f7c6))
- 🎸 新增table组件的示例 ([e30b0b0](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/e30b0b08619cfb65675b6aa0fc07e7986d9052c3))
- 🎸 修复按钮及级联组件，并完善文档 ([2e9ffc5](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/2e9ffc5c7cc900900632b56c767b1ad605f9313c))
- 🎸 修复部分bug，添加测试数据 ([a66b08d](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/a66b08dd4f45e5751b08078f082ddcfb33b64703))
- 🎸 修复组件样式布局符合coosUI设计规范 ([8a22a13](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/8a22a136cd66472bff956543404ca36ffd1151bb))
- 🎸 优化表格组件，优化其性能 ([1d34469](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/1d344694a3d570aa2fafb81d2321b8d693ba63d5))
- 🎸 优化表格table组件 ([b3608e3](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/b3608e3d3a2b0035a18ce390df08ed1a0f240ce1))
- 🎸 优化部分组件并打包 ([66293a8](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/66293a8715157b8639202784eb3aee5c420abe5e))
- 🎸 优化代码 ([f6f4a5c](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/f6f4a5c7da43651d57f7829daa811afa49990d5a))
- 🎸 优化调整可编辑表格组件 ([aa6ea4b](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/aa6ea4bd7575e408447cb1cf2ad6f80838bd3e17))
- 🎸 优化调整组件库样式及文档 ([50b78aa](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/50b78aa3b7183ec90e51f9984e9d784974a81912))
- 🎸 优化可编辑表格组件 ([69ed106](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/69ed106cc210719797785e08d21e6c029c719af4))
- 🎸 优化示例，调整打包内容 ([31748ee](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/31748ee6f1ed34c0a77ef4713687f2690341fe62))
- 🎸 优化文档展示 ([7eaa7f0](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/7eaa7f0f5742fe3f4d7649418d5804e623817071))
- 🎸 优化组件库 ([33efb35](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/33efb35cffe8b1d578cf67f57f60e8bbb7bed697))
- 🎸 优化组件库体积。缩小20% ([1571fcf](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/1571fcf4de2f551f203f04032fd5a331a2a995f2))
- 🎸 优化组件库样式及主题色 ([a1a26a1](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/a1a26a1cf9e47b8480d2ca8e6fa8611bf8dc2d2f))
- 🎸 优化search组件及样式 ([e2bbb12](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/e2bbb1267e7bc34222a90f1fcef9abde2a639c38))
- 🎸 重构表单组件参数，未完成，备份 ([72e7618](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/72e7618e7ad4f3265212a2532abe57ff500e4ad2))
- 🎸 重构下拉框组件，以满足自定义options，搜索，分页等场景。 ([8fc04e7](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/8fc04e78163024f3a70971130cc60b8337830bfe))
- 🎸 重构dialog组件。优化表格、表单相关，新增测试用例 ([2f07692](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/2f0769254988074755471f0fd13fa1e4d5d387c0))
- 🎸 重新调整样式，符合coosUI设计规范 ([c47ec3a](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/c47ec3a394a565543892bf497cc8b0c8b2ccea40))
- 🎸 组件更新 ([1f5b046](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/1f5b04691a76468630d3ce1a2de22a0a7e164a08))
- 🎸 组件及文档调整，发布到服务器 ([b7b1841](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/b7b1841aa9b104e91a4fdabe8862a35dd6b4c9f1))
- 🎸 组件库调整备份 ([b073d14](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/b073d1442e49e72f2d3ec37beff3df5c6333b0f2))
- 🎸 组件示例更新及组件优化 ([f044fa1](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/f044fa1c86db3f7c0d46892bf646fe116aebdfb1))
- 🎸 组件文档样式、组件优化 ([430df2e](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/430df2eb32699f3eaac85d536e9b82e1c57c4a5c))
- 🎸 组件优化 ([31c20ad](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/31c20ad7ee8aac94274d5a2b0fe83a4961f7f290))
- 🎸 组件优化 ([a39691b](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/a39691bdccbe1f9dd08f29400558931fb4df5de0))
- 🎸 dialog组件扩展size ([8bf14ee](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/8bf14eeb22a3663a317f4162f0ef95331ffb67db))
- 🎸 editTable表格、表单组件扩展 ([cf9a1ef](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/cf9a1ef30d05718da501f2f32da1e7968babbb7e))
- 🎸 input、inputNumber、radio组件优化完毕 ([28c6043](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/28c604328b41ca919e60bce3fecc198cf6fc2373))
- 🎸 searchTable组件示例新增 ([af76ff6](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/af76ff6f87bf86dc6f68f4abdd2dd14707e82ee4))
- **component:** 初始化组件库 ([9e798f4](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/9e798f4fc068782dcd7dcffd6f725b3fe3cb22ba))
- **component:** 更新表单组件及表格组件符合coosUI标准 ([ffd239d](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/ffd239de446632e7a31b24982ba005919183c8ff))
- **component:** 更新参数 ([7983607](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/7983607c295d33ef6e8c665e96c669c349ec685d))
- **component:** 更新可编辑表格组件交互 ([68ca68b](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/68ca68b5cf201dfa1100fe31e533a8454de1b872))
- **component:** 更新组件样式 ([f5f696b](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/f5f696b5c9814212d41311e8f55d981a78c571b1))
- **component:** 修复部分bug ([da36abe](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/da36abe011937369d3e1affb8e341b30f1fc2ce6))
- **component:** 优化editTable组件、扩展formItems组件支持title项；更新组件示例 ([0cab435](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/0cab435ed60acd5c398ad446076285aedd65b6f9))
- **component:** 组件库样式优化 ([b06e57e](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/b06e57edf7f5e4066017c37fc074e15c6e9f61f7))
- **component:** form、formItems、dialog、empty等组件文档更新 ([786b565](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/786b565fc3df48cd5eddd0588941f1901a44d23d))
- **component:** form、formItems支持标题设置 ([91fc710](http://git.wisesoft.net.cn/denglianglin/vite-element-components/commits/91fc71023d40293a98d5e2082ed3a9c7dde62cf7))
