<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-24 10:55:07
 * @FilePath: /vite-element-components/packages/search/doc/custom-controls.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <x-search
      v-model="searchDefault"
      :search-config="searchFormConfig"
      :search-controls="customControls"
      @updateParams="handleSearchBtnClick"
      @resetSearch="handleResetClick"
      @exportData="handleExportClick"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchDefault: {
        keyword: '',
        dateRange: [],
        category: '',
      },
      customControls: [
        {
          label: '导出数据',
          type: 'primary',
          onClick: (e) => {
            console.log('%c [  ]-37-「custom-controls」', 'font-size:13px; background:pink; color:#bf2c9f;', e);
          },
          icon: 'el-icon-download',
        },
      ],
    };
  },
  computed: {
    searchFormConfig() {
      return [
        {
          prop: 'keyword',
          label: '关键词',
          component: 'input',
          formInputConfig: {
            placeholder: '请输入搜索关键词',
          },
        },
        {
          prop: 'category',
          label: '数据分类',
          component: 'select',
          formInputConfig: {
            placeholder: '请选择分类',
            options: [
              { label: '类型A', value: 'A' },
              { label: '类型B', value: 'B' },
              { label: '类型C', value: 'C' },
            ],
          },
        },
        {
          prop: 'dateRange',
          label: '时间范围',
          component: 'date',
          formInputConfig: {
            type: 'daterange',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
          },
        },
      ];
    },
  },
  methods: {
    handleSearchBtnClick(params) {
      console.log('搜索参数：', params);
    },
    handleResetClick(params) {
      console.log('重置参数：', params);
    },
    handleExportClick() {
      console.log('触发导出操作');
    },
  },
};
</script>
