<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-10 10:06:02
 * @FilePath: /vite-element-components/packages/search/doc/dynamic.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <x-search v-model="searchDefault" :search-config="searchFormConfig" @updateParams="handleSearchBtnClick" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchDefault: {
        industry: '',
        subIndustry: '',
        scale: '',
      },
      industryOptions: [
        {
          label: '互联网',
          value: 'internet',
          children: [
            { label: '电商', value: 'ecommerce' },
            { label: '社交', value: 'social' },
            { label: '游戏', value: 'game' },
          ],
        },
        {
          label: '金融',
          value: 'finance',
          children: [
            { label: '银行', value: 'bank' },
            { label: '保险', value: 'insurance' },
            { label: '证券', value: 'securities' },
          ],
        },
      ],
      currentSubIndustryOptions: [],
    };
  },
  computed: {
    searchFormConfig() {
      return [
        {
          prop: 'industry',
          label: '所属行业',
          component: 'select',
          formInputConfig: {
            placeholder: '请选择行业',
            options: this.industryOptions,
          },
          events: {
            change: this.handleIndustryChange,
          },
        },
        {
          prop: 'subIndustry',
          label: '子行业',
          component: 'select',
          formInputConfig: {
            placeholder: '请选择子行业',
            options: this.currentSubIndustryOptions,
            disabled: !this.searchDefault.industry,
          },
        },
        {
          prop: 'scale',
          label: '企业规模',
          component: 'select',
          formInputConfig: {
            placeholder: '请选择规模',
            options: [
              { label: '0-50人', value: 'small' },
              { label: '50-200人', value: 'medium' },
              { label: '200人以上', value: 'large' },
            ],
          },
        },
      ];
    },
  },
  methods: {
    handleIndustryChange(value) {
      this.searchDefault.subIndustry = '';
      const industry = this.industryOptions.find((item) => item.value === value);
      this.currentSubIndustryOptions = industry ? industry.children : [];
    },
    handleSearchBtnClick(params) {
      console.log('搜索参数：', params);
    },
  },
};
</script>
