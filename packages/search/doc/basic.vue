<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 13:50:09
 * @FilePath: /vite-element-components/packages/search/doc/basic.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <div style="margin-bottom: 20px; padding: 10px; background: #f5f5f5; border-radius: 4px">
      <p><strong>响应式布局测试说明：</strong></p>
      <p>当前窗口宽度: {{ windowWidth }}px</p>
      <p>预期每行显示: {{ getExpectedItemsPerRow() }}个搜索项</p>
      <p>
        <label>
          <input type="checkbox" v-model="hideMoreButton" @change="handleShowMoreChange" />
          隐藏展开/收起按钮（showMore={{ !hideMoreButton }}）
        </label>
      </p>
      <p>请调整浏览器窗口大小来测试响应式效果</p>
    </div>

    <x-search
      :search-config="searchFormConfig"
      :show-more="hideMoreButton"
      @updateParams="(params) => handleSearchBtnClick(params)"
      @resetSearch="handleResetSearch"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      windowWidth: window.innerWidth,
      hideMoreButton: false, // 控制是否隐藏更多按钮
    };
  },

  mounted() {
    // 监听窗口大小变化
    this.handleResize = () => {
      this.windowWidth = window.innerWidth;
    };
    window.addEventListener('resize', this.handleResize);
  },

  beforeDestroy() {
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }
  },
  computed: {
    // 弹窗事件
    searchFormConfig() {
      return [
        {
          prop: 'name',
          label: '关键词搜索',
          component: 'input',
          isShow: false,
          formInputConfig: {
            placeholder: '搜索手机号码/url链接/来源',
          },
          events: {
            'keyup.enter': (val) => {
              console.log('%c [  ]-49-「Search」', 'font-size:13px; background:pink; color:#bf2c9f;', val);
            },
          },
        },
        {
          prop: 'name1',
          label: '搜索项1',
          component: 'input',
          formInputConfig: {
            placeholder: '请输入搜索项1',
          },
        },
        {
          prop: 'name2',
          label: '搜索项2',
          component: 'input',
          formInputConfig: {
            placeholder: '请输入搜索项2',
          },
        },
        {
          prop: 'name3',
          label: '搜索项3',
          component: 'input',
          formInputConfig: {
            placeholder: '请输入搜索项3',
          },
        },
        {
          prop: 'name4',
          label: '搜索项4',
          component: 'input',
          formInputConfig: {
            placeholder: '请输入搜索项4',
          },
        },
        {
          prop: 'name5',
          label: '搜索项5',
          component: 'input',
          formInputConfig: {
            placeholder: '请输入搜索项5',
          },
        },
        {
          prop: 'name6',
          label: '搜索项6',
          component: 'input',
          formInputConfig: {
            placeholder: '请输入搜索项6',
          },
        },
        {
          label: '日期时间区间',
          prop: 'opportunityTime',
          component: 'date',
          formInputConfig: {
            type: 'datetimerange',
            'start-placeholder': '开始时间',
            'end-placeholder': '结束时间',
          },
        },
        {
          label: '普通时间',
          prop: 'date',
          component: 'date',
          formInputConfig: {
            type: 'date',
          },
        },
      ];
    },
  },
  methods: {
    /**
     * 触发搜索事件
     * @param {*} query
     */
    handleSearchBtnClick(query) {
      console.log('%c [搜索]-「Search」', 'font-size:13px; background:pink; color:#bf2c9f;', query);
    },

    handleResetSearch(data) {
      console.log('%c [重置]-「Search」', 'font-size:13px; background:orange; color:#ff6600;', data);
    },

    /**
     * 获取预期每行显示的搜索项数量
     */
    getExpectedItemsPerRow() {
      if (this.windowWidth >= 1920) {
        return 4;
      }
      return 3;
    },

    /**
     * 处理showMore属性变化
     */
    handleShowMoreChange() {
      console.log('showMore changed to:', this.hideMoreButton);
    },
  },
};
</script>
