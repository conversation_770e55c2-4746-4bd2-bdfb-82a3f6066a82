<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-04 16:04:21
 * @FilePath: /vite-element-components/packages/search/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import AdvanceVue from './advance.vue';
  import LayoutVue from './layout.vue';
  import CustomControlsVue from './custom-controls.vue';
  import DynamicVue from './dynamic.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      AdvanceVue,
      LayoutVue,
      CustomControlsVue,
      DynamicVue,
      Preview
    }
  };
</script>

## Search 组件

基于 Element UI 的搜索组件，支持表单项的动态配置、展开收起和响应式布局等功能。主要用于列表页面的搜索条件区域，可以根据配置自动生成表单项，支持展开收起功能。

### 特性

- **响应式布局**：根据浏览器窗口宽度自动调整每行显示的搜索项数量
- **动态配置**：支持通过配置数组动态生成表单项
- **展开收起**：当搜索项较多时，支持展开收起功能
- **自定义布局**：支持自定义标签位置、宽度等布局属性
- **事件处理**：支持回车搜索、自定义事件等交互功能

### 基础用法

基础的搜索组件用法，支持响应式布局。组件会根据浏览器窗口宽度自动调整每行显示的搜索项数量：

- 窗口宽度 ≥ 1920px：每行显示 4 个搜索项
- 窗口宽度 < 1920px：每行显示 3 个搜索项

<basic-vue/>
<preview  comp-name='search' demo-name='basic'/>

### 高级用法

<advance-vue/>
<preview  comp-name='search' demo-name='advance'/>

### 自定义布局

通过设置 `search-item-span`、`label-position` 和 `label-width` 属性来自定义搜索表单的布局。

**注意**：当明确设置了 `search-item-span` 属性时，将优先使用该值，不会启用响应式布局。如果希望使用响应式布局，请不要设置此属性或将其设置为默认值 3。

<layout-vue/>
<preview  comp-name='search' demo-name='layout'/>

### 自定义操作按钮

通过 `search-controls` 属性可以添加自定义的操作按钮，并通过事件处理按钮的点击行为。

<custom-controls-vue/>
<preview  comp-name='search' demo-name='custom-controls'/>

### 动态表单项

展示如何实现表单项之间的联动效果，比如根据上级选项动态加载下级选项。

<dynamic-vue/>
<preview  comp-name='search' demo-name='dynamic'/>

### Attributes

| 参数             | 说明                                                                                    | 类型          | 可选值         | 默认值  |
| ---------------- | --------------------------------------------------------------------------------------- | ------------- | -------------- | ------- |
| searchConfig     | 搜索表单配置，见 x-form-items 的表单配置                                                | array         | —              | []      |
| value/v-model    | 表单数据对象                                                                            | object        | —              | {}      |
| labelWidth       | 表单域标签的宽度，例如 '80px'。作为 Form 直接子元素的 form-item 会继承该值。支持 auto。 | string        | —              | '80px'  |
| showMore         | 是否隐藏展开/收起按钮                                                                   | boolean       | true/false     | true    |
| minShowRowNumber | 搜索条件最小显示行数                                                                    | number        | —              | 1       |
| labelPosition    | label 显示位置                                                                          | string        | right/left/top | 'right' |
| searchItemSpan   | 每行显示的搜索项数量，设置后将禁用响应式布局                                            | string/number | —              | 3       |
| size             | 组件尺寸                                                                                | string        | —              | 'small' |

### Events

| 事件名       | 说明               | 回调参数                                    |
| ------------ | ------------------ | ------------------------------------------- |
| updateParams | 点击查询按钮时触发 | formData: object (表单数据对象)             |
| resetSearch  | 点击重置按钮时触发 | { formData: object } (重置后的表单数据对象) |

### Methods

| 方法名            | 说明         | 参数 |
| ----------------- | ------------ | ---- |
| handleSearch      | 触发查询操作 | —    |
| handleSearchReset | 重置搜索条件 | —    |

### Slots

| 插槽名     | 说明                                                         |
| ---------- | ------------------------------------------------------------ |
| handle-btn | 自定义操作按钮区域的内容，可用于添加额外的按钮或其他操作元素 |

### 注意事项

1. **searchConfig 配置项说明：**

   - 每个表单项必须包含 prop、label 和 component 属性
   - component 属性支持 input、select、datePicker 等 Element UI 表单组件
   - 可通过 formInputConfig 配置表单组件的具体属性
   - 可通过 events 配置表单项的事件处理函数
   - 可通过 isShow 或 isShowForm 控制表单项的显示隐藏

2. **响应式布局说明：**

   - **自动响应**：组件默认启用响应式布局，根据浏览器窗口宽度自动调整
   - **断点规则**：
     - 窗口宽度 ≥ 1920px：每行显示 4 个搜索项
     - 窗口宽度 < 1920px：每行显示 3 个搜索项
   - **禁用响应式**：当明确设置 `searchItemSpan` 属性时，将使用固定值，不再响应窗口大小变化
   - **按钮位置**：搜索按钮的位置也会根据搜索项数量和窗口宽度自动调整

3. **布局相关：**

   - 当操作按钮区域的栅格宽度小于 6 时，会自动换行
   - 可通过 searchItemSpan 控制每行显示的表单项数量（会禁用响应式布局）
   - 可通过 minShowRowNumber 控制默认显示的行数
   - 展开收起功能会根据当前的搜索项数量和每行显示数量自动计算

4. **性能优化：**

   - 组件会自动监听窗口大小变化，并在组件销毁时清理事件监听器
   - 响应式计算使用 Vue 的计算属性进行缓存，避免不必要的重复计算
