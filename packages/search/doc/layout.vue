<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-25 10:45:47
 * @FilePath: /vite-element-components/packages/search/doc/layout.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <x-search
      v-model="searchDefault"
      :search-config="searchFormConfig"
      :search-item-span="3"
      @updateParams="(params) => handleSearchBtnClick(params)"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchDefault: {
        name: '',
        status: '',
        type: '',
        date: [],
      },
    };
  },
  computed: {
    searchFormConfig() {
      return [
        {
          prop: 'name',
          label: '项目名称',
          component: 'input',
          labelWidth: '120px',
          formInputConfig: {
            placeholder: '请输入项目名称',
          },
        },
        {
          prop: 'status',
          label: '项目状态',
          component: 'select',
          formInputConfig: {
            placeholder: '请选择状态',
            options: [
              { label: '进行中', value: 'processing' },
              { label: '已完成', value: 'completed' },
              { label: '已暂停', value: 'paused' },
            ],
          },
        },
        {
          prop: 'type',
          label: '项目类型',
          component: 'select',
          formInputConfig: {
            placeholder: '请选择类型',
            options: [
              { label: '研发项目', value: 'dev' },
              { label: '设计项目', value: 'design' },
              { label: '运维项目', value: 'ops' },
            ],
          },
        },
        {
          label: '创建日期',
          prop: 'date',
          component: 'date',
          formInputConfig: {
            type: 'daterange',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
          },
        },
      ];
    },
  },
  methods: {
    handleSearchBtnClick(params) {
      console.log('搜索参数：', params);
    },
  },
};
</script>
