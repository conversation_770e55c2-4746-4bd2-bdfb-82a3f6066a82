<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-07 13:56:07
 * @FilePath: /vite-element-components/packages/search/doc/advance.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <x-search
      v-model="searchDefault"
      :min-show-row-number="1"
      :search-config="searchFormConfig"
      @updateParams="(params) => handleSearchBtnClick(params)"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchDefault: {
        name1: '默认值',
      },
    };
  },
  computed: {
    // 弹窗事件
    searchFormConfig() {
      return [
        {
          prop: 'name',
          label: '关键词搜索',
          component: 'input',
          formInputConfig: {
            placeholder: '搜索手机号码/url链接/来源',
          },
          events: {
            'keyup.enter.native': (val) => {
              console.log('%c [  ]-49-「Search」', 'font-size:13px; background:pink; color:#bf2c9f;', val);
            },
          },
        },
        {
          prop: 'name1',
          label: '搜索1',
          component: 'input',
          formInputConfig: {},
        },
        {
          prop: 'name11',
          label: '搜索11',
          component: 'input',
          formInputConfig: {},
        },
        {
          prop: 'name3',
          label: '搜索3',
          component: 'input',
          formInputConfig: {},
        },
        {
          prop: 'name4',
          label: '搜索4',
          component: 'input',
          formInputConfig: {},
        },
        {
          prop: 'name45',
          label: '搜索45',
          component: 'input',
          formInputConfig: {},
        },
        {
          label: '日期时间区间',
          prop: 'opportunityTime',
          component: 'date',
          formInputConfig: {
            type: 'datetimerange',
            'start-placeholder': '开始时间',
            'end-placeholder': '结束时间',
          },
        },
        {
          label: '普通时间',
          prop: 'date',
          component: 'date',
          formInputConfig: {
            type: 'date',
          },
        },
      ];
    },
  },
  methods: {
    /**
     * 触发搜索事件
     * @param {*} query
     */
    handleSearchBtnClick(query) {
      console.log('%c [  ]-62-「Search」', 'font-size:13px; background:pink; color:#bf2c9f;', query);
    },
  },
};
</script>
