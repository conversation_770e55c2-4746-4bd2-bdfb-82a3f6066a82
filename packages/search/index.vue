<template>
  <div class="x-search">
    <x-form
      ref="searchForm"
      class="x-search__form"
      inline-message
      v-model="formData"
      :label-width="labelWidth"
      :show-message="false"
      v-bind="$attrs"
      @submit.native.prevent
    >
      <x-form-items
        slot="formAppend"
        v-model="formData"
        form-type="search"
        class="x-search__form-items"
        size="medium"
        :label-width="labelWidth"
        :form-item-span="searchItems.length >= currentSearchItemSpan ? 24 / currentSearchItemSpan : 6"
        :form-config="searchItems"
      >
        <div slot="searchButton" class="x-search-handle">
          <div
            class="x-search-handle-button"
            :class="{
              'is-active': expanded,
              'no-active': !expanded,
            }"
          >
            <x-button
              :class="{
                'mgr-8': !expanded,
              }"
              label="查询"
              type="primary"
              icon="el-icon-search"
              @click="handleSearch"
            />
            <x-button
              :class="{
                'mgt-8': expanded,
              }"
              label="重置"
              icon="el-icon-refresh-right"
              @click="handleSearchReset"
            />
          </div>
          <el-button
            v-if="isShowMore"
            type="text"
            class="x-search-handle-more"
            :class="{ folded: !expanded }"
            @click="handleMoreBtn"
          >
            {{ expanded ? '收起' : '展开' }}
            <i :class="{ 'el-icon-arrow-down': !expanded, 'el-icon-arrow-up': expanded }"></i>
          </el-button>
        </div>
      </x-form-items>
    </x-form>
  </div>
</template>

<script>
import XFormItems from '../formItems/index.vue';
import XButton from '../button/index.vue';
import { getInitialValueByComponent } from '../shared/utils';

/**
 * @description 搜索组件，基于Element UI的Form组件进行封装，支持表单项的动态配置、展开收起和布局适配等功能
 * @module XSearch
 */
export default {
  name: 'XSearch',
  components: { XFormItems, XButton },
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    /**
     * @description 搜索条件最小显示行数
     * @property {Number} minShowRowNumber
     * @default 1
     */
    minShowRowNumber: {
      type: Number,
      default: 1,
    },
    /**
     * @description 搜索表单配置数组，用于动态生成表单项
     * @property {Array} searchConfig
     * @default []
     */
    searchConfig: {
      type: Array,
      default: () => [],
    },
    /**
     * @description 是否隐藏展开/收起按钮
     * 当设置为true时，将隐藏"展开/收起"按钮，所有搜索项都会显示
     * @property {Boolean} showMore
     * @default false
     */
    showMore: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 搜索条件默认值
     * @property {Object} value
     */
    value: {
      type: Object,
      default: undefined,
    },
    /**
     * @description 表单域标签的宽度，支持auto
     * @property {String} labelWidth
     * @default '80px'
     */
    labelWidth: {
      type: String,
      default: '80px',
    },
    /**
     * @description 每行显示的搜索项数量
     * @property {String|Number} searchItemSpan
     * @default 3
     */
    searchItemSpan: {
      type: [String, Number],
      default: 3,
      validator(value) {
        const num = Number(value);
        return num > 0 && num <= 24;
      },
    },
  },
  data() {
    return {
      expanded: false,
      windowWidth: window.innerWidth, // 当前窗口宽度
      innerFormData: {}, // 新增，支持非受控
      initialUserValues: {}, // 记录用户传入的初始默认值
    };
  },
  computed: {
    /**
     * 响应式搜索项数量
     * 根据浏览器宽度动态计算每行显示的搜索项数量
     * 1920及以上显示4个，1920以下显示3个
     */
    responsiveSearchItemSpan() {
      return this.windowWidth >= 1920 ? 4 : 3;
    },

    /**
     * 当前使用的搜索项数量
     * 优先使用传入的searchItemSpan，如果没有则使用响应式计算的值
     */
    currentSearchItemSpan() {
      // 如果用户明确设置了searchItemSpan，则使用用户设置的值
      if (this.searchItemSpan !== 3) {
        return this.searchItemSpan;
      }
      // 否则使用响应式计算的值
      return this.responsiveSearchItemSpan;
    },

    // Form组件的配置，也就是每一个formItem的配置
    searchItems: {
      get() {
        return this.searchConfig
          .filter((item) => {
            if ('isShow' in item || 'isShowForm' in item) {
              return item.isShow || item.isShowForm;
            }
            return item;
          })
          .map((item) => {
            // 只给输入框相关的组件添加回车事件
            const inputComponents = ['input', 'inputNumber'];
            const { component, formInputConfig, events } = item;
            if (inputComponents.includes(component)) {
              return {
                ...item,
                formInputConfig: {
                  maxlength: 1000, // 不限制
                  ...formInputConfig,
                  showWordLimit: false,
                },
                events: {
                  ...events,
                  keyup: (event) => {
                    console.log('keyup event fired', event.key);
                    if (event.key === 'Enter' || event.keyCode === 13) {
                      this.handleSearch();
                    }
                    // 如果原来有keyup事件，也要保留
                    if (events?.keyup) {
                      events.keyup(event);
                    }
                  },
                },
              };
            }
            return item;
          });
      },
      set(val) {
        this.$emit('update:searchConfig', val);
      },
    },
    // 表单数据
    formData: {
      get() {
        // 受控优先
        return this.value !== undefined ? this.value : this.innerFormData;
      },
      set(val) {
        if (this.value !== undefined) {
          this.$emit('update:value', val);
        } else {
          this.innerFormData = val;
        }
      },
    },
    // 是否需要显示更多按钮
    isShowMore() {
      if (this.showMore) return false;
      return this.searchItems.length > this.currentSearchItemSpan * this.minShowRowNumber;
    },
    // 默认需要显示的formItem数量
    formItemShowNumber() {
      const maxShowItems = this.currentSearchItemSpan * this.minShowRowNumber;
      // 如果总搜索项数量小于等于最大显示数量，则显示所有项
      // 否则显示最大显示数量的项
      return Math.min(this.searchItems.length, maxShowItems);
    },
  },
  watch: {
    searchItems() {
      // 重新计算展开和收起
      if (!this.showMore) {
        this.switchItemShow();
      }
    },
  },
  created() {
    // 记录用户传入的初始默认值（用于重置时保持用户的默认值）
    if (this.value !== undefined) {
      this.initialUserValues = { ...this.value };
    }

    // 初始化内部表单数据
    if (this.value === undefined) {
      const initData = {};
      this.searchConfig.forEach((item) => {
        const { component, prop, formInputConfig } = item;
        if (prop) {
          initData[prop] = getInitialValueByComponent(component, formInputConfig);
        }
      });
      this.innerFormData = initData;
    }

    if (!this.showMore) {
      this.switchItemShow();
    }
  },

  mounted() {
    // 添加窗口大小变化监听器
    this.handleResize = () => {
      this.windowWidth = window.innerWidth;
      // 当窗口大小变化时，重新计算展开收起状态
      if (!this.showMore) {
        this.switchItemShow();
      }
    };
    window.addEventListener('resize', this.handleResize);
  },

  beforeDestroy() {
    // 移除窗口大小变化监听器
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }
  },
  methods: {
    /**
     * @description 点击查询按钮时触发
     * @method handleSearch
     * @fires updateParams
     */
    handleSearch() {
      this.$emit('updateParams', this.formData);
    },
    /**
     * @description 重置搜索条件
     * @method handleSearchReset
     * @fires resetSearch
     * @fires updateParams
     */
    handleSearchReset() {
      // 生成重置数据，优先使用用户的初始默认值
      const resetData = {};
      this.searchConfig.forEach((item) => {
        const { component, prop, formInputConfig } = item;
        if (prop) {
          // 优先使用用户传入的初始默认值，如果没有则使用组件默认值
          if (Object.prototype.hasOwnProperty.call(this.initialUserValues, prop)) {
            resetData[prop] = this.initialUserValues[prop];
          } else {
            resetData[prop] = getInitialValueByComponent(component, formInputConfig);
          }
        }
      });
      // 如果没有设置 v-model，直接重置内部状态
      if (this.value === undefined) {
        this.innerFormData = resetData;
      } else {
        // 有 v-model 时，触发更新事件
        this.$emit('update:value', resetData);
      }

      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.searchForm && this.$refs.searchForm.clearValidate) {
          this.$refs.searchForm.clearValidate();
        }
        this.$emit('resetSearch', resetData);
        // this.$emit('updateParams', resetData);
      });
    },
    /**
     * @description 点击展开/收起按钮时触发
     * @method handleMoreBtn
     */
    handleMoreBtn() {
      this.expanded = !this.expanded;
      this.switchItemShow();
    },
    // 切换formItem的展示状态
    switchItemShow() {
      this.searchItems.forEach((item, i) => {
        if (this.showMore) {
          // 如果showMore为true，显示所有搜索项
          item.isShow = true;
          return;
        }

        // 否则根据展开状态和位置决定是否显示
        if (i < this.formItemShowNumber) {
          // 前面的项始终显示
          item.isShow = true;
        } else {
          // 后面的项根据展开状态显示
          item.isShow = this.expanded;
        }
      });
    },
  },
};
</script>

<style lang="scss">
@import '../styles/index.scss';
.x-search {
  background-color: #fff;
  border-radius: 3px;
  overflow: hidden;
  padding: 8px 15px 0px;
  .el-form-item--medium .el-form-item__content {
    line-height: 32px;
  }
  &__form {
    &-items.x-form-items {
      .el-form-item {
        margin-bottom: 0 !important;
      }
      .x-form-item__label {
        line-height: 32px;
      }
      .x-form-item {
        margin-top: 8px !important;
      }
    }
  }

  .el-form__item:last-of-type {
    margin-bottom: 0 !important;
  }
  .el-form-item__content {
    line-height: 1;
  }
  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 12px;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
    &__title {
      font-size: 20px;
      flex-grow: 1;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    &__controls {
      .el-button:not(:first-child) {
        margin-left: 8px;
      }
    }
  }
  &-handle {
    display: flex;
    align-items: flex-start;
    padding-left: 30px;
    padding-top: 8px;
    &-button {
      flex-grow: 1;
      &.is-active {
        white-space: wrap;
      }
      &.no-active {
        white-space: nowrap;
      }
    }
    .el-button + .el-button {
      margin-left: 0 !important;
    }
    &-more {
      margin-left: 48px !important;
    }
  }
}
</style>
<style lang="scss" scoped>
.mgr-8 {
  margin-right: 8px;
}
.mgt-8 {
  margin-top: 8px;
}
</style>
