/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2023-06-08 14:17:31
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-06 14:29:16
 * @FilePath: /vite-element-components/packages/formItems/constant.js
 * @Description: 组件的配置信息，文件路径及表单组件默认校验规则。相对路径是相对 组件AsyncLoadComponent
 */

// 组件路径配置
export const constantComponent = {
  input: 'input', // 输入框
  cascader: `cascader`, // 级联选择器
  select: `select`, // 下拉组件
  selectTree: `selectTree`, // 下拉树选择
  inputNumber: `inputNumber`, // 数字输入框
  inputNumberRange: `inputNumberRange`, // 数字区间输入框
  radio: `radio`, // 单选按钮
  switch: `switch`, // 开关
  date: `datePicker`, // 日期控件
  checkbox: `checkbox`, // 复选框
  text: `text`, // 文本组件
  upload: `upload`, // 上传组件
  custom: `custom`, // 自定义的内容组件
  transfer: `transfer`, // 穿梭框
  title: `title`, // 标题
};

// 配置判空方式和提示文字
const GET_DEFAULT_VALIDATOR = (trigger) => ({
  trigger,
  getMessage: (label) => `请${trigger === 'change' ? '选择' : '输入'}${label}`,
});

// 配置表单组件默认的校验规则
export const constantRulesRequired = {
  input: { ...GET_DEFAULT_VALIDATOR(['blur']) },
  date: { ...GET_DEFAULT_VALIDATOR('change') },
  radio: { ...GET_DEFAULT_VALIDATOR('change') },
  switch: { ...GET_DEFAULT_VALIDATOR('change') },
  select: { ...GET_DEFAULT_VALIDATOR('change') },
  selectTree: { ...GET_DEFAULT_VALIDATOR('change') },
  checkbox: { ...GET_DEFAULT_VALIDATOR('change') },
  inputNumber: { ...GET_DEFAULT_VALIDATOR(['blur']) },
  inputNumberRange: {
    validator: (rule, value, callback, label) => {
      if (Array.isArray(value)) {
        const [min, max] = value;
        if (min === undefined && max === undefined) {
          callback(new Error(`请输入${label}`));
        }
        if (min === undefined) {
          callback(new Error('请输入最小值'));
        }
        if (max === undefined) {
          callback(new Error('请输入最大值'));
        }
      }
      callback();
    },
    trigger: 'blur',
    getMessage: (label) => `请输入${label}`,
  },
  cascader: { ...GET_DEFAULT_VALIDATOR('change') },
  text: { ...GET_DEFAULT_VALIDATOR('change') },
  upload: { trigger: 'change', getMessage: (label) => `请上传${label}` },
  custom: { ...GET_DEFAULT_VALIDATOR('change') },
  transfer: { ...GET_DEFAULT_VALIDATOR('change') },
  title: { ...GET_DEFAULT_VALIDATOR('change') },
};
