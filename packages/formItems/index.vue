<script lang="jsx">
import { v4 as uuidv4 } from 'uuid';
import Vue from 'vue';
import AsyncLoadComponent from './asyncLoadComponent.vue';
import XTooltip from '../tooltip/index.vue';
import { constantComponent, constantRulesRequired } from './constant';
import { getInitialValueByComponent } from '../shared/utils';

import directives from '../shared/directives.js';

directives.forEach(({ key, value }) => {
  Vue.directive(key, value);
});

/**
 * @description 动态表单项组件，支持多种表单控件的动态渲染和数据双向绑定
 * @module XFormItems
 */
export default {
  name: 'XFormItems',
  components: { AsyncLoadComponent, XTooltip },
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    /**
     * @description 表单类型，用于区分不同场景下的表单布局
     * @default 'form'
     */
    formType: {
      type: String,
      default: 'form',
    },
    /**
     * @description 表单标题
     * @default ''
     */
    title: {
      type: String,
      default: '',
    },
    /**
     * @description 表单数据对象，用于双向绑定
     * @default {}
     */
    value: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 表单配置数组，用于动态生成表单项
     * @default []
     */
    formConfig: {
      type: Array,
      default: () => [],
    },
    /**
     * @description 编辑模式
     * @default 'edit'
     */
    mode: {
      type: String,
      default: 'edit',
    },
    /**
     * @description 表单项栅格占比
     * @default 24
     */
    formItemSpan: {
      type: [Number],
      default: 24,
    },
    /**
     * @description 表单域标签的位置
     * @default 'right'
     */
    labelPosition: {
      type: [String],
      default: 'right',
    },
    /**
     * @description 表单域标签的宽度
     * @default '130px'
     */
    labelWidth: {
      type: String,
      default: '130px',
    },
    /**
     * @description 是否隐藏表单项标签
     * @default false
     */
    hideLabel: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 是否禁用整个表单
     * @default false
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 表单组件的尺寸
     * @default ''
     */
    size: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      formId: uuidv4(),
      windowWidth: typeof window !== 'undefined' ? window.innerWidth : 1920, // 当前窗口宽度，SSR兼容
    };
  },
  computed: {
    /**
     * @description 表单数据的计算属性，实现双向绑定
     */
    formData: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('update:value', value);
      },
    },
    /**
     * @description 表单配置源，用于监听配置变化
     */
    formItemSource() {
      return this.formConfig;
    },
    /**
     * @description 响应式搜索项栅格占比
     * 根据浏览器宽度动态计算搜索模式下每个表单项的栅格占比
     * 1920及以上显示4个(span=6)，1920以下显示3个(span=8)
     */
    responsiveSearchItemSpan() {
      return this.windowWidth >= 1920 ? 6 : 8; // 24/4=6, 24/3=8
    },

    /**
     * @description 响应式搜索项每行数量
     * 根据浏览器宽度动态计算搜索模式下每行显示的搜索项数量
     * 1920及以上显示4个，1920以下显示3个
     */
    responsiveSearchItemsPerRow() {
      return this.windowWidth >= 1920 ? 4 : 3;
    },
  },
  created() {
    this.initFormData();
  },

  mounted() {
    // 添加窗口大小变化监听器（仅在搜索模式下）
    if (this.formType === 'search' && typeof window !== 'undefined') {
      this.handleResize = () => {
        this.windowWidth = window.innerWidth;
      };
      window.addEventListener('resize', this.handleResize);
    }
  },

  beforeDestroy() {
    // 移除窗口大小变化监听器
    if (this.handleResize && typeof window !== 'undefined') {
      window.removeEventListener('resize', this.handleResize);
    }
  },
  methods: {
    /**
     * @description 控制表单项是否显示
     * @param {Object} config - 表单项配置
     * @returns {boolean} 是否显示
     */
    formItemIsShow(config) {
      if (!config) return false;
      const isShow = Object.keys(config).includes('isShow');
      if (isShow) return config.isShow;
      return true;
    },
    /**
     * @description 获取表单控件的配置参数
     * @param {Object} config - 表单项配置
     * @returns {Object} 控件配置
     */
    getFormInputConfig(config = {}) {
      if (!config.component || !constantRulesRequired[config.component]) {
        return config.formInputConfig || {};
      }

      // inputNumberRange需要两个placeholder
      if (config.component === 'inputNumberRange') {
        return {
          ...config.formInputConfig,
          placeholder:
            this.mode === 'edit' ? config?.formInputConfig?.placeholder || ['请输入最小值', '请输入最大值'] : [],
        };
      }

      return {
        ...config.formInputConfig,
        placeholder:
          this.mode === 'edit'
            ? config?.formInputConfig?.placeholder ||
              constantRulesRequired[config.component].getMessage(config.label || '')
            : '',
      };
    },
    /**
     * @description 生成表单项的配置参数，包括校验规则
     * @param {Object} config - 表单项配置
     * @returns {Object} 表单项配置
     */
    getFormItemConfig(config = {}) {
      const {
        label,
        component,
        isRequired = 0,
        rules: configRules = [],
        ruleMessage,
        slots,
        helpText,
        formInputConfig,
        ...restConfig
      } = config;
      let defaultValidator;

      if (!component) return { formInputConfig, ...restConfig };
      const { trigger, getMessage, validator } = constantRulesRequired[component]; // 默认规则触发方式和提示文字
      // 某些特殊组件需要走特殊的校验规则
      if (formInputConfig?.showCustom && !formInputConfig?.type?.includes('range') && component === 'date') {
        // 时间选择器，且存在显示至今选项
        defaultValidator = validator
          ? {
              required: true,
              trigger,
              validator: (rule, value, callback) => {
                validator(rule, value, callback, label);
              },
            }
          : {
              required: true,
              trigger,
              validator: (rule, value, callback) => {
                // 时间选择器允许值为null
                if (value === undefined || value === '') {
                  callback(new Error(ruleMessage || `请选择${label}`));
                }
                callback();
              },
            };
      } else {
        defaultValidator = validator
          ? {
              required: true,
              trigger,
              validator: (rule, value, callback) => {
                validator(rule, value, callback, label);
              },
            }
          : {
              required: true,
              trigger,
              message: ruleMessage || getMessage(label),
            };
      }
      let rules = [];

      if (this.mode === 'edit') {
        rules = isRequired
          ? [
              defaultValidator, // 默认的校验规则
              ...configRules, // 自定义的校验规则
            ]
          : [...configRules];

        return { formInputConfig, ...restConfig, rules, slots, helpText };
      }
      return { formInputConfig, ...restConfig, rules: [], slots, helpText };
    },
    /**
     * @description 初始化表单数据
     * 根据表单项配置生成对应的初始值
     */
    initFormData() {
      const formData = {};

      (this.formItemSource || []).forEach(({ prop, component, formInputConfig = {} }) => {
        if (!prop) return;

        formData[prop] = getInitialValueByComponent(component, formInputConfig);
      });

      // 合并现有表单数据，保留已有值
      this.formData = {
        ...formData,
        ...this.formData,
      };
    },
    /**
     * @description 提交上传文件
     * @param {string} refName - 上传组件的引用名
     * @returns {Promise} 上传结果
     */
    uploadFiles(refName) {
      if (refName) {
        const componentRef = this.$refs[refName].$children.find((ref) => ref.$vnode.data.ref === refName);
        if (componentRef?.$refs[refName]?.submit) {
          return componentRef.$refs[refName].submit();
        }
      }
      // 提供默认的 Promise 返回值
      return Promise.resolve([]);
    },
    /**
     * @description 渲染表单项插槽内容
     * @param {Function|string} slot - 插槽内容
     * @returns {VNode} 渲染结果
     */
    renderFormItemSlots(slot) {
      return typeof slot === 'function' ? slot() : slot;
    },
    /**
     * @description 渲染单个表单项
     * @param {Object} config - 表单项配置
     * @returns {VNode} 表单项节点
     */
    renderFormItem(config) {
      const { prop, label, tips, isRequired, infoText, component, ellipsis, events, hideLabel, labelWidth } = config;
      const formInputConfig = this.getFormInputConfig(config);
      const { rules, slots, helpText } = this.getFormItemConfig(config);

      const formItemComponent = constantComponent[component];
      const showLabelWidth = this.calculateLabelWidth(label, isRequired);
      const labelWidthNumber = parseInt(this.labelWidth.split('px')[0], 10);

      let lineHeight = '';
      if (labelWidthNumber <= showLabelWidth) {
        lineHeight = this.formType === 'form' ? '20px' : '16px';
      }

      if (config.component === 'title') {
        return <AsyncLoadComponent ref={formInputConfig.refName} label={label} path={formItemComponent} tips={tips} />;
      }

      return (
        <div
          class={{
            'x-form-item': true,
            'x-form-item__form': this.formType === 'form',
            'x-form-item__search': this.formType === 'search',
            'is-required': this.mode === 'edit' && !!isRequired,
          }}
        >
          {!this.hideLabel && !hideLabel && (
            <div
              class="x-form-item__label"
              style={`width: ${labelWidth !== undefined ? labelWidth : this.labelWidth};line-height: ${lineHeight};text-align: ${this.labelPosition}`}
            >
              {[
                label,
                infoText && (
                  <el-tooltip style="margin-left: 2px" effect="dark" placement="top" content={infoText}>
                    <i class="el-icon-warning fs-14"></i>
                  </el-tooltip>
                ),
                this.formType === 'search' ? ':' : '',
              ]}
            </div>
          )}

          <el-form-item
            prop={this.mode === 'edit' ? prop : ''}
            key={`${this.formId}_${prop}`}
            labelWidth="0"
            size={this.size}
            rules={this.mode === 'edit' ? rules : []}
          >
            {slots?.left && (
              <div class="el-form-item__content_slot mgr-5">{slots?.left && this.renderFormItemSlots(slots.left)}</div>
            )}
            {component && prop && (
              <AsyncLoadComponent
                ref={formInputConfig.refName}
                vModel={this.formData[prop]}
                path={formItemComponent}
                mode={formInputConfig.disabled || this.disabled ? 'disabled' : formInputConfig.mode || this.mode}
                ellipsis={ellipsis || false}
                disabled={formInputConfig.disabled || this.disabled}
                size={this.size}
                {...{
                  on: {
                    ...events,
                  },
                  attrs: formInputConfig,
                }}
              />
            )}
            {(slots?.right || helpText) && (
              <div class="el-form-item__content_slot">
                {slots?.right && this.renderFormItemSlots(slots.right)}
                {helpText && (
                  <el-tooltip content={helpText}>
                    <i size="16px" style="color:var($--x-text-color-placeholder)" class="el-icon-question"></i>
                  </el-tooltip>
                )}
              </div>
            )}
          </el-form-item>
        </div>
      );
    },
    /**
     * @description 渲染表单内容
     * @returns {VNode[]} 表单项节点数组
     */
    renderDefaultForm() {
      return this.formItemSource.map((config) => {
        let itemSpan;

        if (this.formType === 'search') {
          // 搜索模式下使用响应式栅格占比
          itemSpan = config.formItemSpan || this.responsiveSearchItemSpan;
        } else {
          // 普通表单模式
          itemSpan =
            this.mode === 'edit'
              ? config.formItemSpan || this.formItemSpan
              : config.viewItemSpan || config.formItemSpan || this.formItemSpan;
        }

        return (
          this.formItemIsShow(config) && (
            <el-col span={itemSpan} type="flex">
              {this.renderFormItem(config)}
            </el-col>
          )
        );
      });
    },
    /**
     * @description 渲染表单标题
     * @returns {VNode} 标题节点
     */
    renderTitle() {
      return (
        this.title && (
          <div span={24} class="x-form-items__title">
            {this.title}
          </div>
        )
      );
    },
    /**
     * 计算标签宽度
     * @param label
     * @param isRequired
     */
    calculateLabelWidth(label, isRequired) {
      const tempDiv = document.createElement('div');
      try {
        tempDiv.style.position = 'absolute';
        tempDiv.style.visibility = 'hidden';
        tempDiv.style.whiteSpace = 'nowrap';
        tempDiv.style.fontSize = '14px';
        tempDiv.textContent = label;

        document.body.appendChild(tempDiv);
        const labelWidth =
          tempDiv.offsetWidth + (this.mode === 'edit' && isRequired && this.formType === 'form' ? 6 : 0);
        return labelWidth;
      } finally {
        // 确保无论如何都会移除DOM节点
        if (tempDiv.parentNode) {
          document.body.removeChild(tempDiv);
        }
      }
    },
  },
  render() {
    // 普通表单模式
    if (this.formType === 'form') {
      return (
        <div class="x-form-items x-form-items-normal">
          {this.renderTitle()}
          {!!this.formItemSource.length && (
            <el-row
              type="flex"
              id={this.formId}
              class={`x-form-items__${this.mode}`}
              style="flex-wrap: wrap"
              gutter={this.formType === 'search' ? 12 : 60}
            >
              {this.renderDefaultForm()}
              {this.$slots['form-append']}
              {this.$slots['handle-btn']}
            </el-row>
          )}
        </div>
      );
    }
    // 搜索表单模式
    return (
      <div class="x-form-items x-form-items-search">
        <div class="x-form-search-form">
          <el-row type="flex" id={this.formId} style="flex-wrap: wrap" gutter={16}>
            {this.renderDefaultForm()}
            {this.formItemSource.length < this.responsiveSearchItemsPerRow && this.$slots.searchButton}
          </el-row>
        </div>
        {this.formItemSource.length >= this.responsiveSearchItemsPerRow && (
          <div class="x-form-search-handle">{this.$slots.searchButton}</div>
        )}
      </div>
    );
  },
};
</script>

<style lang="scss">
@import './index.scss';
</style>
