@import '../styles/index.scss';
$--x-text-color-placeholder: rgba(0, 0, 0, 0.4);
.x-form-items {
  overflow: hidden;
  &-normal {
    .x-form-item {
      &__form {
        margin-bottom: 20px !important;
      }
      &.is-required {
        .x-form-item__label::before {
          width: 6px;
          height: 100%;
          text-align: center;
          display: inline-block;
          content: '*';
          color: red;
        }
      }
      &__label {
        float: left;
        color: $--form-item-label-color;
        font-size: 14px;
        line-height: 40px;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }

      .el-input__inner {
        height: 40px;
        line-height: 40px;
      }
    }
    // .x-form-items__view,
    // .x-form-items__disabled {
    //   .x-input,
    //   .x-select {
    //     & + .el-form-item__error {
    //       display: none;
    //     }
    //   }
    // }
    .el-input__icon {
      line-height: 38px;
    }
  }
  &-search {
    width: 100%;
    display: flex;
    .x-form-item {
      display: flex;
      &__form {
        margin-bottom: 16px !important;
      }
      &__label {
        color: $--form-item-label-color;
        font-size: 14px;
        line-height: 40px;
        margin-right: 12px;
        flex-shrink: 0;
      }

      .el-form-item {
        flex-grow: 1;
      }
    }
    .x-form-search-form {
      flex-grow: 1;
    }
  }
  &__title {
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 20px;
    position: relative;
    &::before {
      display: inline-block;
      content: '';
      width: 4px;
      height: 20px;
      border-radius: 2px;
      background-color: var(--brand-6, #0f45ea);
      margin-right: 6px;
      vertical-align: top;
    }
  }
  .x-form-item {
    .el-form-item__content {
      display: flex;
      align-items: center;
      .x-input,
      .x-select,
      .x-select-tree,
      .x-radio,
      .x-checkbox,
      .x-cascader,
      .x-date-picker,
      .x-switch,
      .x-upload,
      .x-input-number,
      .x-custom {
        flex: 1 1 0;
        width: 100%;
      }
      .x-input,
      .x-input-number,
      .x-upload,
      .x-custom {
        overflow: hidden;
      }
    }
  }
  // 输入框样式定制
  .el-input,
  .el-select__input,
  .el-input__inner,
  .el-date-editor .el-range-input {
    color: $--form-item-value-color;
  }
  .el-input__inner,
  .el-transfer-panel,
  .el-textarea__inner {
    border-radius: $--border-radius;
  }

  .el-input-number__decrease,
  .el-input-group__prepend {
    border-radius: $--border-radius 0 0 $--border-radius;
  }
  .el-input-number__increase,
  .el-input-group__append {
    border-radius: 0 $--border-radius $--border-radius 0;
  }
}
