<!--
 * @Description: 动态组件
 * @Version:
 * @Author: <PERSON><PERSON>
 * @Date: 2022-05-18 14:52:40
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-06-04 14:34:40
-->
<template>
  <component :is="renderView" :ref="$attrs.refName" v-model="inputValue" v-bind="$attrs" v-on="$listeners" />
</template>

<script lang="jsx">
// 异步加载组件
export default {
  name: 'AsyncLoadComponent',
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    // 组件v-model绑定的值value
    value: {
      type: [Array, String, Number, Boolean, Object, Date],
      default: () => null,
    },
    // 组件路径
    path: {
      type: String,
      default: '',
    },
  },
  computed: {
    // 组件绑定的v-model更新
    inputValue: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('update:value', value);
      },
    },
    // 按需渲染组件 - 优化动态导入，减少打包警告
    renderView() {
      // 使用静态映射减少动态导入警告
      const componentMap = {
        button: () => import('../button/index.vue'),
        tooltip: () => import('../tooltip/index.vue'),
        cascader: () => import('../cascader/index.vue'),
        checkbox: () => import('../checkbox/index.vue'),
        custom: () => import('../custom/index.vue'),
        datePicker: () => import('../datePicker/index.vue'),
        dialog: () => import('../dialog/index.vue'),
        empty: () => import('../empty/index.vue'),
        pagination: () => import('../pagination/index.vue'),
        editTable: () => import('../editTable/index.vue'),
        formItems: () => import('./index.vue'),
        form: () => import('../form/index.vue'),
        title: () => import('../title/index.vue'),
        input: () => import('../input/index.vue'),
        inputNumber: () => import('../inputNumber/index.vue'),
        inputNumberRange: () => import('../inputNumberRange/index.vue'),
        radio: () => import('../radio/index.vue'),
        search: () => import('../search/index.vue'),
        toolbar: () => import('../toolbar/index.vue'),
        table: () => import('../table/index.vue'),
        searchTable: () => import('../searchTable/index.vue'),
        select: () => import('../select/index.vue'),
        selectTree: () => import('../selectTree/index.vue'),
        switch: () => import('../switch/index.vue'),
        text: () => import('../text/index.vue'),
        transfer: () => import('../transfer/index.vue'),
        upload: () => import('../upload/index.vue'),
      };

      return componentMap[this.path] || (() => import(`../${this.path}/index.vue`));
    },
  },
};
</script>
