<script>
  import BasicVue from './basic.vue';
  import ValidationVue from './validation.vue';
  import DynamicVue from './dynamic.vue';
  import LayoutVue from './layout.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      ValidationVue,
      DynamicVue,
      LayoutVue,
      Preview
    }
  };
</script>

## FormItems 组件

FormItems 组件是一个灵活的表单项集合组件，基于 Element UI 的表单组件进行封装，支持通过配置动态生成各种类型的表单项，提供了丰富的表单控件和验证功能。

### 基础用法

通过配置 formConfig 来定义表单项，支持 input、select、radio 等多种表单控件类型。

<basic-vue/>
<preview  comp-name='formItems' demo-name='basic'/>

### 表单验证

通过配置 rules 来定义表单项的验证规则，支持必填、自定义正则、类型等多种验证方式。

<validation-vue/>
<preview  comp-name='formItems' demo-name='validation'/>

### 动态表单

通过监听表单项的变化事件，动态显示或隐藏其他表单项，实现复杂的表单交互逻辑。

<dynamic-vue/>
<preview  comp-name='formItems' demo-name='dynamic'/>

### 自定义布局

通过配置 formItemSpan 属性来控制表单项的布局，支持24栅格系统，可以灵活地设置表单项的宽度和排列方式。

<layout-vue/>
<preview  comp-name='formItems' demo-name='layout'/>

### Attributes

| 参数          | 说明                                                         | 类型    | 可选值             | 默认值 | 必传 |
| ------------- | ------------------------------------------------------------ | ------- | ------------------ | ------ | ---- |
| value/v-model | 表单项组绑定的数据对象。需与外层的 t-form 保持一致。         | Object  | —                  | —      | T    |
| formConfig    | 需要渲染的表单 formItem 配置                                 | Object  | —                  | —      | T    |
| title         | 表单标题                                                     | object  | —                  | —      | N    |
| mode          | 编辑模式                                                     | String  | edit/disabled/view | edit   | N    |
| formItemSpan  | 表单栅格布局每列占多少，element-eoss 栅格 12 等分            | Number  | —                  | 12     | N    |
| formType      | 表单用于类型,表单或搜索                                      | String  | form/search        | form   | N    |
| colon         | 是否在表单标签字段右侧显示冒号是否在表单标签字段右侧显示冒号 | Boolean | false              | true   | N    |
| labelWidth    | label 宽度                                                   | String  | —                  | 110px  | N    |
| hideLabel     | 是否隐藏 label,主要使用于可编辑表格场景                      | Boolean | —                  | false  | N    |
| disabled      | 是否禁用整个表单                                             | Boolean | —                  | false  | N    |
| singleRow     | 是否是单行模式，即表示一行一个输入项                         | Boolean | —                  | false  | N    |

### formConfig

| 参数            | 说明                                                   | 类型           | 可选值                                                                                                         | 默认值 |
| --------------- | ------------------------------------------------------ | -------------- | -------------------------------------------------------------------------------------------------------------- | ------ |
| prop            | 必需，唯一标识 也是生成的数据的 key                    | String         | —                                                                                                              | —      |
| label           | 必需，表单项名称                                       | String         | —                                                                                                              | —      |
| component       | 必需，组件类型                                         | String         | input/inputNumber/textarea/select/cascader/radio/checkbox/date/selectTree/select/iconSelect/text/upload/custom | —      |
| formItemSpan    | 当前表单项占自定义栅格数                               | String/Number  | —                                                                                                              | —      |
| isRequired      | 字段是否必填                                           | Boolean/Number | false                                                                                                          | —      |
| ellipsis        | 回显内容是否溢出隐藏                                   | Boolean        | false                                                                                                          | true   |
| isMultiple      | 是否支持多选，仅对【下拉框】有效                       | Boolean        | true                                                                                                           | false  |
| filterable      | 是否支持静态搜索，仅对【下拉框】有效                   | Boolean        | true                                                                                                           | false  |
| isShow          | 字段是否在表单展示                                     | String         | false                                                                                                          | true   |
| infoText        | 表单域侧边提示文字                                     | String         | —                                                                                                              | —      |
| formInputConfig | 支持 element-eoss <所选表单控件>组件所有属性           | Object         | —                                                                                                              | —      |
| events          | 支持 element-eoss <所选表单控件>组件所有事件           | Object         | —                                                                                                              | —      |
| ...             | FormItem 配置，支持 element-eoss FormItem 组件所有属性 | Object         | —                                                                                                              | —      |

### formInputConfig

| 参数         | 说明                                                              | 类型     | 可选值 | 默认值                                   |
| ------------ | ----------------------------------------------------------------- | -------- | ------ | ---------------------------------------- |
| options      | 选项，仅对 select/selectTree/select/cascader 有效                 | Array    | —      | —                                        |
| maxlength    | 字段数据长度，仅对【input】、【inputNumber】、【textarea】有效    | String   | —      | —                                        |
| clearable    | 是否允许清除该值,默认允许                                         | Boolean  | false  | true                                     |
| digits       | 9_2，9 位整数 2 位小数，仅对【inputNumber】有效                   | String   | —      | 9_2                                      |
| placeholder  | 输入框占位文本                                                    | String   | —      | —                                        |
| modifiers    | 修饰字段，仅对【inputNumber】有效                                 | Object   | —      | {negative:true,short:true,canEmpty:true} |
| remoteMethod | 懒加载方法，仅仅 superProps.lazy 为 true 生效                     | function | —      | —                                        |
| slots        | 支持 element 表单组件全部插槽,例：{append: h => {return '内容';}} | Object   | —      | —                                        |
| ...          | 其他原生表单组件属性                                              | —        | —      | —                                        |

### modifiers 修饰字段，仅对【inputNumber】有效

| 参数     | 说明                                    | 类型    | 可选值 | 默认值 |
| -------- | --------------------------------------- | ------- | ------ | ------ |
| negative | 允许输入负数，否则默认整数（含小数）    | Boolean | false  | true   |
| short    | 小数位不补零                            | Boolean | false  | true   |
| canEmpty | 可以为空 加上后为空字符串时不格式化为 0 | Boolean | false  | true   |

### Events

| 事件名称 | 说明                 | 回调参数                     |
| -------- | -------------------- | ---------------------------- |
| change   | 表单项值改变时触发   | (value: any, prop: string)   |
| blur     | 表单项失去焦点时触发 | (event: Event, prop: string) |
| focus    | 表单项获得焦点时触发 | (event: Event, prop: string) |

### Methods

| 方法名        | 说明                   | 参数                                                               |
| ------------- | ---------------------- | ------------------------------------------------------------------ |
| validate      | 对整个表单进行校验     | (callback: Function(boolean, object))                              |
| validateField | 对部分表单字段进行校验 | (props: array \| string, callback: Function(errorMessage: string)) |
| clearValidate | 移除表单项的校验结果   | (props: array \| string)                                           |
