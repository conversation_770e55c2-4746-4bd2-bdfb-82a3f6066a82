<template>
  <div class="component-view">
    <el-form ref="form" :model="formData" label-width="130px">
      <x-form-items ref="formItems" v-model="formData" label-width="130px" :form-config="formConfig" />
      <div style="margin-left: 130px">
        <el-button type="primary" @click="submitForm('form')">提交</el-button>
        <el-button @click="resetForm('form')">重置</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        employmentType: '',
        companyName: '',
        businessLicense: [],
        schoolName: '',
        studentId: '',
        idCard: '',
      },
      formConfig: [
        {
          label: '就业类型',
          prop: 'employmentType',
          component: 'radio',
          isRequired: true,
          formInputConfig: {
            options: [
              { label: '在职', value: 'employed' },
              { label: '创业', value: 'selfEmployed' },
              { label: '学生', value: 'student' },
            ],
          },
          events: {
            change: (value) => {
              // 重置表单项显示状态
              this.formConfig.forEach((item) => {
                if (['companyName', 'businessLicense', 'schoolName', 'studentId'].includes(item.prop)) {
                  item.isShow = false;
                }
              });

              // 根据选择的就业类型显示对应的表单项
              switch (value) {
                case 'employed':
                  this.formConfig.find((item) => item.prop === 'companyName').isShow = true;
                  break;
                case 'selfEmployed':
                  this.formConfig.find((item) => item.prop === 'companyName').isShow = true;
                  this.formConfig.find((item) => item.prop === 'businessLicense').isShow = true;
                  break;
                case 'student':
                  this.formConfig.find((item) => item.prop === 'schoolName').isShow = true;
                  this.formConfig.find((item) => item.prop === 'studentId').isShow = true;
                  break;
                default:
              }
            },
          },
        },
        {
          label: '公司名称',
          prop: 'companyName',
          component: 'input',
          isShow: false,
          isRequired: true,
        },
        {
          label: '营业执照',
          prop: 'businessLicense',
          component: 'upload',
          isShow: false,
          isRequired: true,
          formInputConfig: {
            action: '/api/upload',
            accept: '.jpg,.png,.pdf',
            limit: 1,
          },
        },
        {
          label: '学校名称',
          prop: 'schoolName',
          component: 'input',
          isShow: false,
          isRequired: true,
        },
        {
          label: '学生证号',
          prop: 'studentId',
          component: 'input',
          isShow: false,
          isRequired: true,
        },
        {
          label: '身份证号',
          prop: 'idCard',
          component: 'input',
          isRequired: true,
          rules: [
            { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号', trigger: 'blur' },
          ],
        },
      ],
    };
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$message.success('表单验证通过！');
          console.log('表单数据：', this.formData);
        } else {
          this.$message.error('表单验证失败，请检查输入！');
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  },
};
</script>
