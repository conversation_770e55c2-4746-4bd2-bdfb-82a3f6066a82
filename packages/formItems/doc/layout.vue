<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-11 09:36:25
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-11 10:58:40
 * @FilePath: /vite-element-components/packages/formItems/doc/layout.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <el-form ref="form" :model="formData" label-width="130px">
      <x-form-items ref="formItems" v-model="formData" :form-config="formConfig" />
      <div style="margin-left: 130px">
        <el-button type="primary" @click="submitForm('form')">提交</el-button>
        <el-button @click="resetForm('form')">重置</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formData: {},
      formConfig: [
        {
          label: '标题',
          prop: 'title',
          component: 'input',
          formItemSpan: 24,
        },
        {
          label: '姓名',
          prop: 'name',
          component: 'input',
          formItemSpan: 12,
        },
        {
          label: '年龄',
          prop: 'age',
          component: 'inputNumber',
          formItemSpan: 12,
          formInputConfig: {
            min: 0,
            max: 100,
          },
        },
        {
          label: '部门',
          prop: 'department',
          component: 'select',
          formItemSpan: 12,
          formInputConfig: {
            options: [
              { label: '研发部', value: 'dev' },
              { label: '市场部', value: 'market' },
              { label: '运营部', value: 'operation' },
            ],
          },
        },
        {
          label: '详细地址',
          prop: 'address',
          component: 'input',
          formItemSpan: 16,
          formInputConfig: {
            type: 'textarea',
            rows: 3,
          },
        },
        {
          label: '备注',
          prop: 'remark',
          component: 'input',
          formItemSpan: 24,
          formInputConfig: {
            type: 'textarea',
            rows: 4,
          },
        },
      ],
    };
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$message.success('表单验证通过！');
          console.log('表单数据：', this.formData);
        } else {
          this.$message.error('表单验证失败，请检查输入！');
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  },
};
</script>
