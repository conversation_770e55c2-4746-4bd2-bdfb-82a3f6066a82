<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-11 09:34:58
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-11 10:58:04
 * @FilePath: /vite-element-components/packages/formItems/doc/validation.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <el-form ref="form" :model="formData">
      <x-form-items ref="formItems" v-model="formData" label-width="130px" :form-config="formConfig" />
      <div style="margin-left: 130px">
        <el-button type="primary" @click="submitForm('form')">提交</el-button>
        <el-button @click="resetForm('form')">重置</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formData: {},
      formConfig: [
        {
          label: '用户名',
          prop: 'username',
          component: 'input',
          isRequired: true,
        },
        {
          label: '邮箱',
          prop: 'email',
          component: 'input',
          isRequired: true,
          rules: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
        },
        {
          label: '年龄',
          prop: 'age',
          component: 'inputNumber',
          isRequired: true,
          rules: [{ type: 'number', min: 18, max: 100, message: '年龄必须在18到100岁之间', trigger: 'blur' }],
          formInputConfig: {
            min: 0,
            max: 100,
          },
        },
        {
          label: '性别',
          prop: 'gender',
          component: 'radio',
          isRequired: true,
          formInputConfig: {
            options: [
              { label: '男', value: 'male' },
              { label: '女', value: 'female' },
            ],
          },
        },
        {
          label: '兴趣爱好',
          prop: 'hobbies',
          component: 'checkbox',
          isRequired: true,
          formInputConfig: {
            options: [
              { label: '阅读', value: 'reading' },
              { label: '音乐', value: 'music' },
              { label: '运动', value: 'sports' },
              { label: '旅行', value: 'travel' },
            ],
          },
        },
      ],
    };
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$message.success('表单验证通过！');
          console.log('表单数据：', this.formData);
        } else {
          this.$message.error('表单验证失败，请检查输入！');
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  },
};
</script>
