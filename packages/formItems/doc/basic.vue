<template>
  <div class="component-view">
    {{ formData }}
    <el-form ref="form" :model="formData" label-width="130px">
      <x-form-items
        ref="designForm"
        title="我是标题"
        v-model="formData"
        label-width="130px"
        :form-config="formConfig"
      />
      <div style="margin-left: 130px">
        <el-button type="primary" @click="submitForm('form')">提交</el-button>
        <el-button type="default" @click="$refs.form.resetFields()">重置</el-button>
        <el-button type="default" @click="handleUpload">手动上传</el-button>
      </div>
    </el-form>
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    const cascaderOptions = [
      {
        value: 'zhinan',
        name: '设计',
        children: [
          {
            value: 'shejiyuanze',
            name: '设计原则',
            children: [
              {
                value: 'yizhi',
                name: '一致',
              },
              {
                value: 'fankui',
                name: '反馈',
              },
            ],
          },
          {
            value: 'daohang',
            name: '导航',
            children: [
              {
                value: 'cexiangdaohang',
                name: '侧向导航',
              },
              {
                value: 'dingbudaohang',
                name: '顶部导航',
              },
            ],
          },
        ],
      },
      {
        value: 'zujian',
        name: '组件',
        children: [
          {
            value: 'basic',
            name: 'Basic',
            children: [
              {
                value: 'layout',
                name: 'Layout 布局',
              },
              {
                value: 'color',
                name: 'Color 色彩',
              },
            ],
          },
        ],
      },
      {
        value: 'ziyuan',
        name: '资源',
        children: [
          {
            value: 'axure',
            name: 'Axure Components',
          },
          {
            value: 'sketch',
            name: 'Sketch Templates',
          },
          {
            value: 'jiaohu',
            name: '组件交互文档',
          },
        ],
      },
    ];
    return {
      formData: {
        禁用输入框: '名称名称名称名称',
        普通级联Name: '2112',
        单选框: 0,
      },
      formConfig: [
        {
          label: '我是另一个标题',
          component: 'title',
          formItemSpan: 24,
        },
        {
          prop: '禁用输入框',
          label: '禁用输入框',
          component: 'input',
          isRequired: 1,
          formItemSpan: 12,
          infoText: '我是禁用的',
          formInputConfig: {
            disabled: true,
            // readonly: true,
            slots: {
              append: '万元',
            },
          },
        },
        {
          prop: '普通输入框',
          label: '普通输入框',
          component: 'input',
          isRequired: 1,
          formItemSpan: 12,
          formInputConfig: {
            maxlength: 100,
            showWordLimit: true,
            slots: {
              append: '万元',
            },
          },
          rules: [
            {
              validator: (rule, value, callback) => {
                if (value?.length <= 5) {
                  callback(new Error('至少 5 个字，中文长度等于英文长度'));
                } else {
                  callback();
                }
              },
              trigger: 'blur',
            },
            {
              validator: (rule, value, callback) => {
                if (value?.length > 20) {
                  callback(new Error('不能超过 20 个字，中文长度等于英文长度'));
                } else {
                  callback();
                }
              },
              trigger: 'blur',
            },
          ],
        },
        {
          prop: '多行文本框',
          label: '多行文本框多行文本框',
          component: 'input',
          isRequired: 1,
          formInputConfig: {
            type: 'textarea',
            maxlength: 100,
            showWordLimit: true,
          },
        },
        {
          prop: '普通级联',
          label: '普通级联',
          component: 'cascader',
          formItemSpan: 12,
          formInputConfig: {
            clearable: true,
            options: cascaderOptions,
            props: {
              label: 'name',
              value: 'value',
              children: 'children',
            },
          },
          events: {
            change: (value, option) => {
              console.log('%c [  ]-194-「App」', 'font-size:13px; background:pink; color:#bf2c9f;', value, option);
            },
          },
        },
        {
          prop: '动态级联',
          label: '动态级联',
          component: 'cascader',
          formItemSpan: 8,
          formInputConfig: {
            clearable: false,
            props: {
              label: 'name',
              value: 'value',
              children: 'children',
            },
            options: [
              {
                name: '第一层-1',
                value: 1,
                children: [],
              },
              {
                name: '第一层-2',
                value: 2,
                children: [],
              },
            ],
            lazy: true,
            load: (node) =>
              new Promise((resolve) => {
                setTimeout(() => {
                  let nodes = [];
                  if (node.level < 2) {
                    nodes = [
                      {
                        name: `${node.label}.1`,
                        value: `${node.value}-1.${node.level}`,
                        children: node.level < 1,
                      },
                      {
                        name: `${node.label}.2`,
                        value: `${node.value}-2.${node.level}`,
                        children: node.level < 1,
                      },
                    ];
                  }
                  resolve(nodes);
                }, 1000);
              }),
          },
          events: {
            change: (value) => {
              console.log('%c [  ]-62-「DesignCascader」', 'font-size:13px; background:pink; color:#bf2c9f;', value);
            },
          },
        },

        {
          prop: '下拉搜索分页',
          label: '下拉搜索分页',
          component: 'select',
          formItemSpan: 8,
          formInputConfig: {
            filterable: true,
            multiple: true,
            reserveKeyword: true,
            remote: true,
            onSearch: (query, page) => {
              console.log('%c [  ]-77-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', query, page);
              return {
                data: [
                  {
                    label: `${query}1`,
                    value: `${query}1`,
                  },
                  {
                    label: `${query}2`,
                    value: `${query}2`,
                  },
                  {
                    label: `${query}3`,
                    value: `${query}3`,
                  },
                ],
                hasMore: false,
              };
            },
            onLoadMore: (query, page) =>
              new Promise((resolve) => {
                setTimeout(() => {
                  if (page < 5) {
                    resolve({
                      data: new Array(15).fill(1).map((item, index) => ({
                        label: `${query}${page}_${index + 1}`,
                        value: `${query}${page}_${index + 1}`,
                      })),
                      hasMore: true,
                    });
                  }
                  resolve({
                    data: [
                      {
                        label: `${query}1${page}`,
                        value: `${query}11${page}`,
                      },
                      {
                        label: `${query}21${page}`,
                        value: `${query}21${page}`,
                      },
                      {
                        label: `${query}31${page}`,
                        value: `${query}31${page}`,
                      },
                    ],
                    hasMore: false,
                  });
                }, 500);
              }),
          },
          events: {
            blur: ({ value, e }) => {
              console.log('%c [ blur ]-287-「BaseForm」', 'font-size:13px; background:pink; color:#bf2c9f;', value, e);
            },
            enter: ({ value, e, inputValue }) => {
              console.log(
                '%c [ enter ]-294-「BaseForm」',
                'font-size:13px; background:pink; color:#bf2c9f;',
                value,
                e,
                inputValue,
              );
            },
          },
        },
        {
          prop: '普通下拉',
          label: '普通下拉',
          component: 'select',
          formItemSpan: 8,
          formInputConfig: {
            options: [
              { label: '张三', value: 1 },
              { label: '李四', value: 2 },
              { label: '王五', value: 3 },
            ],
          },
        },
        {
          prop: '异步下拉',
          label: '异步下拉',
          component: 'select',
          formItemSpan: 8,
          formInputConfig: {
            keys: {
              value: 'value1',
              label: 'label1',
            },
            remoteMethod: () => [
              { label1: '张三', value1: 1 },
              { label1: '李四', value1: 2 },
              { label1: '王五', value1: 3 },
            ],
          },
        },
        {
          label: '普通日期',
          prop: '普通日期',
          component: 'date',
          formItemSpan: 8,
          formInputConfig: {
            type: 'date',
          },
        },
        {
          label: '日期时间',
          prop: '日期时间',
          component: 'date',
          formItemSpan: 8,
          formInputConfig: {
            type: 'datetime',
          },
        },
        {
          label: '日期区间',
          prop: '日期区间',
          component: 'date',
          formItemSpan: 8,
          formInputConfig: {
            type: 'daterange',
          },
        },
        {
          label: '月份区间',
          prop: '月份区间',
          component: 'date',
          formItemSpan: 8,
          formInputConfig: {
            type: 'monthrange',
          },
        },
        {
          label: '时间区间',
          prop: '时间区间',
          component: 'date',
          formItemSpan: 8,
          formInputConfig: {
            type: 'datetimerange',
          },
        },
        {
          label: '选择年份',
          prop: '选择年份',
          component: 'date',
          formItemSpan: 8,
          formInputConfig: {
            type: 'year',
          },
        },
        {
          label: '选择月份',
          prop: '选择月份',
          component: 'date',
          formItemSpan: 8,
          formInputConfig: {
            type: 'month',
          },
        },
        {
          label: '选择季度',
          prop: '选择季度',
          component: 'date',
          formItemSpan: 8,
          formInputConfig: {
            type: 'quarter',
          },
        },
        {
          label: '选择周',
          prop: '选择周',
          component: 'date',
          formItemSpan: 8,
          formInputConfig: {
            type: 'week',
          },
        },
        {
          label: '日期时间戳',
          prop: '日期时间戳',
          component: 'date',
          formItemSpan: 8,
          formInputConfig: {
            type: 'datetime',
            valueType: 'time-stamp',
          },
        },
        {
          label: '数字输入',
          prop: '数字输入',
          component: 'inputNumber',
          formItemSpan: 8,
          formInputConfig: {
            suffix: '个',
            digits: '3_2',
          },
        },
        {
          label: '无控制数字',
          prop: '无控制数字',
          component: 'inputNumber',
          formItemSpan: 8,
          formInputConfig: {
            suffix: '个',
            digits: '3_2',
            theme: 'normal',
          },
        },
        {
          label: '单选框',
          prop: '单选框',
          component: 'radio',
          formItemSpan: 8,
          formInputConfig: {
            allowUncheck: true,
            options: [
              {
                value: 0,
                label: '苹果',
              },
              {
                value: 1,
                label: '西瓜',
              },
              {
                value: 2,
                label: '香蕉',
              },
              {
                value: 3,
                label: '牛奶',
                disabled: true,
              },
            ],
          },
        },
        {
          label: '复选框',
          prop: '复选框',
          component: 'checkbox',
          formItemSpan: 8,
          formInputConfig: {
            options: [
              {
                value: 1,
                label: '香蕉',
              },
              {
                value: 2,
                label: '苹果',
              },
              {
                value: 3,
                label: '西瓜',
              },
            ],
          },
        },
        {
          label: '带全选复选框',
          prop: '带全选复选框',
          component: 'checkbox',
          formItemSpan: 8,
          formInputConfig: {
            options: [
              {
                label: '全选',
                checkAll: true,
              },
              {
                value: 1,
                label: '香蕉',
              },
              {
                value: 2,
                label: '苹果',
              },
              {
                value: 3,
                label: '西瓜',
              },
            ],
          },
        },
        {
          label: '自定义上传',
          prop: '自定义上传',
          component: 'upload',
          formItemSpan: 8,
          isRequired: true,
          formInputConfig: {
            refName: 'test',
            action: 'https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo',
            theme: 'custom',
            autoUpload: false,
            slots: {
              default: <el-button theme="primary">自定义上传</el-button>,
              footer: () => <div>插槽渲染的内容1111</div>,
            },
            onSuccess: (context) => {
              console.log(
                '%c [ onSuccess ]-629-「BaseForm」',
                'font-size:13px; background:pink; color:#bf2c9f;',
                context,
              );
            },
          },
        },
      ],
    };
  },
  methods: {
    handleUpload() {
      this.$refs.designForm.uploadFiles('test');
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          console.log('%c [  ]-571-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', 1);
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
  },
};
</script>
