<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-24 17:02:36
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 11:02:15
 * @FilePath: /vite-element-components/packages/dialog/index.vue
 * @Description: XDialog 组件 - 基于 Element UI 的对话框组件封装
-->

<script lang="jsx">
import { generateComponentKey } from '../shared';

/**
 * XDialog 组件
 * @component XDialog
 * @description 基于 Element UI 的 Dialog 组件封装，提供更便捷的配置方式和更丰富的功能
 *
 * @features
 * - 支持全屏切换功能
 * - 支持自定义头部操作按钮
 * - 支持多种预设尺寸（tips、small、medium、large）
 * - 支持自定义底部操作按钮配置
 * - 内置表单重置功能
 * - 支持动态高度设置
 *
 * @example
 * // 基础用法
 * <x-dialog v-model:show="visible" title="基础对话框">内容</x-dialog>
 *
 * // 全屏模式
 * <x-dialog v-model:show="visible" :fullscreen="true" :show-fullscreen="true">内容</x-dialog>
 *
 * // 自定义操作按钮
 * <x-dialog v-model:show="visible" :actions="[{label: '确定', onClick: handleConfirm}]">内容</x-dialog>
 */
export default {
  name: 'XDialog',
  title: '对话框组件',
  inheritAttrs: false,
  model: {
    prop: 'show',
    event: 'update:show',
  },

  props: {
    /**
     * 底部操作按钮配置
     * @type {Array}
     * @description 按钮配置数组，每个按钮支持以下属性：
     *   - label: 按钮文本
     *   - type: 按钮类型（primary, success, warning, danger 等）
     *   - isCancel: 是否为取消按钮
     *   - onClick: 点击事件处理函数
     *   - show: 是否显示（默认为 true）
     */
    actions: {
      type: Array,
      default: () => [],
    },
    /**
     * 控制对话框是否显示
     * @type {Boolean}
     */
    show: {
      type: Boolean,
      default: false,
    },

    /**
     * 对话框标题
     * @type {String}
     */
    title: {
      type: String,
      default: '',
    },

    /**
     * 是否可以通过点击遮罩层关闭对话框
     * @type {Boolean}
     */
    closeOnClickModal: {
      type: Boolean,
      default: false,
    },

    /**
     * 是否开启全屏模式
     * @type {Boolean}
     */
    fullscreen: {
      type: Boolean,
      default: false,
    },

    /**
     * 对话框宽度
     * @type {String}
     * @description 支持百分比或像素值，如 '50%' 或 '800px'
     */
    width: {
      type: [String],
      default: '',
    },

    /**
     * 对话框高度
     * @type {String}
     * @description 支持百分比或像素值，如 '60%' 或 '600px'
     */
    height: {
      type: [String],
      default: '',
    },

    /**
     * 是否在关闭对话框后销毁组件
     * @type {Boolean}
     */
    destroyOnClose: {
      type: Boolean,
      default: false,
    },

    /**
     * 是否展示全屏切换按钮
     * @type {Boolean}
     */
    showFullscreen: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否展示loading
     * @type {Boolean}
     */
    dialogLoading: {
      type: Boolean,
      default: false,
    },
    /**
     * 弹窗加载文字
     * @type {Boolean}
     */
    dialogLoadingText: {
      type: String,
      default: '',
    },
    /**
     * 对话框预设尺寸
     * @type {String}
     * @values tips, small, medium, large
     * @description
     *   - tips: 480px-960px 宽度，适用于提示性对话框
     *   - small: 530px 宽度，适用于简单表单
     *   - medium: 720px 宽度，适用于中等复杂度表单
     *   - large: 960px 宽度，适用于复杂表单或表格
     */
    size: {
      type: String,
      default: 'large',
      validator: (value) => ['tips', 'small', 'medium', 'large'].includes(value),
    },
  },

  data() {
    return {
      /** 内部全屏状态控制 */
      isFullscreen: this.fullscreen,
      /** 组件唯一标识 */
      uniqueKey: generateComponentKey('dialog'),
    };
  },

  computed: {
    /**
     * 对话框显示状态的双向绑定
     * @returns {Boolean} 当前显示状态
     */
    visible: {
      get() {
        return this.show;
      },
      set(value) {
        this.$emit('update:show', value);
      },
    },

    /**
     * 过滤需要显示的操作按钮
     * @returns {Array} 可见的操作按钮数组
     * @description 过滤掉 show 属性为 false 的按钮，避免无按钮时显示空的 footer
     */
    showActions() {
      // 因操作按钮的是否显示默认值是未赋值undefined
      return this.actions.filter((action) => action.show === undefined || action.show);
    },

    /**
     * 对话框样式类名
     * @returns {Object} 样式类名对象
     */
    dialogClasses() {
      return {
        'x-dialog': true,
        'is-fullscreen': this.isFullscreen,
        [`x-dialog--${this.size}`]: this.size,
      };
    },
  },
  watch: {
    /**
     * 监听对话框显示状态变化
     * @param {Boolean} val 新的显示状态
     */
    visible(val) {
      if (val) {
        // 对话框打开时，重置滚动位置到顶部
        this.$nextTick(() => {
          const dialogBody = this.$el?.querySelector('.el-dialog__body');
          if (dialogBody) {
            dialogBody.scrollTop = 0;
          }
        });
      }
    },
    /**
     * 监听 fullscreen prop 变化
     * @param {Boolean} val 新的全屏状态
     */
    fullscreen(val) {
      this.isFullscreen = val;
    },
    // 监听 loading prop 改变
    dialogLoading(v) {
      this.renderDialogLoading(v);
    },
  },
  methods: {
    // 获取弹窗节点添加loading
    renderDialogLoading(flag) {
      this.$nextTick(() => {
        let dialogEl = this.$refs.dialog.$el.querySelector('.el-dialog');
        let dialogBody = dialogEl.querySelector('.el-dialog__body');
        let dialogFooter = dialogEl.querySelector('.el-dialog__footer');

        if (flag) {
          // 创建包装容器来覆盖body和footer
          let loadingContainer = dialogEl.querySelector('.x-dialog__loading-container');
          if (!loadingContainer) {
            loadingContainer = document.createElement('div');
            loadingContainer.className = 'x-dialog__loading-container';
            loadingContainer.style.cssText = `
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              z-index: 10;
              pointer-events: auto;
            `;

            // 计算需要覆盖的区域
            const headerHeight = dialogEl.querySelector('.x-dialog__header')?.offsetHeight || 0;
            loadingContainer.style.top = `${headerHeight}px`;

            dialogEl.appendChild(loadingContainer);
          }

          this.loading = this.$loading({
            lock: true,
            text: this.dialogLoadingText || '',
            background: 'hsla(0,0%,100%,.9)', // 遮罩层颜色
            zIndex: 2,
            target: loadingContainer,
          });

          dialogBody.classList.add('x-dialog__loading');
          if (dialogFooter) {
            dialogFooter.classList.add('x-dialog__loading');
          }
        } else {
          this.loading.close();
          dialogBody.classList.remove('x-dialog__loading');
          if (dialogFooter) {
            dialogFooter.classList.remove('x-dialog__loading');
          }

          // 清理loading容器
          const loadingContainer = dialogEl.querySelector('.x-dialog__loading-container');
          if (loadingContainer) {
            loadingContainer.remove();
          }
        }

        dialogEl = null;
        dialogBody = null;
        dialogFooter = null;
      });
    },
    /**
     * 重置表单字段
     * @description 对话框关闭时自动重置内部 XForm 组件的表单字段
     */
    resetForm() {
      const slots = this.$slots.default;
      if (!slots) return;

      slots.forEach((element) => {
        const vNode = element.componentInstance;

        // 检查是否为 XForm 组件并调用重置方法
        if (vNode && vNode.$options.name === 'XForm' && typeof vNode.resetFields === 'function') {
          vNode.resetFields();
        }
      });
    },

    /**
     * 切换全屏状态
     * @emits fullscreen-change 全屏状态改变时触发
     */
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen;
      this.$emit('fullscreen-change', this.isFullscreen);
    },

    /**
     * 渲染对话框头部
     * @returns {VNode} 头部 JSX 元素
     */
    renderHeader() {
      return (
        <div
          class={{
            'x-dialog__header': true,
            'x-dialog__header--custom': !!this.$slots.title,
          }}
        >
          {/* 优先使用插槽内容，否则使用 title prop */}
          {this.$slots?.title ? this.$slots.title : <span class="el-dialog__title">{this.title}</span>}

          {/* 头部操作按钮区域 */}
          <div class="x-dialog__header__operations">
            {/* 全屏切换按钮 */}
            {this.showFullscreen && (
              <button
                class="x-dialog__header__operations-button"
                onClick={this.toggleFullscreen}
                title={this.isFullscreen ? '退出全屏' : '全屏'}
              >
                <i class={[this.isFullscreen ? 'el-icon-crop' : 'el-icon-full-screen']} />
              </button>
            )}

            {/* 关闭按钮 */}
            <button class="x-dialog__header__operations-button" onClick={this.handleCloseDialog} title="关闭">
              <i class="el-icon-close" />
            </button>
          </div>
        </div>
      );
    },

    /**
     * 渲染对话框底部操作按钮
     * @returns {VNode|null} 底部按钮组 JSX 元素或 null
     */
    renderFooter() {
      // 如果有可显示的操作按钮，则渲染底部区域
      if (this.showActions.length > 0) {
        return (
          <div slot="footer" class="x-dialog__footer">
            {this.showActions.map((action, index) => {
              const { isCancel, label, type, onClick } = action;

              return (
                <x-button
                  key={`dialog-action-${index}`}
                  label={isCancel && !label ? '取消' : label}
                  type={isCancel ? 'default' : type}
                  {...{
                    on: {
                      ...action,
                    },
                    props: {
                      ...action,
                    },
                  }}
                  onClick={
                    isCancel
                      ? (context) => {
                          // 取消按钮特殊处理：如果有自定义 onClick，先执行再关闭
                          if (onClick && typeof onClick === 'function') {
                            const result = onClick(context);

                            // 支持异步操作
                            if (result && typeof result.then === 'function') {
                              result
                                .then(() => {
                                  this.handleCloseDialog(context.$event);
                                })
                                .catch(() => {
                                  // 取消按钮的 onClick 失败时不关闭对话框
                                });
                            } else {
                              this.handleCloseDialog(context.$event);
                            }
                          } else {
                            this.handleCloseDialog(context.$event);
                          }
                        }
                      : onClick
                  }
                />
              );
            })}
          </div>
        );
      }
      return null;
    },

    /**
     * 关闭对话框
     * @param {Event} event 触发事件
     * @emits cancel 取消事件
     * @emits update:show 更新显示状态
     */
    handleCloseDialog(event) {
      this.visible = false;
      this.$emit('cancel', event);
    },
  },

  /**
   * 渲染函数
   * @returns {VNode} 对话框组件的虚拟节点
   */
  render() {
    const dialogProps = {
      ref: 'dialog',
      class: this.dialogClasses,
      style: {
        '--dialog-height': this.height,
      },
      props: {
        visible: this.visible,
        fullscreen: this.isFullscreen,
        width: this.width,
        height: this.height,
        customClass: `el-dialog--${this.size}`,
        closeOnClickModal: this.closeOnClickModal,
        destroyOnClose: this.destroyOnClose,
        showClose: false, // 使用自定义关闭按钮
        ...this.$attrs,
      },
      on: {
        ...this.$listeners,
        // 对话框完全关闭后的回调
        closed: () => {
          this.resetForm();
          this.$emit('closed');
        },
        // 对话框打开后的回调
        opened: () => {
          this.$emit('opened');
        },
      },
      key: this.uniqueKey,
    };

    return (
      <el-dialog {...dialogProps}>
        {/* 自定义头部 */}
        <template slot="title">{this.renderHeader()}</template>

        {/* 对话框主体内容 */}
        {!this.dialogLoading && this.$slots.default}

        {/* 底部操作按钮 */}
        {this.renderFooter()}
      </el-dialog>
    );
  },
};
</script>

<style lang="scss" scoped>
@import '../styles/index.scss';

/**
 * XDialog 组件样式
 * 基于 Element UI Dialog 组件的样式扩展
 */
.x-dialog {
  display: flex;
  align-items: center;

  /**
   * 对话框头部样式
   */
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding: 16px 30px 15px;
    border-bottom: 1px solid var(--border-color, #eee);

    // 自定义头部样式（使用插槽时）
    &--custom {
      border-bottom: none;
      padding: 16px 30px 0;
    }

    /**
     * 头部操作按钮区域
     */
    &__operations {
      position: absolute;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;
      gap: 8px;

      // 操作按钮样式
      &-button {
        height: 24px;
        width: 24px;
        padding: 0;
        background: transparent;
        border: none;
        outline: none;
        cursor: pointer;
        transition: all 0.2s ease;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background-color: var(--hover-bg, rgba(0, 0, 0, 0.04));
        }

        i {
          font-size: 16px;
          color: var(--icon-color, #8c8c8c);
          font-weight: 500;
          transition: color 0.2s ease;

          &:hover {
            color: var(--brand-6, #0f45ea);
          }

          &.is-active {
            color: var(--brand-6, #0f45ea);
          }
        }
      }
    }

    // 头部 tabs 组件样式优化
    ::v-deep {
      .el-tabs {
        width: 100%;

        .el-tabs__header {
          margin: 0;
        }
      }
    }
  }

  /**
   * 对话框底部操作区域样式
   */
  &__footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 隐藏类
.dp-n {
  display: none;
}

/**
 * Element UI Dialog 组件样式覆盖
 */
::v-deep {
  .el-dialog {
    display: flex;
    flex-direction: column;
    box-shadow: var(--dialog-shadow, 0 8px 24px rgba(0, 0, 0, 0.12));
    transition: all 0.3s ease;
    overflow: hidden;

    // 全屏模式样式
    &.is-fullscreen {
      border-radius: 0;
    }

    // 非全屏模式样式
    &:not(.is-fullscreen) {
      border-radius: 16px;
      margin-top: 0 !important;
      margin: 0 auto !important;
      height: var(--dialog-height, auto);
    }

    /**
     * 不同尺寸的对话框样式
     */
    // 小尺寸对话框
    &--small:not(.is-fullscreen) {
      width: 530px;
      max-height: 600px;
      min-height: 200px;
    }

    // 中等尺寸对话框
    &--medium:not(.is-fullscreen) {
      width: 720px;
      max-height: 600px;
      min-height: 400px;
    }

    // 大尺寸对话框
    &--large:not(.is-fullscreen) {
      width: 960px;
      max-height: 80vh;
      min-height: 400px;
    }

    // 提示性对话框
    &--tips:not(.is-fullscreen) {
      min-width: 480px;
      max-width: 960px;
      max-height: 600px;
      min-height: 400px;
    }

    /**
     * 对话框主体内容区域
     */
    &__body {
      flex-grow: 1;
      padding: 24px 30px;
      min-height: 100px;
      position: relative;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      @include scrollbar();
    }

    /**
     * 对话框头部区域
     */
    &__header {
      padding: 0;
      position: relative;
    }

    /**
     * 对话框底部区域
     */
    &__footer {
      padding: 16px 30px 20px;

      .el-button {
        padding: 8px 20px;
        min-width: 80px;
      }
    }
  }
}
</style>
<style lang="scss">
.el-dialog__body.x-dialog__loading,
.el-dialog__footer.x-dialog__loading {
  overflow-x: hidden;
  overflow-y: hidden;
}
</style>
