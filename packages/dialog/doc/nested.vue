<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 15:52:35
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-23 14:48:53
 * @FilePath: /vite-element-components/packages/dialog/doc/nested.vue
 * @Description: 嵌套弹窗示例
-->
<template>
  <div class="component-view">
    <el-button @click="parentDialog.visible = true">打开父弹窗</el-button>

    <!-- 父弹窗 -->
    <x-dialog v-model="parentDialog.visible" :title="parentDialog.title" :actions="parentDialogActions">
      <div class="dialog-content">
        <p>这是父弹窗的内容</p>
        <el-button type="primary" @click="childDialog.visible = true">打开子弹窗</el-button>
      </div>
    </x-dialog>
    <!-- 子弹窗 -->
    <x-dialog v-model="childDialog.visible" :title="childDialog.title" :actions="childDialogActions">
      <div class="dialog-content">
        <p>这是子弹窗的内容</p>
        <el-button type="primary" @click="showMessage">弹出message</el-button>
      </div>
    </x-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      parentDialog: {
        visible: false,
        title: '父弹窗',
      },
      childDialog: {
        visible: false,
        title: '子弹窗',
      },
    };
  },
  computed: {
    parentDialogActions() {
      return [
        {
          isCancel: true,
          label: '关闭',
        },
        {
          label: '确定',
          type: 'primary',
          isLoading: true,
          onClick: ({ endLoading }) => {
            setTimeout(() => {
              this.parentDialog.visible = false;
              endLoading();
            }, 1000);
          },
        },
      ];
    },
    childDialogActions() {
      return [
        {
          isCancel: true,
        },
        {
          label: '确定',
          type: 'primary',
          isLoading: true,
          onClick: ({ endLoading }) => {
            setTimeout(() => {
              this.childDialog.visible = false;
              endLoading();
            }, 1000);
          },
        },
      ];
    },
  },
  methods: {
    showMessage() {
      this.$message('一二三四五看i去北京');
    },
  },
};
</script>

<style scoped>
.dialog-content {
  padding: 20px;
  text-align: center;
}
</style>
