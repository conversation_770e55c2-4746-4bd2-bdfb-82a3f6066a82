<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:44:18
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-24 11:10:15
 * @FilePath: /vite-element-components/packages/dialog/doc/table.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <el-button @click="updateDialog.visible = true">打开弹窗</el-button>
    <x-dialog
      v-model="updateDialog.visible"
      width="1200px"
      show-fullscreen
      :title="updateDialog.title"
      :actions="updateDialogActions"
    >
      <x-search-table
        ref="searchTable"
        v-model="searchForm"
        :initialize-data="false"
        :search-table-config="searchTableConfig"
        :fetch-table-function="fetchTableFunction"
      />
    </x-dialog>
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      searchForm: {},
      updateDialog: {
        visible: false,
        title: '我是弹窗标题',
        id: '',
        form: {},
      },
    };
  },
  computed: {
    // 搜索表格配置项
    searchTableConfig() {
      return {
        searchFormConfig: [
          {
            prop: 'name',
            label: '产品名称',
            component: 'input',
            formInputConfig: {
              placeholder: '请输入产品名称',
            },
          },
          {
            prop: 'category',
            label: '产品类别',
            component: 'select',
            formInputConfig: {
              options: [
                { label: '电子产品', value: '电子产品' },
                { label: '家居用品', value: '家居用品' },
                { label: '办公用品', value: '办公用品' },
              ],
            },
          },
          {
            prop: 'status',
            label: '状态',
            component: 'select',
            formInputConfig: {
              options: [
                { label: '在售', value: '在售' },
                { label: '已下架', value: '已下架' },
                { label: '待上架', value: '待上架' },
              ],
            },
          },
        ],
        tableColumnsConfig: [
          { type: 'checkbox' },
          { title: '产品名称', field: 'name' },
          { title: '价格', field: 'price', formatter: ({ cellValue }) => `¥${cellValue}` },
          { title: '类别', field: 'category' },
          {
            title: '状态',
            field: 'status',
            width: 100,
            slots: {
              default: ({ row }) => {
                const typeMap = {
                  在售: 'success',
                  已下架: 'info',
                  待上架: 'warning',
                };
                return <el-tag type={typeMap[row.status]}>{row.status}</el-tag>;
              },
            },
          },
          {
            type: 'actions',
            title: '操作',
            width: 100,
            actions: [
              {
                label: '查看',
                onClick: (row, index, e) => {
                  console.log(
                    '%c [  ]-125-「basic」',
                    'font-size:13px; background:pink; color:#bf2c9f;',
                    row,
                    index,
                    e,
                  );
                },
              },
              {
                label: '编辑',
                onClick: (row, index, e) => {},
              },
            ],
          },
        ],
      };
    },
    // 弹窗事件
    updateDialogActions() {
      return [
        {
          isCancel: true,
        },
        {
          label: '隐藏按钮',
          type: 'primary',
          show: false,
          isLoading: true,
          onClick: async ({ endLoading }) => {
            this.$refs.updateForm.validate((valid) => {
              if (!valid) return endLoading();
            });
          },
        },
        {
          label: '确定',
          type: 'primary',
          isLoading: true,
          onClick: async ({ endLoading }) => {
            this.$refs.updateForm.validate((valid) => {
              if (!valid) return endLoading();
              setTimeout(() => {
                this.updateDialog.visible = false;
                endLoading();
              }, 100);
            });
          },
        },
      ];
    },
  },
  watch: {
    'updateDialog.visible': function (val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.searchTable.search();
        });
      }
    },
  },
  methods: {
    fetchTableFunction(query, succeed) {
      console.log('%c [  ]-175-「table」', 'font-size:13px; background:pink; color:#bf2c9f;', 1);
      // 使用succeed回调返回结果
      return new Promise((resolve) => {
        const data = new Array(20).fill().map((item, index) => ({
          id: index + 1,
          name: `name${index + 1}`,
          price: Math.floor(Math.random() * 100),
          category: `address${index + 1}`,
        }));
        setTimeout(() => {
          succeed({
            records: data,
            total: data.length,
          });
          resolve();
        }, 400);
      });
    },
  },
};
</script>
