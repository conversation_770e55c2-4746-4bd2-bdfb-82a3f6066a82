<script>
  import BasicVue from './basic.vue';
  import TableVue from './table.vue';
  import FormVue from './form.vue';
  import FullScreenVue from './fullScreen.vue';
  import NestedVue from './nested.vue';
  import SmallVue from './small.vue';
  import MediumVue from './medium.vue';
  import TipsVue from './tips.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      TableVue,
      FormVue,
      FullScreenVue,
      NestedVue,
      SmallVue,
      MediumVue,
      TipsVue,
      Preview
    }
  };
</script>

## Dialog 组件

基于 Element UI 的 Dialog 组件扩展的弹出层组件，提供了更丰富的功能和更统一的交互体验。支持基础弹窗、表单弹窗、全屏弹窗等多种使用场景，并且完全兼容 Element UI Dialog 的所有属性。

### 基础用法

展示基础的弹窗（size=large）功能，包括标题、内容区域和底部按钮。支持自定义标题、内容和底部按钮的事件处理。

<basic-vue/>
<preview  comp-name='dialog' demo-name='basic'/>

### 弹窗内嵌 表格 用法

在弹窗中通过嵌套 x-search-table 组件，实现嵌套表格功能。这种方式适用于需要在弹窗中展示表格数据的场景。

<table-vue/>
<preview  comp-name='dialog' demo-name='table'/>

### 弹窗包含 form 用法

在弹窗中直接嵌套 xForm 组件，实现完整的表单功能。这种方式提供了更灵活的表单布局和验证功能。

<form-vue/>
<preview  comp-name='dialog' demo-name='form'/>

### 全屏弹窗用法

支持全屏展示的弹窗，适用于需要展示大量内容或复杂表单的场景。

<full-screen-vue/>
<preview  comp-name='dialog' demo-name='fullScreen'/>

### 嵌套弹窗

支持在一个弹窗内打开另一个弹窗，通过设置 `append-to-body` 属性可以避免多层弹窗的遮罩层叠加问题。

<nested-vue/>
<preview  comp-name='dialog' demo-name='nested'/>

### Small尺寸弹窗

适用于展示少量信息的场景，宽度固定为530px。

<small-vue/>
<preview  comp-name='dialog' demo-name='small'/>

### Medium尺寸弹窗

适用于中等信息量的场景，宽度固定为720px。

<medium-vue/>
<preview  comp-name='dialog' demo-name='medium'/>

### Tips尺寸弹窗

适用于提示信息的场景，宽度在480px到960px之间自适应。

<tips-vue/>
<preview  comp-name='dialog' demo-name='tips'/>

### Attributes

| 参数              | 说明                                                                            | 类型           | 可选值                  | 默认值 |
| ----------------- | ------------------------------------------------------------------------------- | -------------- | ----------------------- | ------ |
| value/v-model     | 是否显示 Dialog                                                                 | boolean        | —                       | —      |
| superOptions      | 对 dialog 处理的额外参数，使用 el-dialog 的属性                                 | object         | —                       | —      |
| showModel         | 是否需要遮罩层                                                                  | boolean        | false/true              | true   |
| appendToBody      | 遮罩层是否插入至 body 元素上                                                    | boolean        | false/true              | false  |
| title             | Dialog 标题                                                                     | string         | —                       | —      |
| destroy           | 关闭时销毁 Dialog 中的元素                                                      | boolean        | false/true              | false  |
| width             | Dialog 宽度，不同size下有不同的默认宽度                                         | string         | —                       | 960px  |
| height            | Dialog 高度                                                                     | string         | —                       | —      |
| top               | dialog 的顶部距离                                                               | string         | —                       | 15vh   |
| actions           | dialog 底部按钮的事件配置                                                       | array          | —                       | —      |
| beforeClose       | 关闭 dialog 前的回调函数，若返回 false 或者返回 Promise 且被 reject，则阻止关闭 | function(done) | —                       | —      |
| dialogLoading     | 弹窗是否显示加载状态                                                            | boolean        | —                       | false  |
| dialogLoadingText | 弹窗加载状态文字                                                                | string         | —                       | ''     |
| fullScreen        | 是否启用全屏模式                                                                | string         | —                       | false  |
| mode              | 弹窗显示模式，normal为普通模式，full-screen为全屏模式                           | string         | normal/full-screen      | normal |
| show-fullscreen   | 是否显示全屏按钮                                                                | boolean        | false/true              | false  |
| size              | 对话框大小                                                                      | string         | tips/small/medium/large | large  |

### Events

| 事件名称 | 说明              | 回调参数 |
| -------- | ----------------- | -------- |
| open     | Dialog 打开的回调 | —        |
| close    | Dialog 关闭的回调 | —        |
| confirm  | 确认按钮的回调    | —        |
| cancel   | 取消按钮的回调    | —        |

### Methods

| 方法名 | 说明               | 参数 |
| ------ | ------------------ | ---- |
| open   | 打开 Dialog 对话框 | —    |
| close  | 关闭 Dialog 对话框 | —    |

### Slots

| name   | 说明                                        |
| ------ | ------------------------------------------- |
| —      | Dialog 的内容                               |
| title  | Dialog 标题区的内容，会替换标题部分         |
| footer | Dialog 按钮操作区的内容，会替换默认的按钮区 |

### Actions 配置项

底部按钮的配置项，类型为数组，每个按钮支持以下属性：

| 参数      | 说明                 | 类型     | 可选值                         | 默认值 |
| --------- | -------------------- | -------- | ------------------------------ | ------ |
| label     | 按钮文本             | string   | —                              | —      |
| type      | 按钮主题             | string   | primary/success/warning/danger | —      |
| isLoading | 是否显示加载状态     | boolean  | —                              | false  |
| show      | 是否显示按钮         | boolean  | —                              | true   |
| isCancel  | 是否为取消按钮       | boolean  | —                              | false  |
| onClick   | 按钮点击事件处理函数 | function | —                              | —      |

```js
export default {
  computed: {
    dialogActions() {
      return [
        {
          label: '提交',
          type: 'primary',
          show: true,
          onClick: ({ endLoading }) => {
            // 业务逻辑...
          },
        },
      ];
    },
  },
};
```
