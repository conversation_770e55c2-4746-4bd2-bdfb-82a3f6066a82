<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-03 11:31:02
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-03 11:31:05
 * @FilePath: /vite-element-components/packages/dialog/doc/tips.vue
 * @Description:
-->
<template>
  <div>
    <x-button @click="showTipsDialog = true">打开Tips弹窗</x-button>
    <x-dialog :show="showTipsDialog" title="提示信息" size="tips" @update:show="(val) => (showTipsDialog = val)">
      <div style="padding: 20px">
        <p>这是一个Tips尺寸的弹窗示例</p>
        <p>宽度在480px到960px之间自适应</p>
      </div>
    </x-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      showTipsDialog: false,
    };
  },
};
</script>
