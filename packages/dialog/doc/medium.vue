<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-03 11:30:43
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-03 11:30:46
 * @FilePath: /vite-element-components/packages/dialog/doc/medium.vue
 * @Description:
-->
<template>
  <div>
    <x-button @click="showMediumDialog = true">打开Medium弹窗</x-button>
    <x-dialog
      :show="showMediumDialog"
      title="Medium尺寸弹窗"
      size="medium"
      @update:show="(val) => (showMediumDialog = val)"
    >
      <div style="padding: 20px">
        <p>这是一个Medium尺寸的弹窗示例</p>
        <p>宽度固定为720px</p>
      </div>
    </x-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      showMediumDialog: false,
    };
  },
};
</script>
