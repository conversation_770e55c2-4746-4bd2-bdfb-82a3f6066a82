<template>
  <div class="component-view">
    <el-button @click="updateDialog.visible = true">打开弹窗</el-button>
    <x-dialog
      v-model="updateDialog.visible"
      :dialog-loading="updateDialog.loading"
      :title="updateDialog.title"
      fullscreen
    >
      <x-form ref="baseForm" v-model="updateDialog.form" :form-config="formConfig">
        <x-form-items slot="formAppend" v-model="updateDialog.form" :form-config="formConfig"></x-form-items>
        <x-form-items slot="formAppend" v-model="updateDialog.form" :form-config="formConfig"></x-form-items>
        <x-form-items slot="formAppend" v-model="updateDialog.form" :form-config="formConfig"></x-form-items>
        <x-form-items slot="formAppend" v-model="updateDialog.form" :form-config="formConfig"></x-form-items>
        <x-form-items slot="formAppend" v-model="updateDialog.form" :form-config="formConfig"></x-form-items>
        <x-form-items slot="formAppend" v-model="updateDialog.form" :form-config="formConfig"></x-form-items>
        <x-form-items slot="formAppend" v-model="updateDialog.form" :form-config="formConfig"></x-form-items>
        <x-edit-table
          title="我是标题"
          slot="formAppend"
          v-model="updateDialog.form.tableData"
          table-data-name="tableData"
          :initd-min-row="1"
          max="5"
          height="600px"
          :table-columns="tableColumns"
        ></x-edit-table>
        <x-edit-table
          title="我是标题"
          slot="formAppend"
          v-model="updateDialog.form.tableData1"
          table-data-name="tableData1"
          :initd-min-row="1"
          max="5"
          height="600px"
          :table-columns="tableColumns"
        ></x-edit-table>
        <x-edit-table
          title="我是标题"
          slot="formAppend"
          v-model="updateDialog.form.tableData2"
          table-data-name="tableData2"
          :initd-min-row="1"
          max="5"
          height="600px"
          :table-columns="tableColumns"
        ></x-edit-table>
        <x-edit-table
          title="我是标题"
          slot="formAppend"
          v-model="updateDialog.form.tableData3"
          table-data-name="tableData3"
          :initd-min-row="1"
          max="5"
          height="600px"
          :table-columns="tableColumns"
        ></x-edit-table>
      </x-form>
    </x-dialog>
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      updateDialog: {
        loading: false,
        visible: false,
        title: '我是全屏标题',
        id: '',
        form: {
          数字输入: 222,
          tableData: [],
          tableData1: [],
          tableData2: [],
          tableData3: [],
        },
      },
    };
  },
  computed: {
    formConfig() {
      return [
        {
          prop: '禁用输入框',
          label: '禁用输入框',
          component: 'input',
          isRequired: 1,
          formItemSpan: 12,
          formInputConfig: {
            // disabled: true,
            slots: {
              append: '万元',
            },
          },
        },
        {
          prop: '普通输入框',
          label: '普通输入框',
          component: 'input',
          isRequired: 1,
          formItemSpan: 12,
          formInputConfig: {
            maxlength: 10,
            showWordLimit: true,
            slots: {
              append: '万元',
            },
          },
        },
        {
          prop: '多行文本框',
          label: '多行文本框',
          component: 'input',
          isRequired: 1,
          formInputConfig: {
            type: 'textarea',
            maxlength: 100,
            showWordLimit: true,
          },
        },

        {
          prop: '异步下拉',
          label: '异步下拉',
          component: 'select',
          formItemSpan: 12,
          formInputConfig: {
            keys: {
              value: 'value1',
              label: 'label1',
            },
            remoteMethod: () => [
              { label1: '张三', value1: 1 },
              { label1: '李四', value1: 2 },
              { label1: '王五', value1: 3 },
            ],
          },
        },
        {
          label: '普通日期',
          prop: '普通日期',
          component: 'date',
          formItemSpan: 12,
          formInputConfig: {
            type: 'date',
          },
        },
        {
          label: '日期时间',
          prop: '日期时间',
          component: 'date',
          formItemSpan: 12,
          formInputConfig: {
            type: 'datetime',
          },
        },
        {
          label: '日期区间',
          prop: '日期区间',
          component: 'date',
          formItemSpan: 12,
          formInputConfig: {
            type: 'dateRange',
          },
        },
        {
          label: '月份区间',
          prop: '月份区间',
          component: 'date',
          formItemSpan: 12,
          formInputConfig: {
            type: 'monthRange',
          },
        },
        {
          label: '时间区间',
          prop: '时间区间',
          component: 'date',
          formItemSpan: 12,
          formInputConfig: {
            type: 'datetimeRange',
          },
        },
        {
          label: '选择年份',
          prop: '选择年份',
          component: 'date',
          formItemSpan: 12,
          formInputConfig: {
            type: 'year',
          },
        },
        {
          label: '选择月份',
          prop: '选择月份',
          component: 'date',
          formItemSpan: 12,
          formInputConfig: {
            type: 'month',
          },
        },
        {
          label: '选择季度',
          prop: '选择季度',
          component: 'date',
          formItemSpan: 12,
          formInputConfig: {
            type: 'quarter',
          },
        },
        {
          label: '选择周',
          prop: '选择周',
          component: 'date',
          formItemSpan: 12,
          formInputConfig: {
            type: 'week',
          },
        },
        {
          label: '日期时间戳',
          prop: '日期时间戳',
          component: 'date',
          formItemSpan: 12,
          formInputConfig: {
            type: 'datetime',
            valueType: 'time-stamp',
          },
        },
        {
          label: '数字输入',
          prop: '数字输入',
          component: 'inputNumber',
          formItemSpan: 12,
          formInputConfig: {
            label: '机器',
            placeholder: '请输入机器数量',
            suffix: '台',
            digits: '3_2',
          },
        },
        {
          label: '无控制数字',
          prop: '无控制数字',
          component: 'inputNumber',
          formItemSpan: 12,
          formInputConfig: {
            suffix: '个',
            digits: '3_2',
            theme: 'normal',
          },
        },
        {
          label: '单选框',
          prop: '单选框',
          component: 'radio',
          formItemSpan: 12,
          formInputConfig: {
            allowUncheck: true,
            options: [
              {
                value: 1,
                label: '西瓜',
              },
              {
                value: 2,
                label: '香蕉',
              },
              {
                value: 3,
                label: '牛奶',
                disabled: true,
              },
            ],
          },
        },
        {
          label: '复选框',
          prop: '复选框',
          component: 'checkbox',
          formItemSpan: 12,
          formInputConfig: {
            options: [
              {
                value: 1,
                label: '香蕉',
              },
              {
                value: 2,
                label: '苹果',
              },
              {
                value: 3,
                label: '西瓜',
              },
            ],
          },
        },

        // {
        //   label: '上传组件',
        //   prop: '上传组件',
        //   component: 'upload',
        //   formItemSpan: 12,
        //   formInputConfig: {
        //     action: 'https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo',
        //     theme: 'image',
        //   },
        // },
      ];
    },
    tableColumns() {
      const selectOptions = [
        { label: '章三', value: 1 },
        { label: '李四', value: 2 },
        { label: '王武', value: 3 },
      ];
      return [
        {
          prop: 'aa',
          label: 'aa',
          visible: false,
        },
        {
          prop: '下拉框',
          label: '下拉框',
          component: 'select',
          width: '180px',
          isRequired: 1,
          formInputConfig: {
            options: selectOptions,
          },
        },
        {
          prop: '文本域',
          label: '文本域',
          component: 'input',
          width: '180px',
          showOverflow: false,
          isRequired: 1,
          formInputConfig: {},
        },
        {
          prop: 'aa3',
          label: 'aa3',
          component: 'input',
          width: '180px',
          isRequired: 1,
          formInputConfig: {
            slots: {
              suffix: () => <i class="el-icon-plus" onClick={(e, b) => {}} />,
            },
          },
          events: {
            blur: (e, val) => {
              console.log('%c [  ]-123-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', e, val);
            },
          },
          filters: [{ data: '' }],
          filterRender: {
            name: 'VxeInput',
          },
        },
        {
          prop: 'aa311',
          label: 'aa312',
          component: 'inputNumber',
          width: '180px',
          isRequired: 1,
          events: {
            blur: (e, val) => {
              console.log('%c [  ]-123-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', e, val);
            },
          },
          filters: [{ data: '' }],
          filterRender: {
            name: 'VxeNumberInput',
          },
        },
        {
          prop: '开关',
          label: '开关',
          component: 'switch',
          isRequired: 1,
          formInputConfig: {},
        },
        {
          prop: '普通下拉',
          label: '普通下拉',
          component: 'select',
          formInputConfig: {
            options: [
              { value: 1, label: '一' },
              { value: 2, label: '二' },
              { value: 3, label: '三' },
              { value: 42, label: '四' },
              { value: 5, label: '五' },
            ],
            multiple: true,
          },
          filters: [{ data: '' }],
          filterRender: {
            name: 'VxeSelect',
            options: [
              { value: 1, label: '一' },
              { value: 2, label: '二' },
              { value: 3, label: '三' },
              { value: 42, label: '四' },
              { value: 5, label: '五' },
            ],
            multiple: true,
          },
        },
        {
          label: '普通日期',
          prop: '普通日期',
          component: 'date',
          width: '180px',
          formInputConfig: {
            type: 'date',
          },
          filters: [{ data: '' }],
          filterRender: {
            name: 'VxeDatePicker',
          },
        },
        {
          label: '数字输入',
          prop: '数字输入',
          component: 'inputNumber',
          width: '180px',
          isRequired: 1,
          formInputConfig: {
            digits: '2_2',
          },
        },
      ];
    },
  },
};
</script>
