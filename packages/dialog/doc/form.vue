<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-18 11:46:13
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 11:02:43
 * @FilePath: /vite-element-components/packages/dialog/doc/form.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <el-button @click="handleOpen">打开弹窗</el-button>
    <x-dialog
      v-model="updateDialog.visible"
      :title="updateDialog.title"
      :dialog-loading="updateDialog.loading"
      :actions="updateDialogActions"
    >
      {{ updateDialog.form }}
      <x-form ref="updateForm" v-model="updateDialog.form" :form-config="formConfig" />
    </x-dialog>
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      updateDialog: {
        loading: false,
        visible: false,
        title: '我是弹窗1标题',
        id: '',
        form: {
          a: 1,
          b: 2,
        },
      },
    };
  },
  computed: {
    formConfig() {
      return [
        // {
        //   label: '分值区间',
        //   prop: 'scoreChangeValue',
        //   component: 'inputNumberRange',
        //   isRequired: 1,
        // },
        // {
        //   prop: '自定义输入框',
        //   label: '自定义输入框',
        //   component: 'input',
        //   isRequired: 1,
        //   formItemSpan: 24,
        //   formInputConfig: {
        //     readonly: true,
        //     slots: {
        //       suffix: () => (
        //         <i
        //           class="el-icon-plus"
        //           style="cursor: pointer;border: 1px solid #ccc;border-radius: 4px;padding: 2px;margin-left: 5px;"
        //           onClick={() => {
        //             if (!this.updateDialog.form.自定义输入框) {
        //               this.updateDialog.form.自定义输入框 = '我是自定义输入框';
        //             } else {
        //               this.updateDialog.form.自定义输入框 = '';
        //             }
        //           }}
        //         />
        //       ),
        //     },
        //   },
        // },
        // {
        //   prop: '下拉树',
        //   label: '下拉树',
        //   component: 'selectTree',
        //   isRequired: 1,
        //   formItemSpan: 12,
        //   formInputConfig: {
        //     options: [
        //       {
        //         label: '选项1',
        //         value: '1',
        //       },
        //       {
        //         label: '选项2',
        //         value: '2',
        //       },
        //     ],
        //     multiple: true,
        //   },
        // },
        {
          prop: '下拉树单选',
          label: '下拉树单选',
          component: 'selectTree',
          isRequired: 1,
          formItemSpan: 12,
          formInputConfig: {
            options: [
              {
                label: '选项1',
                value: '1',
              },
              {
                label: '选项2',
                value: '2',
              },
            ],
          },
        },
        {
          prop: '多选下拉',
          label: '多选下拉',
          component: 'select',
          isRequired: 1,
          formItemSpan: 12,
          formInputConfig: {
            options: [
              {
                label: '选项1',
                value: '1',
              },
              {
                label: '选项2',
                value: '2',
              },
            ],
            multiple: true,
          },
        },
        {
          prop: '单选下拉',
          label: '单选下拉',
          component: 'select',
          isRequired: 1,
          formItemSpan: 12,
          formInputConfig: {
            options: [
              {
                label: '选项1',
                value: '1',
              },
              {
                label: '选项2',
                value: '2',
              },
            ],
          },
        },
        {
          prop: '数字输入',
          label: '数字输入',
          component: 'inputNumber',
          isRequired: 1,
          formItemSpan: 12,
          formInputConfig: {
            min: 1,
            max: 10,
          },
        },
        // {
        //   prop: '禁用输入框',
        //   label: '禁用输入框',
        //   component: 'input',
        //   isRequired: 1,
        //   formItemSpan: 12,
        //   formInputConfig: {
        //     // disabled: true,
        //     slots: {
        //       prepend: () => '前缀',
        //     },
        //   },
        //   slots: {
        //     right: () => (
        //       <el-tooltip content="我是提示">
        //         <i class="el-icon-warning"></i>
        //       </el-tooltip>
        //     ),
        //   },
        // },
        // {
        //   prop: 'table',
        //   label: '编辑表格',
        //   component: 'custom',
        //   isRequired: 1,
        //   formItemSpan: 24,
        //   formInputConfig: {
        //     render: () => (
        //       <x-edit-table
        //         vModel={this.updateDialog.form.table}
        //         draggable
        //         tableDataName="table"
        //         table-columns={this.tableColumns}
        //       />
        //     ),
        //   },
        // },
      ];
    },
    // 弹窗事件
    updateDialogActions() {
      return [
        {
          isCancel: true,
        },
        {
          label: '确定',
          type: 'primary',
          isLoading: true,
          onClick: async ({ endLoading }) => {
            this.$refs.updateForm.validate((valid) => {
              if (!valid) return endLoading();
            });
          },
        },
      ];
    },
    tableColumns() {
      return [
        {
          label: '节点顺序',
          type: 'seq',
          width: 78,
        },
        {
          label: '节点名称',
          prop: 'nodeName',
          isRequired: 1,
          component: 'input',
        },
        {
          label: '权重(%)',
          prop: 'weight',
          isRequired: 1,
          component: 'inputNumber',
          formInputConfig: {
            min: 0,
            max: 100,
          },
        },
        // {
        //   label: '评分持续时间（天）',
        //   prop: 'keepTime',
        //   isRequired: 1,
        //   component: 'inputNumber',
        //   formInputConfig: {
        //     min: 0,
        //   },
        //   events: {
        //     change: (val) => {
        //       this.computedDays();
        //     },
        //   },
        // },
        // {
        //   ruleMessage: '请选择评分模板',
        //   label: '评分模板',
        //   prop: 'tempName',
        //   component: 'input',
        //   isRequired: 1,
        //   formInputConfig: {
        //     placeholder: '请选择评分模板',
        //     disabled: true,
        //   },
        // },
      ];
    },
  },
  methods: {
    handleOpen() {
      this.updateDialog.loading = true;
      this.updateDialog.visible = true;
      setTimeout(() => {
        this.updateDialog.loading = false;
        this.$nextTick(() => {
          this.$refs.updateForm.init({
            数字输入: 1,
          });
        });
      }, 3000);
    },
  },
};
</script>
