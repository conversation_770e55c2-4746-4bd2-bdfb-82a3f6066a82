<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-03 11:30:26
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-03 11:30:29
 * @FilePath: /vite-element-components/packages/dialog/doc/small.vue
 * @Description:
-->
<template>
  <div>
    <x-button @click="showSmallDialog = true">打开Small弹窗</x-button>
    <x-dialog
      :show="showSmallDialog"
      title="Small尺寸弹窗"
      size="small"
      @update:show="(val) => (showSmallDialog = val)"
    >
      <div style="padding: 20px">
        <p>这是一个Small尺寸的弹窗示例</p>
        <p>宽度固定为530px</p>
      </div>
    </x-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      showSmallDialog: false,
    };
  },
};
</script>
