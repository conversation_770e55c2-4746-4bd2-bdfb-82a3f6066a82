<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-24 17:02:36
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-17 13:57:39
 * @FilePath: /vite-element-components/packages/button/doc/events.vue
 * @Description:
-->

<template>
  <div class="component-view">
    <x-button
      label="点击触发loading，3秒后停止loading"
      icon="el-icon-plus"
      type="warning"
      is-loading
      :on-click="buttonClick"
    />
    <x-button label="动态条件显示" :show="handleShow" :params="params" :on-click="handleClick"></x-button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      params: { a: 1, b: 2 },
    };
  },
  methods: {
    buttonClick({ endLoading }) {
      setTimeout(() => {
        endLoading();
      }, 3000);
    },
    handleShow(params) {
      console.log('%c [  ]-43-「events」', 'font-size:13px; background:pink; color:#bf2c9f;', params.a);
      return params.a !== 2;
    },
    handleClick(context) {
      console.log('%c [  ]-47-「events」', 'font-size:13px; background:pink; color:#bf2c9f;', context);
    },
  },
};
</script>
