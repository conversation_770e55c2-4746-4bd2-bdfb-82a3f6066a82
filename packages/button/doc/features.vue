<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 14:30:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-17 13:52:54
 * @FilePath: /vite-element-components/packages/button/doc/features.vue
 * @Description: 按钮组件特性示例
-->

<template>
  <div class="component-view">
    <div class="feature-section">
      <h4>按钮尺寸</h4>
      <x-button size="medium" type="primary">中等按钮</x-button>
      <x-button size="small" type="primary">小型按钮</x-button>
      <x-button size="mini" type="primary">迷你按钮</x-button>
    </div>

    <div class="feature-section">
      <h4>图标按钮</h4>
      <x-button icon="el-icon-edit" type="primary">编辑</x-button>
      <x-button icon="el-icon-share" type="success">分享</x-button>
      <x-button icon="el-icon-delete" type="danger">删除</x-button>
      <x-button icon="el-icon-search">搜索</x-button>
    </div>

    <div class="feature-section">
      <h4>禁用状态</h4>
      <x-button disabled type="primary">禁用按钮</x-button>
      <x-button :disabled="isDisabled" type="success">动态禁用</x-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isDisabled: true,
    };
  },
  methods: {},
};
</script>

<style scoped>
.feature-section {
  margin-bottom: 20px;
}
.feature-section h4 {
  margin-bottom: 10px;
  color: #606266;
}
.component-view .x-button {
  margin-right: 10px;
}
</style>
