<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-24 17:02:36
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-17 13:53:44
 * @FilePath: /vite-element-components/packages/button/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import EventsVue from './events.vue';
  import FeaturesVue from './features.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      EventsVue,
      FeaturesVue,
      Preview
    }
  };
</script>

## XButton 组件

基于 Element UI 的 Button 组件进行二次封装，提供更便捷的配置方式和更丰富的功能。支持按钮类型、尺寸、图标、加载状态等特性，同时提供了更灵活的参数传递和状态控制机制。

### 特性

- 🎨 **丰富的样式** - 支持多种按钮类型和主题样式
- 🔧 **灵活配置** - 支持函数式的动态显示和禁用控制
- ⚡ **加载状态** - 内置加载状态管理，支持异步操作
- 📦 **参数传递** - 便捷的参数传递机制，适用于表格操作等场景
- 🔄 **事件处理** - 灵活的事件处理机制，支持自定义上下文

### 使用场景

- 需要通过配置对象统一管理按钮属性的场景
- 需要动态控制按钮显示/隐藏、禁用状态的场景
- 需要处理按钮加载状态的场景
- 需要传递额外参数到点击事件处理函数的场景
- 表格操作列中的动态按钮配置
- 表单提交按钮的状态管理

### 基础用法

兼容 element-eoss 组件库 button 组件的所有属性。

<basic-vue/>
<preview  comp-name='button' demo-name='basic'/>

### 特性示例

展示按钮组件的各种特性，包括尺寸、图标、禁用状态和参数传递等。

<features-vue/>
<preview  comp-name='button' demo-name='features'/>

### 复杂用法

组件配置点击事件和加载状态控制。

<events-vue/>
<preview  comp-name='button' demo-name='events'/>

### API

#### Props

| 参数      | 说明                       | 类型             | 可选值                                                       | 默认值  | 版本 |
| --------- | -------------------------- | ---------------- | ------------------------------------------------------------ | ------- | ---- |
| label     | 按钮显示的文本             | string           | —                                                            | ''      | —    |
| type      | 按钮类型                   | string           | default / primary / success / warning / danger / info / text | default | —    |
| theme     | 按钮主题样式               | string           | default / danger                                             | default | —    |
| size      | 按钮尺寸                   | string           | large / medium / small / mini                                | —       | —    |
| disabled  | 是否禁用，支持函数动态计算 | boolean/function | —                                                            | false   | —    |
| show      | 是否显示，支持函数动态计算 | boolean/function | —                                                            | true    | —    |
| icon      | 图标类名                   | string           | Element UI 内置图标类名                                      | ''      | —    |
| isLoading | 是否启用加载状态控制       | boolean          | —                                                            | false   | —    |
| params    | 传递给事件处理函数的参数   | array/object     | —                                                            | []      | —    |
| onClick   | 点击事件处理函数           | function         | —                                                            | null    | —    |

#### 继承属性

XButton 组件继承了 Element UI Button 组件的所有属性，包括但不限于：

- `plain` - 是否朴素按钮
- `round` - 是否圆角按钮
- `circle` - 是否圆形按钮
- `loading` - 是否加载中状态（注意：建议使用 `isLoading` 配合内置的加载状态管理）
- `autofocus` - 是否默认聚焦
- `nativeType` - 原生 type 属性

#### Events

| 事件名 | 说明           | 回调参数                                                                        |
| ------ | -------------- | ------------------------------------------------------------------------------- |
| click  | 点击按钮时触发 | (context: EventContext) - 事件上下文对象，包含 $event、startLoading、endLoading |

#### Slots

| 插槽名    | 说明         | 参数 |
| --------- | ------------ | ---- |
| default   | 按钮内容     | —    |
| rightSlot | 按钮右侧内容 | —    |

### 高级用法

#### 动态显示和禁用

支持使用函数来动态控制按钮的显示和禁用状态：

```javascript
// 动态禁用示例
disabled: (params) => {
  return params.some((item) => item.status === 'disabled');
};

// 动态显示示例
show: (params) => {
  return params.length > 0 && params.every((item) => item.visible);
};
```

#### 加载状态管理

内置的加载状态管理，适用于异步操作：

```javascript
// 启用加载状态控制
<x-button
  :isLoading="true"
  :onClick="handleAsyncOperation"
>
  提交数据
</x-button>

// 处理函数
const handleAsyncOperation = ({ startLoading, endLoading },params, ) => {
  startLoading(); // 开始加载

  // 模拟异步操作
  api.submitData(params)
    .then(response => {
      console.log('操作成功', response);
    })
    .catch(error => {
      console.error('操作失败', error);
    })
    .finally(() => {
      endLoading(); // 结束加载
    });
}
```

### 最佳实践

1. **参数传递**: 使用 `params` 属性传递必要的数据，避免在事件处理函数中访问外部变量
2. **加载状态**: 对于异步操作，建议使用 `isLoading` 配合内置的加载状态管理
3. **动态控制**: 充分利用函数式的 `show` 和 `disabled` 属性来实现复杂的业务逻辑
4. **性能优化**: 对于频繁变化的动态属性，考虑使用计算属性或缓存机制

### 常见问题

#### Q: 为什么点击事件没有触发？

A: 检查是否设置了 `show` 属性为 `false` 或函数返回 `false`，隐藏的按钮不会触发点击事件。

#### Q: 加载状态无法正常工作？

A: 确保设置了 `isLoading="true"`，并且在异步操作完成后调用了 `endLoading()` 函数。

#### Q: 动态禁用不生效？

A: 检查 `disabled` 函数是否正确返回布尔值，以及 `params` 参数是否正确传递。

### 更新日志

- **v1.0.0**: 初始版本，基础功能实现
- **v1.0.12**: 重构代码结构，完善类型定义，优化性能
