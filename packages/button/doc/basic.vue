<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-24 17:02:36
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-10 13:57:33
 * @FilePath: /vite-element-components/packages/button/doc/basic.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <x-button type="default">默认按钮</x-button>
    <x-button type="primary">主题按钮</x-button>
    <x-button type="success">成功按钮</x-button>
    <x-button type="warning">警告按钮</x-button>
    <x-button type="danger">危险按钮</x-button>
    <x-button type="text">文字按钮</x-button>
  </div>
</template>
