/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-14 16:44:12
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-15 18:00:00
 * @FilePath: /vite-element-components/packages/button/utils.js
 * @Description: XButton 组件工具函数
 */

/**
 * XButton 组件工具函数
 * <AUTHOR>
 * @description 提供按钮组件相关的工具函数和辅助方法
 */

import { createEventContext, generateClasses } from '../shared';
import { CSS_PREFIX } from './constants';

// ==================== 业务相关方法 ====================

/**
 * 创建按钮事件上下文对象
 * @param {Event} event 原生事件对象
 * @param {Function} startLoading 开始加载函数
 * @param {Function} endLoading 结束加载函数
 * @returns {Object} 事件上下文
 */
export function createButtonEventContext(event, startLoading, endLoading) {
  return createEventContext(event, {
    startLoading,
    endLoading,
  });
}

/**
 * 生成按钮样式类名
 * @param {string} theme 按钮主题
 * @param {Object} extraClasses 额外的类名对象
 * @returns {Object} 样式类名对象
 */
export function generateButtonClasses(theme, extraClasses = {}) {
  return generateClasses(
    CSS_PREFIX,
    {
      [theme]: theme && theme !== 'default',
    },
    extraClasses,
  );
}
