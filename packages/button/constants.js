/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-14 14:48:39
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-16 09:19:16
 * @FilePath: /vite-element-components/packages/button/constants.js
 * @Description: XButton 组件常量定义
 */

/**
 * XButton 组件常量定义
 * <AUTHOR>
 * @description 定义按钮组件相关的常量和枚举值
 */

import { COMMON_EVENTS, CSS_PREFIXES } from '../shared';

/**
 * 按钮类型枚举
 * @type {string[]}
 */
export const BUTTON_TYPES = ['default', 'primary', 'success', 'warning', 'danger', 'info', 'text'];

/**
 * 按钮主题枚举
 * @type {string[]}
 */
export const BUTTON_THEMES = ['default', 'danger'];

/**
 * 按钮组件默认配置
 */
export const DEFAULT_CONFIG = {
  size: '',
  mode: 'edit',
  disabled: false,
  show: true,
  params: [],
  type: 'default',
  theme: 'default',
  isLoading: false,
  label: '',
  icon: '',
};

/**
 * 事件名称常量 (从公共模块导入)
 */
export const EVENTS = {
  CLICK: COMMON_EVENTS.CLICK,
  UPDATE_VALUE: COMMON_EVENTS.UPDATE_VALUE,
};

/**
 * CSS 类名前缀 (从公共模块导入)
 */
export const CSS_PREFIX = CSS_PREFIXES.BUTTON;

/**
 * 主题样式映射
 */
export const THEME_CLASS_MAP = {
  default: `${CSS_PREFIX}-default`,
  danger: `${CSS_PREFIX}-danger`,
};
