<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-24 17:02:36
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-13 11:01:23
 * @FilePath: /vite-element-components/packages/button/index.vue
 * @Description: XButton 组件 - 基于 Element UI 的按钮组件封装
 -->

<script lang="jsx">
import { BUTTON_TYPES, BUTTON_THEMES } from './constants';
import { COMPONENT_SIZES, generateComponentKey, computeVisibility, computeDisabled } from '../shared';
import { createButtonEventContext, generateButtonClasses } from './utils';

/**
 * XButton 组件
 * @component XButton
 * @description 基于 Element UI 的 Button 组件封装，提供更便捷的配置方式和更丰富的功能
 *
 * @features
 * - 支持多种按钮类型和主题样式
 * - 内置加载状态管理，支持异步操作
 * - 支持函数式的动态显示和禁用控制
 * - 便捷的参数传递机制
 *
 * @example
 * // 基础用法
 * <x-button type="primary">主要按钮</x-button>
 *
 * // 异步操作
 * <x-button :onClick="handleAsyncClick" :isLoading="true">异步按钮</x-button>
 *
 * // 动态控制
 * <x-button :show="(params) => params.visible" :disabled="(params) => params.disabled">动态按钮</x-button>
 */
export default {
  name: 'XButton',

  props: {
    /**
     * 按钮显示的文本
     * @type {String}
     */
    label: {
      type: String,
      default: '',
    },

    /**
     * 是否禁用按钮，支持函数动态计算
     * @type {Boolean|Function}
     */
    disabled: {
      type: [Boolean, Function],
      default: false,
    },

    /**
     * 按钮尺寸
     * @type {String}
     * @values large, medium, small, mini
     */
    size: {
      type: String,
      default: '',
      validator: (value) => !value || COMPONENT_SIZES.includes(value),
    },

    /**
     * 按钮类型
     * @type {String}
     * @values default, primary, success, warning, danger, info, text
     */
    type: {
      type: String,
      default: 'default',
      validator: (value) => BUTTON_TYPES.includes(value),
    },

    /**
     * 按钮主题样式
     * @type {String}
     * @values default, danger
     */
    theme: {
      type: String,
      default: 'default',
      validator: (value) => BUTTON_THEMES.includes(value),
    },

    /**
     * 是否显示按钮，支持函数动态计算
     * @type {Boolean|Function}
     */
    show: {
      type: [Boolean, Function],
      default: true,
    },

    /**
     * 是否启用加载状态控制
     * @type {Boolean}
     */
    isLoading: {
      type: Boolean,
      default: false,
    },

    /**
     * 点击事件处理函数
     * @type {Function}
     * @param {*} params 传递的参数
     * @param {Object} context 事件上下文
     */
    onClick: {
      type: Function,
      default: null,
    },

    /**
     * 按钮图标类名
     * @type {String}
     */
    icon: {
      type: String,
      default: '',
    },

    /**
     * 传递给事件处理函数的参数
     * @type {Array|Object}
     */
    params: {
      type: [Array, Object],
      default: () => [],
    },
    /**
     * 指令
     * @type {Array}
     */
    directives: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      /** 内部状态管理 */
      internalState: {
        loading: false,
      },
      uniqueKey: generateComponentKey('button'),
    };
  },

  computed: {
    /**
     * 计算是否显示按钮
     * @returns {Boolean} 是否显示
     */
    isVisible() {
      return computeVisibility(this.show, this.params);
    },

    /**
     * 计算是否禁用按钮
     * @returns {Boolean} 是否禁用
     */
    isDisabled() {
      return computeDisabled(this.disabled, this.params);
    },

    /**
     * 计算按钮样式类名
     * @returns {Object} 样式类名对象
     */
    buttonClasses() {
      return generateButtonClasses(this.theme);
    },
  },

  methods: {
    /**
     * 开始加载状态
     * @returns {Function} 启动加载的函数
     */
    startLoading() {
      let start;
      if (this.isLoading) {
        start = (func) => {
          this.internalState.loading = true;
          if (func) {
            func();
          }
          start = null;
        };
      } else {
        start = () => {
          console.warn('[XButton] 请设置 isLoading=true 以启用加载状态控制');
          start = null;
        };
      }
      return start;
    },

    /**
     * 结束加载状态
     * @returns {Function} 结束加载的函数
     */
    endLoading() {
      let end;
      if (this.isLoading) {
        this.internalState.loading = true;
        end = (func) => {
          this.internalState.loading = false;
          if (func) {
            func();
          }
          end = null;
        };
      } else {
        end = () => {
          console.warn('[XButton] 请设置 isLoading=true 以启用加载状态控制');
          end = null;
        };
      }
      return end;
    },

    /**
     * 处理按钮点击事件
     * @param {Event} event 原生点击事件
     */
    handleClick(event) {
      event.stopPropagation();

      const context = createButtonEventContext(event, this.startLoading(), this.endLoading());

      if (this.onClick) {
        this.onClick(...Array.from(this.params), context);
      } else {
        this.$emit('click', context);
      }
    },

    /**
     * 渲染默认插槽内容
     * @returns {VNode|String} 插槽内容
     */
    renderDefaultSlot() {
      if (this.label) {
        return this.label;
      }

      if (this.$scopedSlots.default) {
        return this.$scopedSlots.default();
      }

      return null;
    },

    /**
     * 渲染右侧插槽内容
     * @returns {VNode|null} 插槽内容
     */
    renderRightSlot() {
      return this.$scopedSlots.rightSlot ? this.$scopedSlots.rightSlot() : null;
    },
  },

  /**
   * 渲染函数
   * @returns {VNode|undefined} 渲染结果
   */
  render() {
    // 如果不显示，直接返回
    if (!this.isVisible) {
      return undefined;
    }

    const buttonProps = {
      ref: 'button',
      class: this.buttonClasses,
      props: {
        size: this.size,
        type: this.type,
        icon: this.icon,
        loading: this.internalState.loading,
        disabled: this.isDisabled,
        nativeType: 'button',
        ...this.$attrs,
      },
      directives: this.directives,
      on: {
        click: this.handleClick,
      },
      key: this.uniqueKey,
    };

    return (
      <el-button {...buttonProps}>
        {this.renderDefaultSlot()}
        {this.renderRightSlot()}
      </el-button>
    );
  },
};
</script>

<style lang="scss" scoped>
@import '../styles/index.scss';

/**
 * XButton 组件样式
 * 基于 Element UI Button 组件的样式扩展
 */
.x-button {
  // 基础样式继承 Element UI

  // 危险主题样式
  &.x-button-danger {
    &.el-button--text {
      color: #ff4d4f;

      &:hover,
      &:focus {
        color: #ff7875;
        background-color: transparent;
      }

      &:active {
        color: #f5222d;
        background-color: transparent;
      }

      &.is-disabled {
        color: #c0c4cc;

        &:hover,
        &:focus,
        &:active {
          color: #c0c4cc;
          background-color: transparent;
        }
      }
    }
  }
}

// 兼容旧版本样式
.el-button--text.x-button {
  &-danger {
    color: #ff4d4f;

    &:active {
      color: #f5222d;
    }

    &:hover,
    &:focus {
      color: #ff7875;
    }
  }
}
</style>
