<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-15 14:30:33
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-17 13:37:00
 * @FilePath: /vite-element-components/packages/button/README.md
 * @Description:
-->

# XButton 组件

基于 Element UI 的 Button 组件进行二次封装，提供更便捷的配置方式和更丰富的功能。

## 快速开始

```vue
<template>
  <div>
    <!-- 基础用法 -->
    <x-button type="primary">主要按钮</x-button>

    <!-- 带参数的点击事件 -->
    <x-button :params="{ id: 1, name: 'test' }" :onClick="handleClick"> 点击传参 </x-button>

    <!-- 异步操作按钮 -->
    <x-button :isLoading="true" :onClick="handleAsyncClick"> 异步操作 </x-button>

    <!-- 动态显示/禁用 -->
    <x-button
      :params="userData"
      :show="(params) => params.role === 'admin'"
      :disabled="(params) => params.status === 'inactive'"
    >
      管理员操作
    </x-button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      userData: {
        role: 'admin',
        status: 'active',
      },
    };
  },
  methods: {
    handleClick(context, params) {
      console.log('事件:', context);
      console.log('参数:', params);
    },

    handleAsyncClick({ startLoading, endLoading }, params) {
      startLoading();

      // 模拟异步操作
      setTimeout(() => {
        console.log('操作完成');
        endLoading();
      }, 2000);
    },
  },
};
</script>
```

## 主要特性

- 🎨 **丰富的样式** - 支持多种按钮类型和主题样式
- 🔧 **灵活配置** - 支持函数式的动态显示和禁用控制
- ⚡ **加载状态** - 内置加载状态管理，支持异步操作
- 📦 **参数传递** - 便捷的参数传递机制，适用于表格操作等场景

## API

### Props

| 参数      | 说明                       | 类型             | 默认值  |
| --------- | -------------------------- | ---------------- | ------- |
| label     | 按钮显示的文本             | string           | ''      |
| type      | 按钮类型                   | string           | default |
| theme     | 按钮主题样式               | string           | default |
| size      | 按钮尺寸                   | string           | —       |
| disabled  | 是否禁用，支持函数动态计算 | boolean/function | false   |
| show      | 是否显示，支持函数动态计算 | boolean/function | true    |
| icon      | 图标类名                   | string           | ''      |
| isLoading | 是否启用加载状态控制       | boolean          | false   |
| params    | 传递给事件处理函数的参数   | object           | null    |
| onClick   | 点击事件处理函数           | function         | null    |

### Events

| 事件名 | 说明           | 回调参数     |
| ------ | -------------- | ------------ |
| click  | 点击按钮时触发 | EventContext |

### Slots

| 插槽名    | 说明         |
| --------- | ------------ |
| default   | 按钮内容     |
| rightSlot | 按钮右侧内容 |

## 更多示例

查看 [完整文档](./doc/doc.md) 了解更多用法和示例。
