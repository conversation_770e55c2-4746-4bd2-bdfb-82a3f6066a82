/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-15 18:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-15 18:00:00
 * @FilePath: /vite-element-components/packages/shared/types.js
 * @Description: 组件库公共类型定义
 */

/**
 * 组件库公共类型定义
 * <AUTHOR>
 * @description 定义组件库中通用的类型和接口
 */

// ==================== 基础类型定义 ====================

/**
 * 组件基础配置类型
 * @typedef {Object} BaseComponentConfig
 * @property {string} size - 组件尺寸
 * @property {string} mode - 组件模式
 * @property {boolean|Function} disabled - 是否禁用
 * @property {boolean|Function} show - 是否显示
 * @property {Array|Object} params - 参数
 */

/**
 * 事件处理器类型
 * @typedef {Function} EventHandler
 * @param {*} value - 事件值
 * @param {*} params - 传递的参数
 * @param {Object} context - 事件上下文
 */

/**
 * 验证规则类型
 * @typedef {Object} ValidationRule
 * @property {boolean} required - 是否必填
 * @property {string} message - 错误消息
 * @property {string} trigger - 触发方式
 * @property {number} min - 最小长度
 * @property {number} max - 最大长度
 * @property {RegExp} pattern - 正则表达式
 * @property {Function} validator - 自定义验证器
 */

/**
 * 表单项配置类型
 * @typedef {Object} FormItemConfig
 * @property {string} type - 表单项类型
 * @property {string} prop - 字段名
 * @property {string} label - 标签文本
 * @property {*} defaultValue - 默认值
 * @property {Array<ValidationRule>} rules - 验证规则
 * @property {Object} props - 组件属性
 * @property {boolean} required - 是否必填
 * @property {boolean} show - 是否显示
 * @property {boolean} disabled - 是否禁用
 * @property {number} span - 栅格占位
 * @property {number} offset - 栅格左侧间隔
 * @property {Object} style - 样式
 * @property {Object} class - CSS类名
 */

/**
 * 按钮配置类型
 * @typedef {Object} ButtonConfig
 * @property {string} label - 按钮文本
 * @property {string} type - 按钮类型
 * @property {string} theme - 按钮主题
 * @property {string} size - 按钮尺寸
 * @property {string} icon - 按钮图标
 * @property {boolean} loading - 是否加载中
 * @property {boolean} disabled - 是否禁用
 * @property {boolean} show - 是否显示
 * @property {Function} onClick - 点击事件处理器
 * @property {Object} props - 额外属性
 */

/**
 * 级联选择器配置类型
 * @typedef {Object} CascaderProps
 * @property {string} expandTrigger - 展开触发方式
 * @property {boolean} multiple - 是否多选
 * @property {boolean} checkStrictly - 是否严格的选择任意一级选项
 * @property {boolean} emitPath - 是否返回由该节点所在的各级菜单的值所组成的数组
 * @property {boolean} lazy - 是否动态加载子节点
 * @property {Function} lazyLoad - 加载动态数据的函数
 * @property {string} value - 指定选项的值字段名
 * @property {string} label - 指定选项的标签字段名
 * @property {string} children - 指定选项的子选项字段名
 * @property {string} disabled - 指定选项的禁用字段名
 * @property {string} leaf - 指定选项的叶子节点字段名
 */

/**
 * 表格列配置类型
 * @typedef {Object} TableColumn
 * @property {string} prop - 对应列内容的字段名
 * @property {string} label - 显示的标题
 * @property {string|number} width - 对应列的宽度
 * @property {string|number} minWidth - 对应列的最小宽度
 * @property {boolean} fixed - 列是否固定
 * @property {boolean} resizable - 对应列是否可以通过拖拽改变宽度
 * @property {boolean} sortable - 对应列是否可以排序
 * @property {string} sortMethod - 排序方法
 * @property {string} sortBy - 排序字段
 * @property {Function} formatter - 格式化函数
 * @property {boolean} showOverflowTooltip - 是否隐藏多余内容并在hover时以tooltip的形式显示
 * @property {string} align - 对齐方式
 * @property {string} headerAlign - 表头对齐方式
 * @property {string} className - 列的className
 * @property {string} labelClassName - 当前列标题的自定义类名
 * @property {Function} renderHeader - 列标题Label区域渲染使用的Function
 * @property {Object} filters - 数据过滤的选项
 * @property {Function} filterMethod - 数据过滤使用的方法
 * @property {boolean} filterable - 该列是否可以筛选
 */

/**
 * 对话框操作按钮配置类型
 * @typedef {Object} DialogAction
 * @property {string} label - 按钮文本
 * @property {string} type - 按钮类型
 * @property {string} size - 按钮尺寸
 * @property {string} icon - 按钮图标
 * @property {boolean} loading - 是否加载中
 * @property {boolean} disabled - 是否禁用
 * @property {boolean} show - 是否显示
 * @property {Function} onClick - 点击事件处理器
 * @property {Object} props - 按钮属性
 */

/**
 * 选项数据类型
 * @typedef {Object} OptionItem
 * @property {string|number} value - 选项值
 * @property {string} label - 选项标签
 * @property {boolean} disabled - 是否禁用
 * @property {Array<OptionItem>} children - 子选项
 */

/**
 * 上传文件类型
 * @typedef {Object} UploadFile
 * @property {string} name - 文件名
 * @property {string} url - 文件URL
 * @property {string} status - 上传状态
 * @property {number} percentage - 上传进度
 * @property {File} raw - 原始文件对象
 * @property {string} uid - 唯一标识
 */

/**
 * 树形数据类型
 * @typedef {Object} TreeNode
 * @property {string|number} id - 节点ID
 * @property {string} label - 节点标签
 * @property {boolean} disabled - 是否禁用
 * @property {Array<TreeNode>} children - 子节点
 * @property {boolean} isLeaf - 是否为叶子节点
 * @property {*} data - 节点数据
 */

// ==================== 组件属性类型定义 ====================

/**
 * 按钮组件属性类型
 * @typedef {Object} ButtonProps
 * @property {string} label - 按钮文本
 * @property {boolean|Function} disabled - 是否禁用
 * @property {string} size - 按钮尺寸
 * @property {string} type - 按钮类型
 * @property {string} theme - 按钮主题
 * @property {boolean|Function} show - 是否显示
 * @property {boolean} isLoading - 是否启用加载状态
 * @property {Function} onClick - 点击事件处理器
 * @property {string} icon - 按钮图标
 * @property {Array|Object} params - 传递的参数
 */

/**
 * 级联选择器组件属性类型
 * @typedef {Object} CascaderComponentProps
 * @property {string|number|Array} value - 绑定值
 * @property {Array} options - 可选项数据源
 * @property {CascaderProps} props - 配置选项
 * @property {string} size - 组件尺寸
 * @property {string} mode - 编辑模式
 * @property {string} placeholder - 输入框占位文本
 * @property {boolean} clearable - 是否可清空
 * @property {boolean} filterable - 是否可搜索
 * @property {string} separator - 选项分隔符
 * @property {boolean} showAllLevels - 是否显示完整路径
 * @property {boolean} collapseTags - 多选时是否折叠Tag
 * @property {number} debounce - 搜索关键词输入的去抖延迟
 * @property {Function} beforeFilter - 筛选之前的钩子
 * @property {string} popperClass - 自定义浮层类名
 * @property {boolean|Function} disabled - 是否禁用
 * @property {boolean|Function} show - 是否显示
 * @property {Array|Object} params - 传递的参数
 * @property {Function} onChange - 值改变事件处理器
 * @property {Function} onExpandChange - 展开节点变化事件处理器
 * @property {Function} onBlur - 失去焦点事件处理器
 * @property {Function} onFocus - 获得焦点事件处理器
 * @property {Function} onVisibleChange - 下拉框显示/隐藏事件处理器
 * @property {Function} onRemoveTag - 移除Tag事件处理器
 */

/**
 * 表单组件属性类型
 * @typedef {Object} FormProps
 * @property {Object} formData - 表单数据
 * @property {Array<FormItemConfig>} formItems - 表单项配置
 * @property {string} labelWidth - 标签宽度
 * @property {string} labelPosition - 标签位置
 * @property {string} size - 表单尺寸
 * @property {boolean|Function} disabled - 是否禁用
 * @property {string} mode - 表单模式
 * @property {string} title - 表单标题
 * @property {boolean} showActions - 是否显示操作按钮
 * @property {string} actionsPosition - 操作按钮位置
 * @property {boolean} hideLabel - 是否隐藏标签
 * @property {number} formItemSpan - 表单项栅格占位
 * @property {Array<ButtonConfig>} actions - 操作按钮配置
 */

/**
 * 输入框组件属性类型
 * @typedef {Object} InputProps
 * @property {string|number} value - 绑定值
 * @property {string} type - 输入框类型
 * @property {string} size - 输入框尺寸
 * @property {string} mode - 编辑模式
 * @property {string} placeholder - 占位文本
 * @property {boolean} clearable - 是否可清空
 * @property {boolean} showWordLimit - 是否显示字数统计
 * @property {boolean} readonly - 是否只读
 * @property {boolean|Function} disabled - 是否禁用
 * @property {boolean|Function} show - 是否显示
 * @property {number|string} maxlength - 最大输入长度
 * @property {number} minlength - 最小输入长度
 * @property {string} suffixIcon - 尾部图标
 * @property {string} prefixIcon - 头部图标
 * @property {number} rows - 文本域行数
 * @property {boolean|Object} autosize - 自适应内容高度
 * @property {string} autocomplete - 自动补全
 * @property {string} resize - 缩放方式
 * @property {boolean} autofocus - 自动获取焦点
 * @property {string} form - 关联的表单
 * @property {string} tabindex - Tab键顺序
 * @property {boolean} validateEvent - 是否触发表单验证
 * @property {Object} inputStyle - 输入框样式
 * @property {Array|Object} params - 传递的参数
 * @property {Function} onChange - 值改变事件处理器
 * @property {Function} onInput - 输入事件处理器
 * @property {Function} onBlur - 失去焦点事件处理器
 * @property {Function} onFocus - 获得焦点事件处理器
 * @property {Function} onClear - 清空事件处理器
 */

/**
 * 对话框组件属性类型
 * @typedef {Object} DialogProps
 * @property {boolean} show - 是否显示对话框
 * @property {string} title - 对话框标题
 * @property {string} width - 对话框宽度
 * @property {boolean} fullscreen - 是否全屏
 * @property {string} top - 对话框距离顶部的距离
 * @property {boolean} modal - 是否需要遮罩层
 * @property {boolean} modalAppendToBody - 遮罩层是否插入至body元素上
 * @property {boolean} appendToBody - 对话框自身是否插入至body元素上
 * @property {boolean} lockScroll - 是否在对话框出现时将body滚动锁定
 * @property {string} customClass - 对话框的自定义类名
 * @property {boolean} closeOnClickModal - 是否可以通过点击modal关闭对话框
 * @property {boolean} closeOnPressEscape - 是否可以通过按下ESC关闭对话框
 * @property {boolean} showClose - 是否显示关闭按钮
 * @property {Function} beforeClose - 关闭前的回调
 * @property {boolean} center - 是否对头部和底部采用居中布局
 * @property {boolean} destroyOnClose - 关闭时销毁对话框中的元素
 * @property {string} size - 对话框尺寸
 * @property {boolean} showFullscreen - 是否显示全屏按钮
 * @property {Array<DialogAction>} actions - 底部操作按钮
 * @property {boolean} showActions - 是否显示底部操作按钮
 * @property {string} confirmText - 确定按钮文本
 * @property {string} cancelText - 取消按钮文本
 * @property {Object} confirmButtonProps - 确定按钮属性
 * @property {Object} cancelButtonProps - 取消按钮属性
 */

// ==================== 事件类型定义 ====================

/**
 * 组件事件类型
 * @typedef {Object} ComponentEvents
 * @property {Function} input - 输入事件
 * @property {Function} change - 改变事件
 * @property {Function} focus - 获得焦点事件
 * @property {Function} blur - 失去焦点事件
 * @property {Function} click - 点击事件
 * @property {Function} clear - 清空事件
 * @property {Function} visible-change - 显示状态改变事件
 * @property {Function} expand-change - 展开状态改变事件
 * @property {Function} remove-tag - 移除标签事件
 * @property {Function} validate - 验证事件
 * @property {Function} reset - 重置事件
 * @property {Function} submit - 提交事件
 */

// ==================== 状态类型定义 ====================

/**
 * 组件状态类型
 * @typedef {Object} ComponentState
 * @property {boolean} loading - 加载状态
 * @property {string|null} error - 错误信息
 * @property {boolean} disabled - 禁用状态
 * @property {boolean} readonly - 只读状态
 * @property {boolean} visible - 可见状态
 * @property {*} value - 当前值
 * @property {Object} validation - 验证状态
 */

/**
 * 验证状态类型
 * @typedef {Object} ValidationState
 * @property {boolean} valid - 是否有效
 * @property {string} message - 错误消息
 * @property {string} trigger - 触发方式
 */

// ==================== 配置类型定义 ====================

/**
 * 组件库全局配置类型
 * @typedef {Object} GlobalConfig
 * @property {string} size - 默认组件尺寸
 * @property {string} locale - 语言环境
 * @property {Object} theme - 主题配置
 * @property {number} zIndex - 弹出层起始z-index值
 */

/**
 * 主题配置类型
 * @typedef {Object} ThemeConfig
 * @property {string} primaryColor - 主色调
 * @property {string} successColor - 成功色
 * @property {string} warningColor - 警告色
 * @property {string} dangerColor - 危险色
 * @property {string} infoColor - 信息色
 * @property {string} textColor - 文本色
 * @property {string} borderColor - 边框色
 * @property {string} backgroundColor - 背景色
 */

export default {
  // 导出类型定义用于类型检查
  BaseComponentConfig: null,
  EventHandler: null,
  ValidationRule: null,
  FormItemConfig: null,
  ButtonConfig: null,
  CascaderProps: null,
  TableColumn: null,
  DialogAction: null,
  OptionItem: null,
  UploadFile: null,
  TreeNode: null,
  ButtonProps: null,
  CascaderComponentProps: null,
  FormProps: null,
  InputProps: null,
  DialogProps: null,
  ComponentEvents: null,
  ComponentState: null,
  ValidationState: null,
  GlobalConfig: null,
  ThemeConfig: null,
};
