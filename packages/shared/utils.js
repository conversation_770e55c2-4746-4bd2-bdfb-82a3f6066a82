/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-15 18:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-08 14:26:53
 * @FilePath: /vite-element-components/packages/shared/utils.js
 * @Description: 组件库公共工具函数
 */

/**
 * 组件库公共工具函数
 * <AUTHOR>
 * @description 提供组件库通用的工具函数和辅助方法
 */

import {
  debounce as lodashDebounce,
  throttle as lodashThrottle,
  cloneDeep,
  merge,
  isFunction,
  isArray,
  isEmpty,
} from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import { COMPONENT_SIZES, COMPONENT_MODES } from './constants';

// ==================== 验证函数 ====================

/**
 * 验证组件尺寸是否有效
 * @param {string} size 组件尺寸
 * @returns {boolean} 是否有效
 */
export function isValidSize(size) {
  return !size || COMPONENT_SIZES.includes(size);
}

/**
 * 验证组件模式是否有效
 * @param {string} mode 组件模式
 * @returns {boolean} 是否有效
 */
export function isValidMode(mode) {
  return COMPONENT_MODES.includes(mode);
}

// ==================== 显示和禁用控制 ====================

/**
 * 计算组件是否应该显示
 * @param {Boolean|Function} show 显示条件，可以是布尔值或函数
 * @param {*} params 传递给函数的参数
 * @returns {boolean} 是否显示
 */
export function computeVisibility(show, params) {
  if (show === undefined) return true;
  if (typeof show === 'function') {
    return show(params);
  }
  return show;
}

/**
 * 计算组件是否应该禁用
 * @param {Boolean|Function} disabled 禁用条件，可以是布尔值或函数
 * @param {*} params 传递给函数的参数
 * @returns {boolean} 是否禁用
 */
export function computeDisabled(disabled, params, mode) {
  if (typeof disabled === 'function') {
    return disabled(params) || mode === 'disabled';
  }
  return disabled || mode === 'disabled';
}

// ==================== 安全执行函数 ====================

/**
 * 安全执行函数，捕获并处理错误
 * @param {Function} func 要执行的函数
 * @param {Array} args 函数参数数组
 * @param {*} defaultValue 出错时的默认返回值
 * @returns {*} 函数执行结果或默认值
 */
export function safeExecute(func, args = [], defaultValue = undefined) {
  try {
    if (typeof func === 'function') {
      return func(...args);
    }
    return defaultValue;
  } catch (error) {
    console.error('[SafeExecute] 函数执行出错:', error);
    return defaultValue;
  }
}

// ==================== 组件工具函数 ====================

/**
 * 生成组件唯一标识
 * @param {string} prefix 组件前缀
 * @returns {string} 唯一标识
 */
export function generateComponentKey(prefix = 'component') {
  return `${prefix}-${uuidv4().substring(0, 8)}`;
}

/**
 * 深度合并配置对象
 * @param {Object} target 目标对象
 * @param {...Object} sources 源对象
 * @returns {Object} 合并后的对象
 */
export function mergeConfig(target, ...sources) {
  return merge(cloneDeep(target), ...sources);
}

/**
 * 创建防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 防抖延迟时间
 * @param {Object} options 防抖选项
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait = 300, options = {}) {
  return lodashDebounce(func, wait, options);
}

/**
 * 创建节流函数
 * @param {Function} func 要节流的函数
 * @param {number} wait 节流延迟时间
 * @param {Object} options 节流选项
 * @returns {Function} 节流后的函数
 */
export function throttle(func, wait = 300, options = {}) {
  return lodashThrottle(func, wait, options);
}

// ==================== 事件处理工具 ====================

/**
 * 创建事件上下文对象
 * @param {Event} event 原生事件对象
 * @param {Object} extraContext 额外的上下文信息
 * @returns {Object} 事件上下文
 */
export function createEventContext(event = null, extraContext = {}) {
  return {
    $event: event,
    timestamp: Date.now(),
    ...extraContext,
  };
}

// ==================== 样式工具函数 ====================

/**
 * 生成组件样式类名
 * @param {string} prefix CSS类名前缀
 * @param {Object} modifiers 修饰符对象
 * @param {Object} extraClasses 额外的类名对象
 * @returns {Object} 样式类名对象
 */
export function generateClasses(prefix, modifiers = {}, extraClasses = {}) {
  const classes = {
    [prefix]: true,
    ...extraClasses,
  };

  Object.keys(modifiers).forEach((key) => {
    const value = modifiers[key];
    if (value) {
      classes[`${prefix}-${key}`] = true;
    }
  });

  return classes;
}

// ==================== 数据处理工具 ====================

/**
 * 深度克隆对象
 * @param {*} source 源对象
 * @returns {*} 克隆后的对象
 */
export function deepClone(source) {
  return cloneDeep(source);
}

/**
 * 检查值是否为空
 * @param {*} value 要检查的值
 * @returns {boolean} 是否为空
 */
export function isEmptyValue(value) {
  return isEmpty(value);
}

/**
 * 检查值是否为函数
 * @param {*} value 要检查的值
 * @returns {boolean} 是否为函数
 */
export function isFunctionValue(value) {
  return isFunction(value);
}

/**
 * 检查值是否为数组
 * @param {*} value 要检查的值
 * @returns {boolean} 是否为数组
 */
export function isArrayValue(value) {
  return isArray(value);
}

// ==================== 表单工具函数 ====================

/**
 * 验证表单字段值
 * @param {*} value 字段值
 * @param {Array} rules 验证规则数组
 * @returns {Object} 验证结果 { valid: boolean, message: string }
 */
export function validateField(value, rules = []) {
  if (!Array.isArray(rules) || rules.length === 0) {
    return { valid: true, message: '' };
  }

  for (const rule of rules) {
    if (rule.required && (value === undefined || value === null || value === '')) {
      return { valid: false, message: rule.message || '此字段为必填项' };
    }

    if (rule.min && typeof value === 'string' && value.length < rule.min) {
      return { valid: false, message: rule.message || `至少输入${rule.min}个字符` };
    }

    if (rule.max && typeof value === 'string' && value.length > rule.max) {
      return { valid: false, message: rule.message || `最多输入${rule.max}个字符` };
    }

    if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
      return { valid: false, message: rule.message || '格式不正确' };
    }

    if (rule.validator && typeof rule.validator === 'function') {
      const result = rule.validator(value);
      if (result !== true) {
        return { valid: false, message: typeof result === 'string' ? result : rule.message || '验证失败' };
      }
    }
  }

  return { valid: true, message: '' };
}

/**
 * 重置表单数据
 * @param {Object} formData 表单数据对象
 * @param {Object} defaultValues 默认值对象
 * @returns {Object} 重置后的表单数据
 */
export function resetFormData(formData, defaultValues = {}) {
  const resetData = { ...formData };
  Object.keys(resetData).forEach((key) => {
    if (Object.prototype.hasOwnProperty.call(defaultValues, key)) {
      resetData[key] = defaultValues[key];
    } else {
      resetData[key] = '';
    }
  });
  return resetData;
}

/**
 * 检查值是否有效（不为空、null、undefined）
 * @param {*} value 要检查的值
 * @returns {boolean} 是否有效
 */
export function isValidValue(value) {
  return value !== '' && value !== null && value !== undefined;
}

/**
 * 格式化数字精度
 * @param {number} value 数字值
 * @param {number} precision 精度
 * @returns {string} 格式化后的数字字符串
 */
export function formatNumberPrecision(value, precision) {
  if (typeof value !== 'number' || !Number.isFinite(value)) {
    return '';
  }
  if (precision === undefined) {
    return value.toString();
  }
  return value.toFixed(precision);
}

/**
 * 解析数字值
 * @param {*} value 要解析的值
 * @param {number} defaultValue 默认值
 * @returns {number} 解析后的数字
 */
export function parseNumber(value, defaultValue = 0) {
  if (typeof value === 'number' && Number.isFinite(value)) {
    return value;
  }
  if (typeof value === 'string' && value.trim() !== '') {
    const parsed = Number(value);
    if (Number.isFinite(parsed)) {
      return parsed;
    }
  }
  return defaultValue;
}

/**
 * 限制数字在指定范围内
 * @param {number} value 数字值
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {number} 限制后的数字
 */
export function clampNumber(value, min = -Infinity, max = Infinity) {
  if (typeof value !== 'number' || !Number.isFinite(value)) {
    return min !== -Infinity ? min : 0;
  }
  return Math.max(min, Math.min(max, value));
}

/**
 * 转换为字符串
 * @param {*} value 要转换的值
 * @param {string} defaultValue 默认值
 * @returns {string} 转换后的字符串
 */
export function toString(value, defaultValue = '') {
  if (value === null || value === undefined) return defaultValue;
  return String(value);
}

/**
 * @description 根据组件类型获取初始值
 * 与 formItems 组件保持一致的逻辑
 * @param {string} component 组件类型
 * @param {object} formInputConfig 组件配置
 * @returns {any} 初始值
 */
export const getInitialValueByComponent = (component, formInputConfig = {}) => {
  // 数组类型的组件（重置为空数组）
  const arrayComponents = ['checkbox', 'transfer'];
  if (arrayComponents.includes(component)) {
    return [];
  }

  // 特殊处理的组件
  switch (component) {
    case 'inputNumberRange':
      // 数字范围组件默认值
      return [undefined, undefined];

    case 'upload':
      // 上传组件根据类型决定初始值
      return formInputConfig?.type === 'default' ? [] : '';

    case 'date':
    case 'datePicker':
      // 日期组件根据是否为范围选择决定初始值
      return formInputConfig?.type?.includes('range') ? ['', ''] : '';

    case 'selectTree':
    case 'cascader':
    case 'select':
      // 选择器组件根据是否多选决定初始值
      return formInputConfig?.multiple ? [] : '';

    case 'switch':
      // 开关组件默认为 false
      return false;

    case 'inputNumber':
      // 数字输入框默认为空字符串
      return undefined;

    case 'custom':
      if (formInputConfig?.valueType === 'string') {
        return '';
      }
      if (formInputConfig?.valueType === 'object') {
        return {};
      }
      if (formInputConfig?.valueType === 'boolean') {
        return {};
      }
      return [];

    default:
      // 其他组件默认为空字符串
      return null;
  }
};
