/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-15 18:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-16 09:26:04
 * @FilePath: /vite-element-components/packages/shared/constants.js
 * @Description: 组件库公共常量定义
 */

/**
 * 组件库公共常量定义
 * <AUTHOR>
 * @description 定义组件库中所有组件共享的常量和枚举值
 */

// ==================== 通用枚举 ====================

/**
 * 组件尺寸枚举
 * @type {string[]}
 */
export const COMPONENT_SIZES = ['large', 'medium', 'small', 'mini'];

/**
 * 组件模式枚举
 * @type {string[]}
 */
export const COMPONENT_MODES = ['edit', 'view', 'disabled'];

// ==================== 公共事件名称 ====================

/**
 * 通用事件名称常量
 */
export const COMMON_EVENTS = {
  // 数据相关
  INPUT: 'input',
  CHANGE: 'change',
  UPDATE_VALUE: 'update:value',
  UPDATE_SHOW: 'update:show',

  // 交互相关
  CLICK: 'click',
  FOCUS: 'focus',
  BLUR: 'blur',

  // 显示相关
  VISIBLE_CHANGE: 'visible-change',
  EXPAND_CHANGE: 'expand-change',

  // 表单相关
  VALIDATE: 'validate',
  RESET: 'reset',
  SUBMIT: 'submit',

  // 其他
  REMOVE_TAG: 'remove-tag',
  CLEAR: 'clear',
  CANCEL: 'cancel',
  CONFIRM: 'confirm',
};

// ==================== 默认配置 ====================

/**
 * 组件默认配置
 */
export const DEFAULT_COMPONENT_CONFIG = {
  size: '',
  mode: 'edit',
  disabled: false,
  show: true,
  params: [],
};

/**
 * 表单默认配置
 */
export const DEFAULT_FORM_CONFIG = {
  ...DEFAULT_COMPONENT_CONFIG,
  labelWidth: '100px',
  labelPosition: 'right',
  hideRequiredAsterisk: false,
  showMessage: true,
  inlineMessage: false,
  statusIcon: false,
  validateOnRuleChange: true,
  title: '',
  showActions: true,
  actionsPosition: 'auto',
  hideLabel: false,
  formItemSpan: 24,
};

/**
 * 对话框默认配置
 */
export const DEFAULT_DIALOG_CONFIG = {
  title: '',
  width: '50%',
  fullscreen: false,
  top: '15vh',
  modal: true,
  modalAppendToBody: true,
  appendToBody: false,
  lockScroll: true,
  customClass: '',
  closeOnClickModal: true,
  closeOnPressEscape: true,
  showClose: true,
  beforeClose: null,
  center: false,
  destroyOnClose: false,
  size: 'medium',
  showFullscreen: false,
  actions: [],
  showActions: true,
  confirmText: '确定',
  cancelText: '取消',
  confirmButtonProps: {},
  cancelButtonProps: {},
};

// ==================== CSS类名前缀 ====================

/**
 * 组件CSS类名前缀
 */
export const CSS_PREFIXES = {
  BUTTON: 'x-button',
  CASCADER: 'x-cascader',
  FORM: 'x-form',
  INPUT: 'x-input',
  DIALOG: 'x-dialog',
  TABLE: 'x-table',
  SELECT: 'x-select',
  CHECKBOX: 'x-checkbox',
  RADIO: 'x-radio',
  SWITCH: 'x-switch',
  UPLOAD: 'x-upload',
  PAGINATION: 'x-pagination',
  TOOLTIP: 'x-tooltip',
  SEARCH: 'x-search',
  INPUT_NUMBER: 'x-input-number',
  DATE_PICKER: 'x-date-picker',
  EMPTY: 'x-empty',
  CUSTOM: 'x-custom',
};

// ==================== 验证规则常量 ====================

/**
 * 验证规则常量
 */
export const VALIDATION_RULES = {
  REQUIRED: 'required',
  MIN_LENGTH: 'minLength',
  MAX_LENGTH: 'maxLength',
  MIN: 'min',
  MAX: 'max',
  PATTERN: 'pattern',
  EMAIL: 'email',
  URL: 'url',
  PHONE: 'phone',
  CUSTOM: 'custom',
};

// ==================== 其他常量 ====================

/**
 * 默认分隔符
 */
export const DEFAULT_SEPARATOR = '/';

/**
 * 默认占位符
 */
export const DEFAULT_PLACEHOLDER = '请选择';

/**
 * 默认空值显示
 */
export const DEFAULT_EMPTY_TEXT = '-';

/**
 * 组件状态常量
 */
export const COMPONENT_STATES = {
  NORMAL: 'normal',
  LOADING: 'loading',
  ERROR: 'error',
  DISABLED: 'disabled',
  SUCCESS: 'success',
  WARNING: 'warning',
};
