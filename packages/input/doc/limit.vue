<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-07 17:40:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-11 09:51:55
 * @FilePath: /vite-element-components/packages/input/doc/limit.vue
 * @Description: 数字输入和长度限制示例
-->
<template>
  <div class="component-view">
    <p>
      数字输入框：
      <x-input v-model="numberValue" type="number" placeholder="请输入数字" />
    </p>
    <p>
      限制最大长度：
      <x-input v-model="limitValue" :maxlength="10" placeholder="最大输入长度为10" show-word-limit />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      numberValue: '',
      limitValue: '',
    };
  },
};
</script>
