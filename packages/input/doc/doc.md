<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:44:32
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-17 11:49:55
 * @FilePath: /vite-element-components/packages/input/doc/doc.md
 * @Description:
-->
<script >
  import BasicVue from './basic.vue';
  import IconVue from './icon.vue';
  import SizeVue from './size.vue';
  import LimitVue from './limit.vue';
  import Preview from '@/components/preview.vue'

  export default {
    components: {
      BasicVue,
      IconVue,
      SizeVue,
      LimitVue,
      Preview
    }
  };
</script>

## Input 输入框组件

基于 Element UI 的输入框组件封装，支持只读和编辑模式，提供基础的文本输入功能。

### 基础用法

<basic-vue/>
<preview  comp-name='input' demo-name='basic'/>

### 带图标的输入框

<icon-vue/>
<preview  comp-name='input' demo-name='icon'/>

### 不同尺寸

<size-vue/>
<preview  comp-name='input' demo-name='size'/>

### 数字输入和长度限制

<limit-vue/>
<preview  comp-name='input' demo-name='limit'/>

### 属性说明

| 参数            | 说明                         | 类型            | 可选值                | 默认值 |
| --------------- | ---------------------------- | --------------- | --------------------- | ------ |
| value / v-model | 绑定值                       | string / number | —                     | ''     |
| ellipsis        | 是否在只读模式下隐藏溢出内容 | boolean         | —                     | false  |
| maxlength       | 最大输入长度                 | number          | —                     | 50     |
| showWordLimit   | 是否显示输入字数统计         | boolean         | true/false            | true   |
| placeholder     | 输入框占位文本               | string          | —                     | ''     |
| readonly        | 是否为只读模式               | boolean         | —                     | false  |
| size            | 输入框尺寸                   | string          | medium / small / mini | small  |
| rows            | 文本域行数                   | string/number   | —                     | —      |
| clearable       | 是否可清空                   | boolean         | —                     | true   |
| disabled        | 是否禁用                     | boolean         | —                     | false  |
| ...             | 其他el-input的属性           | -               | —                     | -      |

### 插槽

| 插槽名称 | 说明           |
| -------- | -------------- |
| prefix   | 输入框头部内容 |
| suffix   | 输入框尾部内容 |
| prepend  | 输入框前置内容 |
| append   | 输入框后置内容 |

### 事件

如果是可编辑表格内的输入框，事件回调会返回当前行数据，否则返回当前输入框的值。

| 事件名称 | 说明                                        | 回调参数                  |
| -------- | ------------------------------------------- | ------------------------- |
| input    | 在 Input 值改变时触发                       | (value: string \| number) |
| change   | 在 Input 值改变时触发                       | (value: string \| number) |
| focus    | 在输入框获得焦点时触发                      | (event: Event)            |
| blur     | 在输入框失去焦点时触发                      | (event: Event)            |
| clear    | 在点击由 clearable 属性生成的清空按钮时触发 | —                         |
