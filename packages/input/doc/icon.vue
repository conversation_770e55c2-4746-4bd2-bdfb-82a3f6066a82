<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-07 17:30:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-11 10:15:20
 * @FilePath: /vite-element-components/packages/input/doc/icon.vue
 * @Description: 带图标的输入框示例
-->
<template>
  <div class="component-view">
    <p>
      带图标的输入框：
      <x-input v-model="inputValue" placeholder="请输入内容">
        <i slot="prefix" class="el-icon-search el-input__icon"></i>
      </x-input>
    </p>
    <p>
      带后缀图标的输入框：
      <x-input v-model="inputValue2" placeholder="请输入内容">
        <i slot="suffix" class="el-icon-date el-input__icon"></i>
      </x-input>
    </p>
    <p>
      带前后缀的输入框：
      <x-input v-model="inputValue3" placeholder="请输入内容">
        <template slot="prepend">https://</template>
        <template slot="append">.com</template>
      </x-input>
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputValue: '',
      inputValue2: '',
      inputValue3: '',
    };
  },
};
</script>
