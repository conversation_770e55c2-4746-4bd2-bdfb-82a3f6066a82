<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-07 17:35:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-07 17:35:00
 * @FilePath: /vite-element-components/packages/input/doc/size.vue
 * @Description: 不同尺寸的输入框示例
-->
<template>
  <div class="component-view">
    <p>
      默认尺寸：
      <x-input v-model="inputValue" placeholder="默认尺寸" />
    </p>
    <p>
      中等尺寸：
      <x-input v-model="inputValue" size="medium" placeholder="中等尺寸" />
    </p>
    <p>
      小型尺寸：
      <x-input v-model="inputValue" size="small" placeholder="小型尺寸" />
    </p>
    <p>
      迷你尺寸：
      <x-input v-model="inputValue" size="mini" placeholder="迷你尺寸" />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputValue: '',
    };
  },
};
</script>
