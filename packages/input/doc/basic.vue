<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:44:32
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-28 15:08:30
 * @FilePath: /vite-element-components/packages/input/doc/basic.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <p>
      普通输入框：
      <x-input v-model="inputValue" :maxlength="5" show-word-limit placeholder="最大长度5">
        <template slot="append"> 后缀 </template>
        <template slot="prepend"> 前缀 </template>
      </x-input>
    </p>
    <p>
      详情输入框：
      <x-input mode="view" v-model="inputValue" :maxlength="5" />
    </p>
    <p>
      只读输入框：
      <x-input readonly v-model="inputValue" :maxlength="5" />
    </p>
    <p>
      文本域标签：
      <x-input v-model="inputValue" :rows="5" type="textarea" placeholder="最大长度5"> </x-input>
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputValue: '',
    };
  },
};
</script>
