<script lang="jsx">
import Tooltip from '../tooltip/index.vue';

/**
 * 输入框组件
 * @component XInput
 * @description 基于 Element UI 的输入框组件封装，支持只读和编辑模式，提供基础的文本输入功能
 */
export default {
  name: 'XInput',
  title: '输入框组件',
  components: { Tooltip },
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    /**
     * 绑定值
     * @type {string|number}
     * @default ''
     */
    value: {
      type: [String, Number],
      default: '',
    },
    /**
     * 是否在只读模式下隐藏溢出内容
     * @type {boolean}
     * @default false
     */
    ellipsis: {
      type: Boolean,
      default: false,
    },
    /**
     * 最大输入长度
     * @type {number}
     * @default 50
     */
    maxlength: {
      type: Number,
      default: 50,
    },
    /**
     * 输入框占位文本
     * @type {string}
     * @default ''
     */
    placeholder: {
      type: String,
      default: '',
    },

    /**
     * 输入框尺寸
     * @type {string}
     * @default 'small'
     */
    size: {
      type: String,
      default: '',
    },
    /**
     * 是否可清空
     * @type {boolean}
     * @default true
     */
    clearable: {
      type: Boolean,
      default: true,
    },
    /**
     * 编辑模式
     * @type {String}
     * @default 'edit'
     */
    mode: {
      type: String,
      default: 'edit',
    },
    /**
     * 是否为禁用模式
     * @type {boolean}
     * @default false
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否为只读模式
     * @type {boolean}
     * @default false
     */
    readonly: {
      type: Boolean,
      default: false,
    },
    /**
     * 控制是否能被用户缩放
     * @type {String}
     * @default '' 可选值: none, both, horizontal, vertical
     */
    resize: {
      type: String,
      default: '',
      validator: (value) => ['', 'none', 'both', 'horizontal', 'vertical'].includes(value),
    },
    /**
     * @description 可编辑表格使用，绑定值
     * @type {Object}
     * @default ''
     */
    context: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 文本域的行数
     * @type {Number|String}
     * @default 3
     */
    rows: {
      type: Number,
      default: 3,
    },
    /**
     * @description 是否显示字数统计
     * @type {Boolean}
     * @default true
     */
    showWordLimit: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    /**
     * 输入框的值，处理空值和数字转字符串的情况
     * @returns {string}
     */
    inputValue: {
      get() {
        return String(this.value || this.value === 0 ? this.value : '');
      },
      set(value) {
        this.$emit('update:value', value);
      },
    },
  },
  methods: {
    /**
     * 获取显示值，如果为空则返回占位符
     * @returns {string}
     */
    getValue() {
      return this.inputValue ? this.inputValue : '-';
    },
  },
  render() {
    return (
      <div class="x-input">
        {this.mode === 'view' ? (
          <tooltip content={this.getValue()} ellipsis={this.ellipsis} />
        ) : (
          <el-input
            vModel={this.inputValue}
            maxlength={this.maxlength}
            size={this.size}
            placeholder={this.placeholder}
            clearable={this.clearable}
            disabled={this.disabled || this.mode === 'disabled'}
            readonly={this.readonly || this.mode === 'disabled'}
            resize={this.disabled || this.readonly ? 'none' : this.resize}
            showWordLimit={this.showWordLimit}
            rows={this.rows}
            {...{
              props: this.$attrs,
              on: {
                ...Object.entries(this.$listeners).reduce((acc, [event, handler]) => {
                  acc[event] = (...args) => handler(...args, this.context);
                  return acc;
                }, {}),
              },
            }}
          >
            {['prefix', 'suffix', 'append', 'prepend'].map((slotName) => {
              const slotContent = this.$slots[slotName] || this.$attrs?.slots?.[slotName];

              return (
                slotContent && (
                  <template slot={slotName}>
                    {typeof slotContent === 'function' ? (
                      slotContent(this.context)
                    ) : (
                      <div {...{ props: { context: this.context } }}>{slotContent}</div>
                    )}
                  </template>
                )
              );
            })}
          </el-input>
        )}
      </div>
    );
  },
};
</script>
<style lang="scss" scoped>
@import '../styles/index.scss';
</style>
