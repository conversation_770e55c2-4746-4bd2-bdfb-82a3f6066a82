<!-- eslint-disable no-unused-vars -->
<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-17 14:19:11
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 13:35:24
 * @FilePath: /vite-element-components/packages/editTable/index.vue
 * @Description: 可编辑表格组件，支持行内编辑、拖拽排序、表单验证等功能
-->
<script lang="jsx">
import { constantComponent, constantRulesRequired } from '../formItems/constant';
import AsyncLoadComponent from '../formItems/asyncLoadComponent.vue';
import sortable from 'sortablejs';
import cloneDeep from 'lodash/cloneDeep';
import debounce from 'lodash/debounce';
import XEmpty from '../empty/index.vue';
import XTooltip from '../tooltip/index.vue';
import XButton from '../button/index.vue';
import XPagination from '../pagination/index.vue';
import { getInitialValueByComponent } from '../shared/utils';

export default {
  name: 'XEditTable',
  components: { AsyncLoadComponent, XButton, XTooltip, XEmpty, XPagination },
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    // 表格绑定数据
    value: {
      type: Array,
      default: () => [],
    },
    // 标题
    title: {
      type: String,
      default: '',
    },
    // 表格列配置
    tableColumns: {
      type: Array,
      default: () => [],
    },
    // 表格数据键名 用在校验处
    tableDataName: {
      type: String,
      default: '',
    },
    // 表格最大允许行数
    max: {
      type: [Number, String],
      default: Infinity,
    },
    // 编辑模式
    mode: {
      type: String,
      default: 'edit',
    },
    // 是否允许添加
    allowIncrease: {
      type: Boolean,
      default: true,
    },
    // 是否允许删除
    allowDelete: {
      type: Boolean,
      default: true,
    },
    // 初始化时最小行数
    initdMinRow: {
      type: Number,
      default: 0,
    },
    // 操作栏配置
    toolbars: {
      type: [Array, Function],
      default: null,
    },
    // 是否允许拖动排序
    draggable: {
      type: Boolean,
      default: false,
    },
    // 自定义的增加行函数
    increaseRowFunction: {
      type: Function,
      default: null,
    },
    // 表格内容是否换行展示
    showOverflow: {
      type: Boolean,
      default: true,
    },
    // 添加新的属性
    rowHeight: {
      type: [String, Number],
      default: '', // 自定义行高
    },
    /**
     * @description 多选事件变更回调函数
     * @type {Function}
     * @default null
     */
    selectionChange: {
      type: Function,
      default: null,
    },
    // 是否分页
    pagination: {
      type: Boolean,
      default: false,
    },
    // 分页配置
    paginationConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      innerPagination: {
        pageNo: this.paginationConfig.pageNo || 1,
        pageSize: this.paginationConfig.pageSize || 10,
        pageSizeOptions: this.paginationConfig.pageSizeOptions || [10, 20, 50],
      },
    };
  },
  computed: {
    // 初始化行对象
    initRow() {
      return this.tableColumns.reduce((result, col) => {
        const o = {};
        o[col.prop] = getInitialValueByComponent(col.component, col.formInputConfig);
        return { ...result, ...o };
      }, {});
    },
    // 表格数据
    tableData: {
      get() {
        if (this.value.length === 0 && this.initdMinRow) {
          for (let i = 0; i < this.initdMinRow; i++) {
            this.$set(this.value, i, JSON.parse(JSON.stringify(this.initRow)));
          }
        }
        return this.value;
      },
      set(val) {
        this.$emit('update:value', val);
      },
    },
    // 列配置
    columns: {
      get() {
        if (this.mode === 'edit' && (this.allowIncrease || this.allowDelete)) {
          return [...this.tableColumns, { type: 'actions', title: '操作', fixed: 'right', width: 60 }];
        }
        return this.tableColumns;
      },
    },
    // 将 renderColumnsOptions 的结果缓存为计算属性
    renderColumns() {
      const columns = [...this.columns];
      if (this.draggable) {
        columns.unshift({
          title: '排序',
          width: 48,
          type: 'move',
          align: 'center',
          className: 'x-edit-table__move',
          slots: {
            default: () => <i class="x-table__drag-btn el-icon-rank" style="cursor:move;font-size:16px" />,
          },
        });
      }
      return this.processColumns(columns);
    },
    // 前端数据进行分页
    pagedTableData() {
      if (!this.pagination) return this.tableData;
      const start = (this.innerPagination.pageNo - 1) * this.innerPagination.pageSize;
      return this.tableData.slice(start, start + this.innerPagination.pageSize);
    },
  },
  beforeDestroy() {
    if (this.sortable) {
      this.sortable.destroy();
    }
  },
  mounted() {
    // 往该组件上绑定vxe-table的方法
    this.forwardMethods.call(this, this.$refs.table);
    if (this.draggable) {
      this.handleDraggable();
    }
  },
  methods: {
    /**
     * 绑定子组件的方法到当前组件
     * @param {Object} childComponent - 子组件实例
     */
    forwardMethods(childComponent) {
      const { methods } = childComponent.$options; // 获取子组件的方法
      // 遍历子组件的方法
      // eslint-disable-next-line guard-for-in
      for (const methodName in methods) {
        // 使用闭包创建一个新的函数，用于转发调用子组件的方法
        this[methodName] = (...args) => childComponent[methodName](...args);
      }
    },
    // 暴露多选方法
    handleSelectionChange({ records }) {
      const selects = records.map((item) => item[this.rowKey] || item);
      // eslint-disable-next-line no-unused-expressions
      this.selectionChange && this.selectionChange(records, selects);
    },
    /**
     * 初始化表格拖拽排序功能
     */
    handleDraggable() {
      this.$nextTick(() => {
        const xTable = this.$refs.table;
        this.sortable = sortable.create(
          xTable.$el.querySelector('.body--wrapper>.vxe-table--body-inner-wrapper tbody'),
          {
            handle: '.x-table__drag-btn',
            onEnd: (context) => {
              const { oldIndex, newIndex } = context;
              const originTableData = cloneDeep(this.tableData); // 当前页原始数据
              // 排序的行
              const sortedRow = this.tableData.splice(oldIndex, 1)[0];
              // 被插入的行
              const beSortedRow = originTableData[newIndex];

              this.tableData.splice(newIndex, 0, sortedRow);
              this.$emit('dragEnd', {
                ...context,
                sortedRow,
                beSortedRow,
                tableData: this.tableData,
              });
            },
          },
        );
      });
    },
    /**
     * 渲染表格行的操作按钮（新增/删除）
     * @param {Object} config - 按钮配置
     * @param {Object} row - 当前行数据
     * @param {Number} rowIndex - 行索引
     * @returns {JSX.Element} 按钮渲染结果
     */
    renderActions(config, row, rowIndex) {
      // 预留，允许自定义操作栏
      if (config.actions) {
        return <el-form>{this.renderButtons(config, row, rowIndex)}</el-form>;
      }
      return (
        this.tableData.length > this.initdMinRow &&
        this.allowDelete && (
          <i
            class="el-icon-minus x-edit-table__actions-item"
            onClick={() => this.handleActions('minus', row, rowIndex)}
          />
        )
      );
    },
    /**
     * 处理表格行的操作事件（新增/删除）
     * @param {String} type - 操作类型：'add'或'minus'
     * @param {Object} row - 当前行数据
     * @param {Number} rowIndex - 行索引
     */
    handleActions(type, row, rowIndex) {
      if (this.mode === 'edit') {
        switch (type) {
          case 'add':
            if (this.tableData.length < this.max) {
              // 现在都是新增到最后一行
              if (this.increaseRowFunction) {
                const row = this.increaseRowFunction();
                this.tableData.push(row);
                this.$emit('added', { tableData: this.tableData, type, row, rowIndex: this.tableData.length - 1 });
              } else {
                this.tableData.push(JSON.parse(JSON.stringify(this.initRow)));
                this.$emit('added', {
                  tableData: this.tableData,
                  type,
                  row: JSON.parse(JSON.stringify(this.initRow)),
                  rowIndex: this.tableData.length - 1,
                });
              }
            }
            break;
          case 'minus':
            this.tableData.splice(rowIndex, 1);
            this.$emit('deleted', { tableData: this.tableData, type, row, rowIndex });
            break;
          default:
        }
      }
    },
    /**
     * 渲染自定义按钮组
     * @param {Object} config - 按钮组配置
     * @param {Object} row - 当前行数据
     * @param {Number} index - 行索引
     * @returns {Array} 按钮组渲染结果
     */
    renderButtons(config, row, index) {
      // 先处理当前条数据，真实可用的按钮组
      const actionsFilter = config.actions.filter((action) => (action.show ? action.show(row, index) : true));
      config.minShowActions = config.minShowActions || 2; // 设置最小展示按钮数默认值
      // 正常显示的按钮
      const moreButtons = actionsFilter.slice(config.minShowActions, actionsFilter.length); // 当分割之后更多按钮数只有1个的时候，使用主按钮替换掉更多按钮
      const normalButtonLength = moreButtons.length === 1 ? config.minShowActions + 1 : config.minShowActions;

      // 正常显示的按钮组
      const normalButtons = actionsFilter.slice(0, Math.min(actionsFilter.length, normalButtonLength));
      // 渲染正常的按钮
      const normalButtonRender = normalButtons.map((action) => (
        <x-button
          type="text"
          css="max-width:100%;"
          {...{
            attrs: action,
            scopedSlots: {
              rightSlot: () => (action.rightIcon ? action.rightIcon(row) : ''),
            },
          }}
          params={[row, index]}
        />
      ));
      const popupProps = {
        overlayClassName: 'x-table__action',
        delay: 0,
        attach: '#XTable',
      };
      // 渲染更多按钮
      // trigger = 'click';
      const moreButtonRender =
        moreButtons.length > 1 ? (
          <el-dropdown maxColumnWidth="120px" trigger="click" popupProps={popupProps}>
            <el-button type="text">
              <span>
                更多
                <i class="el-icon-arrow-down" />
              </span>
            </el-button>
            <el-dropdown-menu>
              {moreButtons.length > 0 &&
                moreButtons.map((action) => (
                  <el-dropdown-item
                    value={action.label}
                    key={action.label}
                    onClick={(e) => action.event(row, index, e)}
                  >
                    {action.label}
                  </el-dropdown-item>
                ))}
            </el-dropdown-menu>
          </el-dropdown>
        ) : (
          ''
        );
      return [normalButtonRender, moreButtonRender];
    },
    /**
     * 生成表单项的配置参数
     * @param {Object} config - 表单配置
     * @returns {Object} 表单配置对象
     */
    getFormItemConfig(config = {}) {
      const {
        label,
        component,
        isRequired = 0,
        rules: configRules = [],
        ruleMessage,
        slots,
        formInputConfig,
        ...restConfig
      } = config;

      let defaultValidator;
      if (!component) return { formInputConfig, ...restConfig };
      const { trigger, getMessage, validator } = constantRulesRequired[component]; // 默认规则触发方式和提示文字
      // 在只读模式下不添加验证规则
      if (this.mode === 'view' || this.mode === 'disabled') {
        return { formInputConfig, ...restConfig, rules: [] };
      }
      // 某些特殊组件需要走特殊的校验规则
      if (formInputConfig?.showCustom && !formInputConfig?.type?.includes('range') && component === 'date') {
        // 时间选择器，且存在显示至今选项
        defaultValidator = validator
          ? {
              required: true,
              trigger,
              validator: (rule, value, callback) => {
                validator(rule, value, callback, label);
              },
            }
          : {
              required: true,
              trigger,
              validator: (rule, value, callback) => {
                // 时间选择器允许值为null
                if ([undefined, ''].includes(value)) {
                  callback(new Error(ruleMessage || `请选择${label}`));
                }
                callback();
              },
            };
      } else {
        defaultValidator = validator
          ? {
              required: true,
              trigger,
              validator: (rule, value, callback) => {
                validator(rule, value, callback, label);
              },
            }
          : {
              required: true,
              trigger,
              message: ruleMessage || getMessage(label),
            };
      }
      let rules = [];

      if (this.mode === 'edit') {
        rules = isRequired
          ? [
              defaultValidator, // 默认的校验规则
              ...configRules, // 自定义的校验规则
            ]
          : [...configRules];

        return { formInputConfig, ...restConfig, rules, slots };
      }
      return { formInputConfig, ...restConfig, rules: [], slots };
    },
    /**
     * 生成表单输入项的配置参数
     * @param {Object} row - 当前行数据
     * @param {Object} config - 配置对象
     * @returns {Object} 输入项配置
     */
    getFormInputConfig(row, config = {}) {
      // 处理自定义列的场景，如果需要某个列的单元格限制需要根据当前行的某一个字段值来决定
      if (config.customColumns) {
        config.customColumns({ row, tableColumns: config }).then((custom) => {
          const { placeholder, ...otherFormInputConfig } = custom.formInputConfig || {};
          return (
            {
              placeholder:
                this.mode === 'edit'
                  ? placeholder || constantRulesRequired[config.component]?.getMessage(config.label || '')
                  : '',
              ...otherFormInputConfig,
            } || {}
          );
        });
      }

      const { placeholder, ...otherFormInputConfig } = config.formInputConfig || {};
      return (
        {
          placeholder:
            this.mode === 'edit'
              ? placeholder || constantRulesRequired[config.component]?.getMessage(config.label || '')
              : '',
          ...otherFormInputConfig,
        } || {}
      );
    },
    /**
     * 处理表格列配置
     * @param {Array} columns - 列配置数组
     * @returns {Array} 处理后的列配置
     */
    processColumns(columns) {
      return columns.map((__) => {
        let m = {};
        if (__.type === 'move') {
          m = {
            width: 50,
            ...__,
          };
        } else if (__.type === 'checkbox') {
          m = {
            width: 42,
            ...__,
            visible: this.mode === 'edit',
          };
        } else if (__.type === 'seq') {
          m = {
            width: 50,
            ...__,
            title: __.label || '序号',
          };
        } else if (__.type === 'actions') {
          m = this.processActionColumn(__);
        } else {
          m = this.processDefaultColumn(__);
        }

        if (__?.children && Array.isArray(__.children)) {
          this.isMultiLevelHeader = true;
          m.children = this.processColumns(__.children);
          m.minWidth = null;
        }
        return m;
      });
    },
    /**
     * 处理操作列配置
     * @param {Object} column - 列配置对象
     * @returns {Object} 处理后的列配置
     */
    processActionColumn(column) {
      return {
        width: 80,
        ...column,
        visible: this.mode === 'edit',
        field: column.prop,
        title: column.label || '操作',
        type: undefined,
        className: 'x-edit-table__actions',
        align: 'center',
        slots: {
          header: () =>
            this.tableData.length <= this.max - 1 && this.allowIncrease && this.mode === 'edit' ? (
              <i class="el-icon-plus x-edit-table__actions-item" onClick={() => this.handleActions('add')} />
            ) : (
              column.label || '操作'
            ),
          default: ({ row, rowIndex }) => this.renderActions(column, row, rowIndex),
        },
      };
    },
    /**
     * 处理默认列配置
     * @param {Object} __ - 列配置对象
     * @returns {Object} 处理后的列配置
     */
    processDefaultColumn(__) {
      return {
        minWidth: 150,
        field: __.prop,
        ...__,
        slots: {
          default: ({ row, rowIndex }) => this.renderCellContent(__, row, rowIndex),
          header: this.renderColumnHeader(__),
          ...__?.slots,
        },
      };
    },
    /**
     * 渲染单元格内容
     * @param {Object} column - 列配置对象
     * @param {Object} row - 当前行数据
     * @param {Number} rowIndex - 行索引
     * @returns {JSX.Element} 单元格内容
     */
    renderCellContent(column, row, rowIndex) {
      const { component, prop, showOverflow } = column;
      if (component) {
        return this.renderFormComponent(column, row, rowIndex);
      }
      const value = row[prop];
      let tooltipContent = value;
      if (tooltipContent === false || tooltipContent === null || tooltipContent === undefined) {
        tooltipContent = '';
      } else if (tooltipContent === '') {
        tooltipContent = '-';
      }

      if (showOverflow) {
        return <x-tooltip content={tooltipContent} />;
      }
      if (!showOverflow && showOverflow !== undefined) {
        return tooltipContent;
      }
      return this.showOverflow ? <XTooltip content={tooltipContent} /> : tooltipContent;
    },
    /**
     * 渲染列头部
     * @param {Object} __ - 列配置对象
     * @returns {Function} 列头部渲染函数
     */
    renderColumnHeader(__) {
      return ({ column, columnIndex, $columnIndex, _columnIndex, $rowIndex }, h) =>
        __?.isRequired && this.mode === 'edit' ? (
          <span>
            <label class="vxe-table__title-required">*</label>
            {__.label}
          </span>
        ) : (
          __.label
        );
    },
    /**
     * 渲染表单组件
     * @param {Object} column - 列配置对象
     * @param {Object} row - 当前行数据
     * @param {Number} rowIndex - 行索引
     * @returns {JSX.Element} 表单组件
     */
    renderFormComponent(column, row, rowIndex) {
      const formInputConfig = this.getFormInputConfig(row, column);
      const formItemConfig = this.getFormItemConfig(column);
      const { prop, component, events, showOverflow } = column;
      const params = {
        row: this.tableData[rowIndex],
        column,
        tableColumns: this.tableColumns,
        rowIndex,
      };

      // 只在编辑模式下构建prop路径
      const formItemName =
        this.mode === 'edit' && prop && this.tableDataName ? `${this.tableDataName}.${rowIndex}.${prop}` : undefined;

      const formItemComponent = constantComponent[component];
      // 使用 debounce 包装 change 事件处理器
      const handleChange = debounce((value, context) => {
        if (events?.change) {
          events.change(value, { ...context, ...params });
        }
      }, 50);

      return (
        <el-form-item prop={formItemName} rules={this.mode === 'edit' ? formItemConfig.rules : []} label-width="0">
          {component && prop && (
            <AsyncLoadComponent
              style="max-width: 100%"
              path={formItemComponent}
              mode={formInputConfig.mode || this.mode}
              disabled={this.mode === 'disabled'}
              vModel={this.tableData[rowIndex][prop]}
              ellipsis={showOverflow}
              show-word-limit={formInputConfig.showWordLimit || false}
              {...{
                on: {
                  ...events,
                  change: handleChange,
                },
                attrs: {
                  ...formInputConfig,
                  context: {
                    row,
                    rowIndex,
                    column,
                  },
                },
              }}
            />
          )}
        </el-form-item>
      );
    },
    /**
     * 渲染工具栏按钮组
     * @returns {JSX.Element|Boolean} 工具栏渲染结果
     */
    renderToolbarsArray() {
      return (
        <el-form>
          {!!this.toolbars?.length && (
            <div class="x-edit-table__header-controls">
              {this.toolbars.map((btn) => (
                <x-button
                  key={`btn-${btn.label}`}
                  {...{
                    attrs: {
                      ...btn,
                    },
                  }}
                />
              ))}
            </div>
          )}
        </el-form>
      );
    },
    /**
     * 渲染表格标题区域
     * @returns {JSX.Element|undefined} 标题区域渲染结果
     */
    renderTitle() {
      if (this.title || (typeof this.toolbars === 'function' ? this.toolbars : !!this.toolbars?.length)) {
        return (
          <div class="x-edit-table__header">
            <div class="x-edit-table__header-title">{this.title}</div>
            {this.$slots?.titleRight ? <div class="x-edit-table__header-right">{this.$slots.titleRight}</div> : ''}
            {typeof this.toolbars === 'function' ? this.toolbars() : this.renderToolbarsArray()}
          </div>
        );
      }
    },
    /**
     * @description 渲染分页器
     * @returns {VNode|String} 分页器节点
     */
    renderPagination() {
      if (!this.pagination) return '';
      return (
        <x-pagination
          pageSizes={this.innerPagination.pageSizeOptions}
          show-sizer
          background
          pageNo={this.innerPagination.pageNo}
          pageSize={this.innerPagination.pageSize}
          total={this.tableData.length}
          onChange={this.handlePaginationChange}
        />
      );
    },
    // 使用防抖处理频繁触发的事件
    handlePaginationChange: debounce(function (pageInfo) {
      this.innerPagination.pageNo = pageInfo.pageNo;
      this.innerPagination.pageSize = pageInfo.pageSize;
    }, 50),
  },
  render() {
    const editTableClass = { 'x-edit-table': true, 'x-edit-table__readonly': this.mode !== 'edit' };

    return (
      <div class={editTableClass}>
        {this.renderTitle()}
        <div class={['x-edit-table__grid']}>
          <vxe-grid
            ref="table"
            class="x-table__grid"
            showOverflow={this.showOverflow}
            border
            row-height={this.rowHeight}
            tooltip-config={{
              enterable: true,
              contentMethod: this.formatTooltip, // 添加自定义提示格式化
            }}
            min-height="66"
            on-checkbox-all={this.handleSelectionChange}
            on-checkbox-change={this.handleSelectionChange}
            {...{
              scopedSlots: {
                empty: () => <x-empty size={100} />,
                pager: () => this.renderPagination(),
              },
              props: {
                data: this.pagination ? this.pagedTableData : this.tableData,
                columns: this.renderColumns,
                rowConfig: {
                  keyField: '_rowKey',
                  useKey: !!this.draggable ?? true,
                },
                ...this.$attrs,
              },
              on: {
                ...this.$listeners,
              },
            }}
          />
        </div>
        {this.$slots?.bottom}
      </div>
    );
  },
};
</script>
<style lang="scss">
@import '../styles/index.scss';
.vxe-table--body-wrapper {
  @include scrollbar();
}
.x-edit-table {
  width: 100%;
  background-color: #fff;
  border-radius: 3px;
  &__header {
    background: inherit;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 4px;
    border-bottom: 1px solid #eee;
    border-radius: 3px;
    &-title {
      font-size: 16px;
      line-height: 32px;
      font-weight: 600;
      flex-grow: 1;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    &-controls {
      .el-button--variant-base:not(:first-child) {
        margin-left: 12px;
      }
    }
    &-right {
      display: flex;
      align-items: center;
    }
  }

  &__empty {
    &-add {
      padding: 0 0 32px;
    }
  }
  &__grid {
    height: 100%;

    .vxe-body--column > .vxe-cell {
      max-height: initial;
      min-height: 72px;
      height: auto !important;
      padding: 0 var(--vxe-ui-table-cell-padding-default) !important;
      .el-form-item {
        width: 100%;
        margin-bottom: 14px !important;
        margin-top: 14px;
        &__error {
          padding-top: 0px;
          height: 14px;
          line-height: 14px;
        }
      }
    }

    .btn {
      .el-icon {
        cursor: pointer;
        &:hover {
          color: var(--brand-6, #0f45ea);
        }
      }
    }
  }
  &__actions {
    .vxe-cell {
      display: flex;
      padding: var(--vxe-ui-table-cell-padding-default);
    }
    &-item {
      display: block;
      width: 24px;
      height: 24px;
      line-height: 22px !important;
      text-align: center;
      border-radius: 4px;
      background-color: #fff;
      font-size: 16px;
      color: #737a94;
      border: 1px solid #d9e2ec;
      margin: 0 auto;
      cursor: pointer;
      &:hover {
        color: var(--brand-6, #0f45ea);
        border-color: var(--brand-6, #0f45ea);
      }
    }
  }
  &__move {
    .vxe-cell {
      justify-content: center;
    }
  }

  &__pagination {
    margin-top: 12px;
  }

  .vxe-table__title-required {
    display: inline-block;
    margin-right: 4px;
    color: #ff4d4f;
    line-height: 22px;
  }

  &__readonly {
    .vxe-body--column.col--ellipsis > .vxe-cell {
      max-height: initial;
      min-height: 44px;
    }
    .el-form-item__error {
      display: none;
    }
  }
  .x-input,
  .x-select,
  .x-checkbox,
  .x-icon-select,
  .x-input-amount,
  .x-input-number,
  .x-date,
  .x-textarea,
  .x-upload,
  .x-tree-select,
  .x-cascader {
    flex-grow: 1;
    width: 100%;
  }
  .vxe-body--row.sortable-ghost,
  .vxe-body--row.sortable-chosen {
    box-shadow:
      0 1px 10px rgba(0, 0, 0, 0.05),
      0 4px 5px rgba(0, 0, 0, 8%),
      0 2px 4px -1px rgba(0, 0, 0, 12%);
  }
  // 优化表格行hover效果
  .vxe-body--row:hover {
    background-color: rgba(64, 158, 255, 0.1);
    transition: background-color 0.25s ease;
  }
  .el-input__inner {
    height: 40px;
    line-height: 40px;
  }
  .vxe-toolbar {
    padding: 0;
  }
}
.x-table__grid {
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  ::-webkit-scrollbar-thumb {
    border: 0px solid transparent;
    background-clip: content-box;
    border-radius: 10px;
  }
  .vxe-table .vxe-table--scroll-y-top-corner {
    border-radius: 0 3px 3px 0;
  }
}
// 默认主题
html[data-vxe-ui-theme='light'] {
  --x-pagination-text-color: #096dd9;
  --x-pagination-bg-color: #e6f7ff;
  --x-gray-1: #f5f5f5;
  --vxe-ui-table-header-font-color: #15224c;
  --vxe-ui-font-primary-color: var(--brand-6, #0f45ea);
  --vxe-ui-table-header-font-weight: 500;
  --vxe-ui-table-header-background-color: #edf2f6;
  --vxe-ui-font-color: #2f446b;
  --vxe-ui-table-cell-negative-color: #ff4d4f;
  --vxe-ui-table-validate-error-color: #ff4d4f;
  --vxe-ui-table-cell-dirty-update-color: #ff4d4f;
  .x-table__grid {
    ::-webkit-scrollbar-track,
    ::-webkit-scrollbar-corner {
      background-color: #fff;
    }
    ::-webkit-scrollbar-thumb {
      background-color: #bfbfbf;
    }
    ::-webkit-scrollbar-thumb:hover,
    ::-webkit-scrollbar-thumb:active {
      background-color: #787878;
    }
  }
}
// 暗黑主题
html[data-vxe-ui-theme='dark'] {
  --x-pagination-text-color: var(--color-primary);
  --x-pagination-bg-color: #fff;
  --x-gray-1: #fff;
  --vxe-ui-font-color: #15224c;
  --vxe-ui-font-primary-color: var(--brand-6, #0f45ea);
  --vxe-ui-table-header-font-weight: 500;
  --vxe-ui-table-cell-negative-color: #ff4d4f;
  --vxe-ui-table-validate-error-color: #ff4d4f;
  --vxe-ui-table-cell-dirty-update-color: #ff4d4f;

  .x-table__grid {
    ::-webkit-scrollbar-track,
    ::-webkit-scrollbar-corner {
      background-color: #151518;
    }
    ::-webkit-scrollbar-thumb {
      background-color: #bfbfbf;
    }
    ::-webkit-scrollbar-thumb:hover,
    ::-webkit-scrollbar-thumb:active {
      background-color: #a3a6ad;
    }
  }
}
</style>
