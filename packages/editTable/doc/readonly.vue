<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 16:22:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-01 14:41:37
 * @FilePath: /vite-element-components/packages/editTable/doc/readonly.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <el-form ref="form" :model="formData">
      <x-edit-table
        title="只读模式表格"
        v-model="formData.tableData"
        table-data-name="tableData"
        mode="disabled"
        :table-columns="tableColumns"
      >
        <div slot="titleRight">只读模式下不可编辑数据</div>
      </x-edit-table>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        tableData: [
          {
            name: '张三',
            age: 25,
            address: '北京市朝阳区',
            date: '2024-03-15',
          },
          {
            name: '李四',
            age: 30,
            address: '上海市浦东新区',
            date: '2024-03-16',
          },
          {
            name: '王五',
            age: 28,
            address: '广州市天河区',
            date: '2024-03-17',
          },
        ],
      },
    };
  },
  computed: {
    tableColumns() {
      return [
        {
          prop: 'name',
          label: '姓名',
          component: 'input',
        },
        {
          prop: 'age',
          label: '年龄',
          component: 'inputNumber',
        },
        {
          prop: 'address',
          label: '地址',
          component: 'input',
        },
        {
          prop: 'date',
          label: '日期',
          component: 'date',
          formInputConfig: {
            type: 'datetime',
          },
        },
      ];
    },
  },
};
</script>
