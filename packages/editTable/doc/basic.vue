<template>
  <div class="component-view">
    当前是{{ mode }}模式
    {{ formData }}
    <x-form ref="form" v-model="formData" :mode="mode">
      <x-edit-table
        title="我是标题"
        ref="baseTable"
        slot="formAppend"
        draggable
        v-model="formData.tableData"
        table-data-name="tableData"
        min-height="300px"
        :initd-min-row="1"
        :mode="mode"
        :toolbars="configureFieldOutputTableToolbars"
        :table-columns="tableColumns"
        @filter-change="handleFilterChange"
        @added="handleAdded"
        @deleted="handleDeleted"
      >
      </x-edit-table>
    </x-form>
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      mode: 'edit',
      //  aa: 111, 树选择: '1'
      formData: {
        tableData: [
          {
            文本: '1、一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一。\n2、二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二。\n3、三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十',
          },
        ],
      },
    };
  },
  computed: {
    tableColumns() {
      const selectOptions = [
        { label: '章三', value: 1 },
        { label: '李四', value: 2 },
        { label: '王武', value: 3 },
      ];
      return [
        {
          label: '顺序',
          type: 'seq',
          width: 50,
        },
        {
          prop: '下拉框',
          label: '下拉框',
          component: 'select',
          width: 150,
          isRequired: 1,
          formInputConfig: {
            options: selectOptions,
            placeholder: '请选择',
          },
        },
        {
          prop: '文本',
          label: '文本',
          width: 250,
          // component: 'input',
          // showOverflow: true,
          // isRequired: 1,
          // // width: 600,
          // formInputConfig: {
          //   type: 'textarea',
          //   rows: 2,
          //   maxlength: 200,
          //   showWordLimit: true,
          // },
        },
        {
          prop: '文本框',
          label: '文本框',
          component: 'input',
          isRequired: 1,
          formInputConfig: {
            clearable: false,
            slots: {
              suffix: ({ row }) =>
                this.mode === 'edit' ? (
                  <i
                    class="el-icon-plus"
                    onClick={() => {
                      console.log('%c [  ]-95-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', row);
                      if (row.文本框) {
                        this.$set(row, '文本框', '');
                      } else {
                        this.$set(
                          row,
                          '文本框',
                          '一二三四五六七八九十，一二三四五六七八九十一二三四五六七八九十，一二三四五六七八九十一二三四五六七八九十，一二三四五六七八九十一二三四五六七八九十，一二三四五六七八九十一二三四五六七八九十，一二三四五六七八九十',
                        );
                      }
                    }}
                  />
                ) : (
                  ''
                ),
            },
          },
          events: {
            blur: (e, val) => {
              console.log('%c [  ]-123-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', e, val);
            },
          },
          filters: [{ data: '' }],
          filterRender: {
            name: 'VxeInput',
          },
        },
        {
          prop: '数字输入',
          label: '数字输入',
          component: 'inputNumber',
          isRequired: 1,
          formInputConfig: {
            digits: '2_2',
            min: 0,
            max: 100,
          },
        },
        {
          prop: '开关',
          label: '开关',
          component: 'switch',
          width: 70,
          isRequired: 1,
          formInputConfig: {},
        },
        {
          label: '节点名称',
          prop: 'nodeName',
        },
        {
          label: '开始时间',
          prop: 'beginTime',
          isRequired: 1,
          component: 'date',
          formInputConfig: {
            dateRangeStart: new Date().getTime(),
            dateRangeEnd: ({ row }) => row.endTime,
          },
        },
        {
          label: '结束时间',
          prop: 'endTime',
          isRequired: 1,
          component: 'date',
          formInputConfig: {
            dateRangeStart: ({ row }) => row.beginTime,
            showCustom: true,
          },
        },
      ];
    },
    // 输出表格的操作栏
    configureFieldOutputTableToolbars() {
      return [
        {
          label: '编辑模式',
          type: 'primary',
          show: this.mode !== 'edit',
          onClick: async () => {
            this.mode = 'edit';
          },
        },
        {
          label: '禁用模式',
          type: 'primary',
          show: this.mode !== 'disabled',
          onClick: async () => {
            this.mode = 'disabled';
          },
        },
        {
          label: '查看模式',
          type: 'primary',
          show: this.mode !== 'view',
          onClick: async () => {
            this.mode = 'view';
          },
        },
        {
          label: '提交',
          type: 'primary',
          onClick: async () => {
            this.$refs.form.validate((valid) => {
              console.log('%c [  ]-256-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', valid);
            });
          },
        },
        {
          label: '获取筛选后数据',
          type: 'primary',
          onClick: async () => {
            const { fullData, tableData, visibleData } = this.$refs.baseTable.getTableData();
            console.log(
              '%c [ 筛选后数据 ]-230-「basic」',
              'font-size:13px; background:pink; color:#bf2c9f;',
              fullData, // 完整的全量表体数据
              tableData, // 当前渲染中的表体数据
              visibleData, // 处理条件之后的全量表体数据
            );
          },
        },
        {
          label: '重置',
          type: 'primary',
          onClick: async () => {
            this.$refs.form.resetField();
          },
        },
        {
          label: '导入',
          type: 'primary',
          isLoading: true,
          onClick: async ({ endLoading }) => {
            console.log('%c [  ]-149-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', '导入');
            endLoading();
          },
        },
        {
          label: '导出',
          type: 'primary',
          isLoading: true,
          onClick: ({ endLoading }) => {
            console.log('%c [  ]-149-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', '导出');
            endLoading();
          },
        },
      ];
    },
  },
  created() {
    // this.formData.tableData = new Array(5).fill({}).map((item, index) => {
    //   if (index < 3) {
    //     return { 文本: index };
    //   } else {
    //     return { 文本: '' };
    //   }
    // });
  },
  methods: {
    handleAdded(context) {
      console.log('%c [ 新增 ]-230-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', context);
    },

    handleDeleted(context) {
      console.log('%c [ 删除 ]-230-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', context);
    },
    handleFilterChange(context) {
      console.log('%c [  ]-263-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', context);
    },
  },
};
</script>
