<template>
  <div class="component-view">
    <x-form ref="form" v-model="formData">
      {{ formData.tableData }}
      <x-edit-table
        title="我是标题"
        slot="formAppend"
        v-model="formData.tableData"
        table-data-name="tableData"
        max="100"
        height="400px"
        draggable
        :initd-min-row="2"
        :scroll-y="{ enabled: true, gt: 20 }"
        :toolbars="configureFieldOutputTableToolbars"
        :table-columns="tableColumns"
        :increase-row-function="customIncreaseRow"
        :selection-change="handleSelectionChange"
        @dragEnd="handleDraggable"
      >
        <div slot="titleRight">我是标题右部插槽</div>
        <div slot="top">上插槽</div>
        <div slot="bottom">下插槽</div>
      </x-edit-table>
    </x-form>
    <div style="margin-top: 10px">
      <el-button theme="primary" type="submit" @click="handleSubmit">提交</el-button>
    </div>
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      formData: {
        tableData: [],
      },
    };
  },
  computed: {
    tableColumns() {
      const selectOptions = [
        { label: '章三', value: 1 },
        { label: '李四', value: 2 },
        { label: '王武', value: 3 },
      ];
      return [
        {
          type: 'checkbox',
        },
        {
          prop: 'a1a',
          label: 'a1a',
          component: 'input',
          width: '180px',
        },
        {
          prop: 'aa32',
          label: 'aa32',
          component: 'input',
          width: '180px',
          isRequired: 1,
          formInputConfig: {
            readonly: true,
            slots: {
              suffix: ({ row, rowIndex }) => (
                <i
                  class="el-icon-plus"
                  onClick={() => {
                    console.log(
                      '%c [  ]-780-「update-form」',
                      'font-size:13px; background:pink; color:#bf2c9f;',
                      row,
                      rowIndex,
                    );
                  }}
                />
              ),
            },
          },
          events: {
            blur: (e, val) => {
              console.log('%c [  ]-123-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', e, val);
            },
          },
        },
        {
          prop: 'aa',
          label: 'aa',
          component: 'input',
          isRequired: 1,
          width: '180px',
          formInputConfig: {
            maxlength: 100,
          },
          rules: [
            {
              validator: (rule, value, callback) => {
                console.log('%c [  ]-72-「advance」', 'font-size:13px; background:pink; color:#bf2c9f;', value);
                if (value === undefined || value?.length < 5) {
                  callback(new Error('至少 5 个字，中文长度等于英文长度'));
                } else {
                  callback();
                }
              },
              trigger: 'blur',
            },
            {
              validator: (rule, value, callback) => {
                if (value === undefined || value?.length > 20) {
                  callback(new Error('不能超过 20 个字，中文长度等于英文长度'));
                } else {
                  callback();
                }
              },
              trigger: 'blur',
            },
          ],
        },
        {
          prop: '开关',
          label: '开关',
          component: 'switch',
          isRequired: 1,
        },
        {
          prop: '下拉框',
          label: '下拉框',
          component: 'select',
          width: '180px',
          isRequired: 1,
          formInputConfig: {
            options: selectOptions,
          },
          events: {
            change: (value, { context, row, tableColumns }) => {},
          },
          customColumns: ({ row, tableColumns }) =>
            new Promise((resolve) => {
              if (row.开关) {
                tableColumns.formInputConfig.options = [
                  { label: '章三', value: 1 },
                  { label: '李四', value: 2 },
                ];
              } else {
                tableColumns.formInputConfig.options = selectOptions;
              }
              resolve(tableColumns);
            }),
        },
        {
          prop: 'aa3',
          label: 'aa3',
          component: 'custom',
          width: '180px',
          isRequired: 1,
          formInputConfig: {
            valueType: 'string',
            render: ({ row, column, rowIndex }) => (
              <el-input v-model={row.aa3} readonly placeholder="请选择">
                <i
                  slot="suffix"
                  class="el-icon-plus"
                  onClick={() => {
                    console.log('%c [  ]-134-「advance」', 'font-size:13px; background:pink; color:#bf2c9f;', row.aa3);
                    if (row.aa3) {
                      this.$set(row, 'aa3', '');
                    } else {
                      this.$set(row, 'aa3', '1111');
                    }
                  }}
                ></i>
              </el-input>
            ),
          },
          rules: [
            {
              validator: (rule, value, callback) => {
                if (value === undefined || value?.length > 3) {
                  callback(new Error('请输入长度小于等于3的内容'));
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
        },
        {
          prop: '普通下拉',
          label: '普通下拉',
          component: 'select',
          formInputConfig: {
            options: [
              { value: 1, label: '一' },
              { value: 2, label: '二' },
              { value: 3, label: '三' },
              { value: 42, label: '四' },
              { value: 5, label: '五' },
            ],
            multiple: true,
          },
        },
        {
          label: '普通日期',
          prop: '普通日期',
          component: 'date',
          width: '180px',
          formInputConfig: {
            type: 'date',
          },
        },
        {
          label: '数字输入',
          prop: '数字输入',
          component: 'inputNumber',
          width: '180px',
          isRequired: 1,
          formInputConfig: {
            digits: '2_2',
          },
        },
        {
          type: 'actions',
          label: '操作',
          width: 100,
          actions: [
            {
              label: '查看',
              onClick: (row, index, e) => {
                console.log(
                  '%c [ 查看 ]-198-「EditTable」',
                  'font-size:13px; background:pink; color:#bf2c9f;',
                  row,
                  index,
                  e,
                );
              },
            },
            {
              label: '删除',
              theme: 'danger',
              onClick: (row, index, e) => {
                console.log(
                  '%c [ 删除 ]-207-「EditTable」',
                  'font-size:13px; background:pink; color:#bf2c9f;',
                  row,
                  index,
                  e,
                );
              },
            },
          ],
        },
      ];
    },
    // 输出表格的操作栏
    configureFieldOutputTableToolbars() {
      return [
        {
          label: '导入',
          type: 'primary',
          isLoading: true,
          onClick: async ({ endLoading }) => {
            endLoading();
          },
        },
        {
          label: '导出',
          type: 'primary',
          isLoading: true,
          onClick: ({ endLoading }) => {
            endLoading();
          },
        },
      ];
    },
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        console.log('%c [  ]-230-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', valid);
        if (!valid) return false;
        console.log('%c [ 校验通过 ]-560-「BaseForm」', 'font-size:13px; background:pink; color:#bf2c9f;', 111);
      });
    },
    customIncreaseRow() {
      return {
        aa: 222,
      };
    },
    // 全选事件
    handleSelectionChange(val) {
      console.log('%c [ 全选事件 ]-200-「BaseTable」', 'font-size:13px; background:pink; color:#bf2c9f;', val);
    },
    // 拖拽事件结束
    handleDraggable(context) {
      console.log('%c [ 拖拽结束 ]-308-「advance」', 'font-size:13px; background:pink; color:#bf2c9f;', context);
    },
  },
};
</script>
