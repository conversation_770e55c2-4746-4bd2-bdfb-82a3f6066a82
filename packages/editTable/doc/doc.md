<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:44:18
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-28 11:40:36
 * @FilePath: /vite-element-components/packages/editTable/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import AdvanceVue from './advance.vue';
  import ReadonlyVue from './readonly.vue';
  import ValidateVue from './validate.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      AdvanceVue,
      ReadonlyVue,
      ValidateVue,
      Preview
    }
  };
</script>

## EditTable 组件

可编辑表格组件，支持行内编辑、拖拽排序、表单验证等功能。

### 基础用法

基础功能的可编辑表格，支持添加、删除行，以及基本的表单编辑功能。

<basic-vue/>
<preview  comp-name='editTable' demo-name='basic'/>

### 高级用法

展示了更多高级功能，包括：

- 拖拽排序
- 自定义工具栏
- 自定义列配置
- 自定义验证规则
- 自定义操作按钮
- 插槽使用示例

<advance-vue/>
<preview  comp-name='editTable' demo-name='advance'/>

### 只读模式

通过设置 `mode` 属性为 `disabled` 可以将表格设置为只读模式，此时表格数据不可编辑。适用于数据展示场景。

<readonly-vue/>
<preview  comp-name='editTable' demo-name='readonly'/>

### 表单验证

结合表单验证功能，可以对表格数据进行验证。需要设置 `table-data-name` 属性，并在列配置中添加相应的验证规则。支持：

- 必填验证
- 自定义验证规则
- 异步验证
- 联动验证

<validate-vue/>
<preview  comp-name='editTable' demo-name='validate'/>

### Attributes

| 参数                | 说明                                             | 类型           | 可选值             | 默认值   | 必填 |
| ------------------- | ------------------------------------------------ | -------------- | ------------------ | -------- | ---- |
| value/v-model       | 表格绑定的数据                                   | Array          | —                  | []       | T    |
| title               | 标题文本，显示在左上方                           | String         | —                  | —        | N    |
| tableColumns        | 表格列配置                                       | Array          | —                  | []       | T    |
| mode                | 编辑模式                                         | String         | edit/disabled/view | edit     | T    |
| tableDataName       | 表格数据键名，用在校验处，如果表格需要校验则必填 | String         | —                  | —        | N    |
| max                 | 表格最大允许行数                                 | Number/String  | —                  | Infinity | N    |
| allowIncrease       | 是否允许添加行                                   | Boolean        | —                  | true     | N    |
| allowDelete         | 是否允许删除行                                   | Boolean        | —                  | true     | N    |
| initdMinRow         | 初始化时最小行数                                 | Number         | —                  | 0        | N    |
| toolbars            | 操作栏配置，可以是数组或函数                     | Array/Function | —                  | null     | N    |
| draggable           | 是否允许拖动排序                                 | Boolean        | —                  | false    | N    |
| showOverflow        | 表格内容是否换行展示                             | Boolean        | —                  | true     | N    |
| rowHeight           | 自定义行高                                       | String/Number  | —                  | —        | N    |
| increaseRowFunction | 自定义的增加行函数                               | Function       | —                  | null     | N    |
| selectionChange     | 多选选择变化时触发                               | Function       | —                  | null     | N    |

### tableColumns 表格列的配置

| 参数            | 说明                                                   | 类型          | 可选值                                        | 默认值 |
| --------------- | ------------------------------------------------------ | ------------- | --------------------------------------------- | ------ |
| prop            | 列字段名，对应数据中的键名                             | String        | —                                             | —      |
| label           | 列标题                                                 | String        | —                                             | —      |
| component       | 列编辑组件类型                                         | String        | input/select/date/switch/inputNumber/custom等 | —      |
| width           | 列宽度                                                 | String/Number | —                                             | —      |
| isRequired      | 是否必填                                               | Boolean       | —                                             | false  |
| formInputConfig | 表单组件配置，如placeholder、options等                 | Object        | —                                             | {}     |
| events          | 组件事件配置，如change、blur等                         | Object        | —                                             | {}     |
| customColumns   | 自定义列配置函数，可动态修改列配置                     | Function      | —                                             | —      |
| type            | 特殊列类型                                             | String        | actions/move/checkbox/seq/radio/expand        | —      |
| actions         | 操作列按钮配置                                         | Array         | —                                             | —      |
| minShowActions  | 操作列最小显示按钮数                                   | Number        | —                                             | 2      |
| fixed           | 列固定方向                                             | String        | left/right                                    | —      |
| align           | 对齐方式                                               | String        | left/center/right                             | —      |
| className       | 列样式类名                                             | String        | —                                             | —      |
| slots           | 自定义插槽配置                                         | Object        | —                                             | —      |
| ...             | FormItem 配置，支持 element-eoss FormItem 组件所有属性 | Object        | —                                             | —      |

### Events

| 事件名称 | 说明               | 回调参数                                                                                                                    |
| -------- | ------------------ | --------------------------------------------------------------------------------------------------------------------------- |
| added    | 添加行时触发       | { tableData: 当前表格数据, type: 操作类型, row: 新增行数据, rowIndex: 行索引 }                                              |
| deleted  | 删除行时触发       | { tableData: 当前表格数据, type: 操作类型, row: 删除行数据, rowIndex: 行索引 }                                              |
| drag-end | 拖拽排序结束时触发 | { oldIndex: 原始位置, newIndex: 新位置, sortedRow: 被拖拽的行数据, beSortedRow: 目标位置的行数据, tableData: 当前表格数据 } |

### Methods

| 方法名        | 说明                                     | 参数                                                       |
| ------------- | ---------------------------------------- | ---------------------------------------------------------- |
| validate      | 对整个表格数据进行验证                   | callback: (valid: boolean, invalidFields?: object) => void |
| resetFields   | 重置表格中所有字段为初始值并移除校验结果 | —                                                          |
| clearValidate | 清理表格的验证信息                       | —                                                          |
| getTableData  | 获取当前表格数据                         | —                                                          |

### Slots

| 插槽名     | 说明                 | 作用域参数 |
| ---------- | -------------------- | ---------- |
| titleRight | 标题栏右侧自定义内容 | —          |
| bottom     | 表格底部自定义内容   | —          |

### tableColumns.customColumns 函数说明

用于动态修改列配置的函数，接收以下参数：

```javascript
{
  row: Object,       // 当前行数据
  tableColumns: Array // 当前表格的列配置
}
```

返回值为一个Promise，resolve的值为新的列配置对象。

### formInputConfig 配置示例

```javascript
{
  // 输入框类型
  placeholder: '请输入',
  maxlength: 50,
  showWordLimit: true,

  // 选择器类型
  options: [
    { label: '选项1', value: 1 },
    { label: '选项2', value: 2 }
  ],
  multiple: true,
  filterable: true,

  // 日期选择器类型
  type: 'daterange',
  format: 'yyyy-MM-dd',
  valueFormat: 'yyyy-MM-dd'
}
```

### 自定义验证规则示例

```javascript
{
  prop: 'age',
  label: '年龄',
  component: 'inputNumber',
  rules: [
    { required: true, message: '请输入年龄' },
    { validator: (rule, value, callback) => {
      if (value < 0 || value > 120) {
        callback(new Error('年龄必须在0-120岁之间'));
      } else {
        callback();
      }
    }, trigger: 'change' }
  ]
}
```

### 自定义列配置函数示例

```javascript
{
  customColumns: async ({ row, tableColumns }) => {
    // 根据行数据动态修改列配置
    const columns = cloneDeep(tableColumns);
    const typeColumn = columns.find((col) => col.prop === 'type');

    if (typeColumn && row.type === '1') {
      // 当类型为1时，修改其他列的配置
      const amountColumn = columns.find((col) => col.prop === 'amount');
      if (amountColumn) {
        amountColumn.isRequired = true;
        amountColumn.formInputConfig = {
          ...amountColumn.formInputConfig,
          min: 0,
          max: 1000,
        };
      }
    }

    return columns;
  };
}
```

### 性能优化建议

1. **数据量控制**：建议单次加载的数据不超过1000条，以保证表格的流畅性。

2. **列配置缓存**：对于不经常变化的列配置，可以使用customColumns函数的缓存机制：

```javascript
const columnConfigCache = new Map();

customColumns: async ({ row, tableColumns }) => {
  const cacheKey = JSON.stringify(row);
  if (columnConfigCache.has(cacheKey)) {
    return columnConfigCache.get(cacheKey);
  }

  // 计算新的列配置
  const newColumns = // ... 计算逻辑
    columnConfigCache.set(cacheKey, newColumns);
  return newColumns;
};
```

3. **合理使用异步验证**：对于需要远程验证的字段，建议使用防抖处理，避免频繁的请求：

```javascript
rules: [
  {
    validator: debounce(async (rule, value, callback) => {
      try {
        await validateRemote(value);
        callback();
      } catch (error) {
        callback(error);
      }
    }, 300),
    trigger: 'change',
  },
];
```

4. **避免不必要的重渲染**：
   - 使用v-show替代v-if进行条件渲染
   - 合理使用计算属性缓存计算结果
   - 对于大量静态内容，使用v-once指令
