<template>
  <div class="component-view">
    <el-form ref="form" :model="formData">
      <x-edit-table
        title="表单验证表格"
        v-model="formData.tableData"
        table-data-name="tableData"
        :table-columns="tableColumns"
      >
        <div slot="titleRight">带有表单验证的表格示例</div>
      </x-edit-table>
      <div style="margin-top: 10px">
        <el-button type="primary" @click="handleSubmit">提交验证</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        tableData: [
          {
            name: '张三',
            phone: '13800138000',
            email: '<EMAIL>',
            age: 25,
          },
        ],
      },
    };
  },
  computed: {
    tableColumns() {
      return [
        {
          prop: 'name',
          label: '姓名',
          component: 'input',
          width: '150px',
          isRequired: 1,
          formInputConfig: {
            maxlength: 20,
          },
          rules: [
            {
              validator: (rule, value, callback) => {
                if (value?.length >= 3) {
                  callback(new Error('请输入长度小于3的内容'));
                } else {
                  callback();
                }
              },
              type: 'error',
            },
          ],
        },
        {
          prop: 'phone',
          label: '手机号',
          component: 'input',
          isRequired: 1,
          rules: [
            {
              validator: (rule, value, callback) => {
                if (!/^1[3-9]\d{9}$/.test(value)) {
                  callback(new Error('请输入正确的手机号'));
                } else {
                  callback();
                }
              },
              type: 'error',
            },
          ],
        },
        {
          prop: 'email',
          label: '邮箱',
          component: 'input',
          isRequired: 1,
          rules: [
            {
              validator: (rule, value, callback) => {
                if (!/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(value)) {
                  callback(new Error('请输入正确的邮箱'));
                } else {
                  callback();
                }
              },
              type: 'error',
            },
          ],
        },
        {
          prop: 'age',
          label: '年龄',
          component: 'inputNumber',
          isRequired: 1,
          formInputConfig: {
            min: 18,
            max: 60,
          },
          rules: [
            {
              validator: (rule, value, callback) => {
                if (value < 18 && value > 60) {
                  callback(new Error('年龄必须在18-60岁之间'));
                } else {
                  callback();
                }
              },
              type: 'error',
            },
          ],
        },
      ];
    },
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$message.success('验证通过！');
        } else {
          this.$message.error('请检查表单填写是否正确！');
        }
      });
    },
  },
};
</script>
