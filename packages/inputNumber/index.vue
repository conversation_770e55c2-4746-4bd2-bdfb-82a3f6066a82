<script lang="jsx">
import Tooltip from '../tooltip/index.vue';

/**
 * @description 数字输入框组件，支持整数和小数输入，具有数值范围限制、精度控制等功能
 * <AUTHOR>
 * @module components/XInputNumber
 */
export default {
  name: 'XInputNumber',
  title: '数字输入组件',
  components: { Tooltip },
  props: {
    /**
     * @description 绑定值
     * @type {string|number}
     * @default undefined
     */
    value: {
      type: [String, Number],
      default: undefined,
    },
    /**
     * @description 输入框占位文本
     * @type {string}
     * @default '请输入'
     */
    placeholder: {
      type: String,
      default: '请输入',
    },
    /**
     * @description 修饰符配置对象，用于控制输入行为
     * @type {object}
     * @property {boolean} negative - 是否允许输入负数
     * @property {boolean} short - 是否允许为空
     * @property {boolean} canEmpty - 是否允许小数位不补零
     */
    modifiers: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 输入框宽度
     * @type {string|number}
     * @default '100%'
     */
    width: {
      type: [String, Number],
      default: '100%',
    },
    /**
     * @description 默认值
     * @type {number}
     * @default null
     */
    defaultValue: {
      type: Number,
      default: undefined,
    },
    /**
     * 编辑模式
     * @type {String}
     * @default 'edit'
     */
    mode: {
      type: String,
      default: 'edit',
    },
    /**
     * 是否禁用
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 输入框尺寸
     * @type {string}
     * @default 'small'
     * @values 'medium'|'small'|'mini'
     */
    size: {
      type: String,
      default: '',
    },
    /**
     * @description 是否显示控制按钮
     * @type {boolean}
     * @default false
     */
    controls: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 可编辑表格使用，绑定值
     * @type {Object}
     * @default ''
     */
    context: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      defaultModifiers: {
        negative: true, // 指令修饰符 允许输入负数，否则默认整数（含小数）
        short: true, // 指令修饰符 可以为空 加上后为空字符串时不格式化为0
        canEmpty: true, // 指令修饰符 小数位不补零
      },
    };
  },
  computed: {
    inputValue: {
      get() {
        return this.value || this.value === 0 ? this.value : this.defaultValue;
      },
      set(value) {
        this.$emit('update:value', value);
      },
    },
    // 直接绑定无效，则使用计算属性生成宽度字符串
    widthStyle() {
      return `width:${this.width}`;
    },
    // 最终的修饰符
    finallyModifiers() {
      return {
        ...this.defaultModifiers,
        ...this.modifiers,
      };
    },
    // 整数位
    integerBits() {
      if (this.$attrs?.max) {
        const str = this.$attrs?.max.toString();
        const match = str.match(/^\d+/);
        return match ? match[0].length : 0;
      }
      return this.$attrs?.digits?.split('_')[0] || 9;
    },
    // 小数位
    decimalPlaces() {
      if (this.$attrs?.min) {
        const str = this.$attrs?.min.toString();
        const match = str.match(/\.\d+/);
        return match ? match[0].length - 1 : 0;
      }
      return this.$attrs?.digits?.split('_')[1] || 4;
    },
    // 最大值
    max() {
      if (this.$attrs.max) {
        return this.$attrs.max;
      }
      // eslint-disable-next-line no-restricted-properties
      return Math.pow(10, this.integerBits);
    },
    // 最小值
    min() {
      if (this.$attrs.min) {
        return this.$attrs.min;
      }

      if (!this.finallyModifiers.negative) {
        return 0;
      }
      // eslint-disable-next-line no-restricted-properties
      return -Math.pow(10, 9);
    },
  },
  methods: {
    /**
     * @description 获取显示值，包含后缀处理
     * @returns {string} 格式化后的显示值
     */
    getValue() {
      let content =
        this.inputValue !== '' && this.inputValue !== null && this.inputValue !== undefined ? this.inputValue : '';

      if (this.$attrs?.suffix && content) {
        content += this.$attrs.suffix;
      }

      return content !== '' && content !== null ? content : '-';
    },
  },
  render() {
    // v-input-money:9_4.negative.short.canEmpty

    return (
      <div class="x-input-number">
        {this.mode === 'view' ? (
          <Tooltip content={this.getValue()} />
        ) : (
          <el-input-number
            vModel={this.inputValue}
            placeholder={this.placeholder}
            style={this.widthStyle}
            max={this.max}
            min={this.min}
            size={this.size}
            controls={this.controls}
            disabled={this.disabled || this.mode === 'disabled'}
            class={{
              tl: !this.controls,
            }}
            {...{
              directives: [
                {
                  name: 'input-money',
                  modifiers: this.finallyModifiers,
                  arg: this.$attrs?.digits, // 数字的整数位和小数位
                },
              ],
              props: this.$attrs,
              on: {
                ...Object.entries(this.$listeners).reduce((acc, [event, handler]) => {
                  acc[event] = (...args) => handler(...args, this.context);
                  return acc;
                }, {}),
              },
            }}
          />
        )}
      </div>
    );
  },
};
</script>
<style lang="scss" scoped>
@import '../styles/index.scss';
.tl.el-input-number {
  ::v-deep {
    .el-input__inner {
      text-align: left;
    }
  }
}
</style>
