<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:44:32
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-06 14:20:27
 * @FilePath: /vite-element-components/packages/inputNumber/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import SizeVue from './size.vue';
  import LimitVue from './limit.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      SizeVue,
      LimitVue,
      Preview
    }
  };
</script>

## InputNumber 数字输入框组件

基于 Element UI 的数字输入框组件封装，支持整数和小数输入，具有数值范围限制、精度控制等功能。

### 基础用法

<basic-vue/>
<preview  comp-name='inputNumber' demo-name='basic'/>

### 不同尺寸

<size-vue/>
<preview  comp-name='inputNumber' demo-name='size'/>

### 数值范围和精度控制

<limit-vue/>
<preview  comp-name='inputNumber' demo-name='limit'/>

### 属性说明

| 参数            | 说明                                     | 类型            | 可选值                | 默认值   |
| --------------- | ---------------------------------------- | --------------- | --------------------- | -------- |
| value / v-model | 绑定值                                   | string / number | —                     | ''       |
| placeholder     | 输入框占位文本                           | string          | —                     | '请输入' |
| modifiers       | 修饰符配置对象                           | object          | —                     | {}       |
| width           | 输入框宽度                               | string / number | —                     | '100%'   |
| disabled        | 是否禁用                                 | boolean         | —                     | false    |
| size            | 输入框尺寸                               | string          | medium / small / mini | small    |
| max             | 最大值                                   | number          | —                     | 10^9     |
| min             | 最小值                                   | number          | —                     | 0        |
| digits          | 整数位和小数位限制，格式：整数位\_小数位 | string          | —                     | '9_4'    |
| suffix          | 后缀文本                                 | string          | —                     | —        |

### 修饰符说明

| 修饰符   | 说明                            | 默认值 |
| -------- | ------------------------------- | ------ |
| negative | 是否允许输入负数                | true   |
| short    | 是否允许为空（不自动格式化为0） | true   |
| canEmpty | 是否允许小数位不补零            | true   |

### 事件

如果是可编辑表格内的输入框，事件回调会返回当前行数据，否则返回当前输入框的值。

| 事件名称 | 说明                   | 回调参数                  |
| -------- | ---------------------- | ------------------------- |
| change   | 在值改变时触发         | (value: string \| number) |
| input    | 在输入值时触发         | (value: string \| number) |
| blur     | 在输入框失去焦点时触发 | (event: Event)            |
| focus    | 在输入框获得焦点时触发 | (event: Event)            |

### 使用注意事项

1. 当设置了`digits`属性时，输入的数值将按照指定的整数位和小数位进行限制
2. 使用`modifiers`可以灵活控制输入框的行为，如是否允许负数、是否允许为空等
3. 在详情模式下（`mode='view'`），输入框将以只读方式展示数值
4. 当设置了`max`或`min`属性时，输入的数值将被限制在指定范围内
