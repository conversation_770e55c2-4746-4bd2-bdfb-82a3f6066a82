<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-07 17:35:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-07 17:35:00
 * @FilePath: /vite-element-components/packages/inputNumber/doc/limit.vue
 * @Description: 数值范围和精度控制示例
-->
<template>
  <div class="component-view">
    <p>
      整数输入（0-100）：
      <x-input-number v-model="intValue" :min="0" :max="100" digits="3_0" placeholder="请输入0-100的整数" />
    </p>
    <p>
      小数输入（精度0.01）：
      <x-input-number v-model="decimalValue" :min="0" :max="9999.99" digits="4_2" placeholder="请输入金额" />
    </p>
    <p>
      负数输入（-1000到1000）：
      <x-input-number
        v-model="negativeValue"
        :min="-1000"
        :max="1000"
        digits="4_2"
        :modifiers="{ negative: true }"
        placeholder="支持负数输入"
      />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      intValue: '',
      decimalValue: '',
      negativeValue: '',
    };
  },
};
</script>
