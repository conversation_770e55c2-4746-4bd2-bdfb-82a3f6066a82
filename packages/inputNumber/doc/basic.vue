<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:44:32
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-11 15:06:06
 * @FilePath: /vite-element-components/packages/inputNumber/doc/basic.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <p>
      普通输入框：
      <x-input-number v-model="inputValue" digits="9_2" />
      <el-input-number v-model="inputValue" :min="0" :max="9" />
    </p>
    <p>
      详情输入框：
      <x-input-number mode="view" v-model="inputValue" />
    </p>
    <p>
      禁用输入框：
      <x-input-number disabled v-model="inputValue" />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputValue: undefined,
    };
  },
};
</script>
