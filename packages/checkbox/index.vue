<!--
 * @Description: 复选框组件
 * @Version:
 * @Author: <PERSON><PERSON>
 * @Date: 2022-06-21 09:08:23
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-21 17:21:59
-->
<script lang="jsx">
import Tooltip from '../tooltip/index.vue';

/**
 * 复选框组件
 * @component XCheckbox
 * @description 基于 Element UI 的复选框组件封装，支持只读和编辑模式，可自定义选项配置
 */
export default {
  name: 'XCheckbox',
  title: '复选框组件',
  components: { Tooltip },
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    /**
     * 绑定值，可以是字符串、数字或数组
     * @type {string|number|array}
     * @default []
     */
    value: {
      type: [String, Number, Array],
      default: () => [],
    },
    /**
     * 可选项数据源，数组格式
     * @type {array}
     * @default []
     */
    options: {
      type: Array,
      default: () => [],
    },
    /**
     * 组件尺寸
     * @type {string}
     * @default ''
     */
    size: {
      type: String,
      default: '',
    },
    /**
     * 编辑模式
     * @type {String}
     * @default 'edit'
     */
    mode: {
      type: String,
      default: 'edit',
    },
    /**
     * 是否禁用
     */
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    /**
     * 输入值的计算属性
     * @returns {array} 当前选中的值数组
     */
    inputValue: {
      get() {
        if (Array.isArray(this.value)) {
          return this.value;
        }

        if (this.value) {
          return [this.value];
        }

        return [];
      },
      set(value) {
        this.$emit('update:value', value);
      },
    },
    /**
     * 选项键名映射
     * @returns {object} 包含 label 和 value 的键名映射对象
     */
    optionKeyMap() {
      const { keys } = this.$attrs;
      const { label = 'label', value = 'value' } = keys || {};

      return { label, value };
    },
    /**
     * 处理后的选项数据
     * @returns {array} 标准化后的选项数组
     */
    finallyOptions() {
      return (this.options || [])
        .map((option) => {
          if (!option || typeof option !== 'object') return null;
          return {
            label: option[this.optionKeyMap.label],
            value: String(option[this.optionKeyMap.value]),
            disabled: !!option.disabled,
          };
        })
        .filter(Boolean);
    },
  },
  methods: {
    /**
     * 获取选中项的显示文本
     * @returns {string} 选中项的标签文本，多个选中项用顿号分隔
     */
    getValue() {
      const { inputValue, finallyOptions } = this;
      const labels =
        inputValue.length > 0
          ? inputValue.map((value) => {
              const option = finallyOptions.find((option) => option.value === String(value));
              return option ? option.label : '';
            })
          : ['-'];

      return labels.join('、');
    },
  },
  render() {
    return (
      <div class="x-checkbox">
        {this.mode === 'view' ? (
          <Tooltip content={this.getValue()} />
        ) : (
          <el-checkbox-group
            vModel={this.inputValue}
            disabled={this.disabled || this.mode === 'disabled'}
            {...{
              on: this.$listeners,
              props: this.$attrs,
            }}
          >
            {this.finallyOptions.map((option) => (
              <el-checkbox
                key={option.value}
                size={this.size}
                {...{
                  on: option.events,
                  props: {
                    ...option,
                    ...this.$attrs,
                    label: option.value,
                    disabled: option.disabled,
                  },
                }}
              >
                {option.label}
              </el-checkbox>
            ))}
          </el-checkbox-group>
        )}
      </div>
    );
  },
};
</script>

<style lang="scss">
@import '../styles/index.scss';
.el-checkbox-group {
  display: inline-block;
}
</style>
