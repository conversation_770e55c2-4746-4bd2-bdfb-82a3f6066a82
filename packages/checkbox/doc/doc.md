<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:43:59
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-26 17:53:50
 * @FilePath: /vite-element-components/packages/checkbox/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import StyleVue from './style.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      StyleVue,
      Preview
    }
  };
</script>

## Checkbox 组件

基于 Element UI 的复选框组件封装，支持只读和编辑模式，可自定义选项配置。组件提供了灵活的数据绑定方式和丰富的配置选项，适用于各种表单场景。

### 基础用法

组件兼容 element-eoss 组件库 checkbox 的所有属性，同时扩展了更多实用功能。使用 `v-model` 进行数据双向绑定，通过 `options` 配置选项列表。

<basic-vue/>
<preview  comp-name='checkbox' demo-name='basic'/>

### 尺寸和样式

提供了多种尺寸选择，包括默认、中等、小型和迷你尺寸。同时支持带边框的样式展示。

<style-vue/>
<preview  comp-name='checkbox' demo-name='style'/>

### Attributes

| 参数     | 说明                               | 类型                | 可选值            | 默认值                             |
| -------- | ---------------------------------- | ------------------- | ----------------- | ---------------------------------- |
| value    | 复选框绑定的数据，也可以用 v-model | string/number/array | —                 | []                                 |
| options  | 复选框组件的选项列表               | array               | —                 | []                                 |
| mode     | 组件模式                           | string              | edit/view         | edit                               |
| size     | 组件尺寸                           | string              | medium/small/mini | small                              |
| disabled | 是否禁用                           | boolean             | —                 | false                              |
| border   | 是否显示边框                       | boolean             | —                 | false                              |
| keys     | 选项的键名配置                     | object              | —                 | { label: 'label', value: 'value' } |
| ...      | 其他 el-checkbox 组件 Attributes   | —                   | —                 | —                                  |

### Events

| 事件名称 | 说明                     | 回调参数                                   |
| -------- | ------------------------ | ------------------------------------------ |
| change   | 当绑定值变化时触发的事件 | (value: string/number/array, 选中的值数组) |
| input    | 当输入值变化时触发       | (value: string/number/array, 当前输入值)   |

### Options 配置项

选项列表的每个选项对象支持以下属性：

| 参数     | 说明         | 类型          | 可选值 | 默认值 |
| -------- | ------------ | ------------- | ------ | ------ |
| label    | 选项标签文本 | string        | —      | —      |
| value    | 选项值       | string/number | —      | —      |
| disabled | 是否禁用     | boolean       | —      | false  |
| checkAll | 是否为全选项 | boolean       | —      | false  |

### Slots

| 名称    | 说明                                |
| ------- | ----------------------------------- |
| default | 自定义选项的内容，参数为 { option } |
