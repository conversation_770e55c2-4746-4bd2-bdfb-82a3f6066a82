<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 16:03:39
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-10 16:06:20
 * @FilePath: /vite-element-components/packages/checkbox/doc/style.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <p>
      默认尺寸：
      <x-checkbox v-model="inputValue" :options="options" />
    </p>
    <p>
      中等尺寸：
      <x-checkbox v-model="inputValue" size="medium" border :options="options" />
    </p>
    <p>
      小型尺寸：
      <x-checkbox v-model="inputValue" size="small" border :options="options" />
    </p>
    <p>
      迷你尺寸：
      <x-checkbox v-model="inputValue" size="mini" border :options="options" />
    </p>
    <p>
      带边框样式：
      <x-checkbox v-model="inputValue" border :options="options" />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputValue: [],
      options: [
        {
          value: 1,
          label: '选项1',
        },
        {
          value: 2,
          label: '选项2',
        },
        {
          value: 3,
          label: '选项3',
        },
      ],
    };
  },
};
</script>
