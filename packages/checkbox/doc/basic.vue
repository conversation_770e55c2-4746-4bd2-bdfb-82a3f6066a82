<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-18 11:46:13
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-25 20:50:38
 * @FilePath: /vite-element-components/packages/checkbox/doc/basic.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <p>
      普通复选框：
      <x-checkbox v-model="inputValue" :options="options" :keys="keys" />
    </p>
    <p>
      阅读状态复选框：
      <x-checkbox v-model="inputValue" mode="view" :options="options" :keys="keys" />
    </p>
    <p>
      禁用复选框：
      <x-checkbox v-model="inputValue" disabled :options="options" :keys="keys" />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputValue: [],
      options: [
        { label1: '张三', value1: 1 },
        { label1: '李四', value1: 2 },
        { label1: '王五', value1: 3 },
      ],
      keys: {
        value: 'value1',
        label: 'label1',
      },
    };
  },
};
</script>
