<!--
 * @Description:
 * @Version:
 * @Author: <PERSON><PERSON>
 * @Date: 2022-05-30 19:05:06
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-21 17:21:31
-->

<script lang="jsx">
import Tooltip from '../tooltip/index.vue';
/**
 * 判断一个值是否有效
 * @param {*} val
 * @returns boolean
 */

const valuable = (val) => val !== '' && val !== null && val !== undefined;

export default {
  name: 'XRadio',
  title: '单选框组件',
  components: { Tooltip },
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    /**
     * 选中的值
     */
    value: {
      type: [String, Number, Boolean],
      default: () => null,
    },
    /**
     * 单选框选项数组
     * @example [{ label: '选项1', value: 1, disabled: false }]
     */
    options: {
      type: Array,
      default: () => [],
    },
    /**
     * 编辑模式
     * @type {String}
     * @default 'edit'
     */
    mode: {
      type: String,
      default: 'edit',
    },
    /**
     * 是否禁用
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * 单选框尺寸
     * @values medium / small / mini
     */
    size: {
      type: String,
      default: '',
    },
    /**
     * 是否使用按钮样式
     * @type {boolean}
     * @default false
     */
    button: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    inputValue: {
      get() {
        return valuable(this.value) ? this.value : '';
      },
      set(value) {
        this.$emit('update:value', value);
      },
    },
  },
  methods: {
    getValue() {
      const selectOption = this.options?.find((option) => option.value === this.inputValue);
      return valuable(this.inputValue) && selectOption ? selectOption.label : '-';
    },
  },
  render() {
    return (
      <div class="x-radio">
        {this.mode === 'view' ? (
          <Tooltip content={this.getValue()} />
        ) : (
          <el-radio-group
            vModel={this.inputValue}
            disabled={this.disabled || this.mode === 'disabled'}
            size={this.size}
            {...{
              on: this.$listeners,
              props: this.$attrs,
            }}
          >
            {this.options.map((option) =>
              this.button ? (
                <el-radio-button
                  {...{
                    on: option.events,
                    props: { ...this.$attrs, ...option, label: option.value },
                  }}
                >
                  {option.label}
                </el-radio-button>
              ) : (
                <el-radio
                  {...{
                    on: option.events,
                    props: { ...this.$attrs, ...option, label: option.value },
                  }}
                >
                  {option.label}
                </el-radio>
              ),
            )}
          </el-radio-group>
        )}
      </div>
    );
  },
};
</script>

<style lang="scss" scoped>
@import '../styles/index.scss';
.x-radio {
  width: 100%;
}
</style>
