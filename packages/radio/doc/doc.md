<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-26 17:55:32
 * @FilePath: /vite-element-components/packages/radio/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import SizeVue from './size.vue';
  import BorderVue from './border.vue';
  import ButtonVue from './button.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      SizeVue,
      BorderVue,
      ButtonVue,
      Preview
    }
  };
</script>

## Radio 组件

单选框组件，支持基础单选、只读和禁用状态。

### 基础用法

<basic-vue/>
<preview  comp-name='radio' demo-name='basic'/>

### 不同尺寸

可以设置不同尺寸的单选框，包括大、中、小三种尺寸。

<size-vue/>
<preview  comp-name='radio' demo-name='size'/>

### 带边框

设置border属性可以渲染为带边框的单选框。

<border-vue/>
<preview  comp-name='radio' demo-name='border'/>

### 按钮样式

设置button属性可以渲染为按钮样式的单选框组。

<button-vue/>
<preview  comp-name='radio' demo-name='button'/>

### Attributes

| 参数     | 说明                                                                             | 类型                      | 可选值                | 默认值 |
| -------- | -------------------------------------------------------------------------------- | ------------------------- | --------------------- | ------ |
| v-model  | 绑定值                                                                           | string / number / boolean | —                     | —      |
| options  | 单选项数组，每项包含label（显示文本）、value（选项值）、disabled（是否禁用）属性 | array                     | —                     | []     |
| readonly | 是否只读                                                                         | boolean                   | —                     | false  |
| disabled | 是否禁用                                                                         | boolean                   | —                     | false  |
| mode     | 编辑模式                                                                         | boolean                   | —                     | false  |
| size     | 单选框尺寸                                                                       | string                    | medium / small / mini | small  |
| border   | 是否显示边框                                                                     | boolean                   | —                     | false  |
| button   | 是否使用按钮样式                                                                 | boolean                   | —                     | false  |

### Events

| 事件名称 | 说明                   | 回调参数                                                                |
| -------- | ---------------------- | ----------------------------------------------------------------------- |
| change   | 绑定值变化时触发的事件 | function(value: string \| number \| boolean)，参数为当前选中项的value值 |

### 使用限制

1. options数组中的每个选项必须包含label和value属性，disabled属性可选。
2. 当设置button属性为true时，border属性将不生效。
3. size属性仅在border或button为true时生效。
4. 当设置readonly或disabled为true时，组件将不响应用户交互。
5. mode='view'时，组件将以只读的形式展示，适用于详情页面。
