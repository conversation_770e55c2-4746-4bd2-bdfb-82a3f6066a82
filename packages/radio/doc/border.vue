<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-03 17:18:39
 * @FilePath: /vite-element-components/packages/radio/doc/border.vue
 * @Description: 带边框的单选框示例
-->
<template>
  <div class="component-view">
    <p>
      普通边框：
      <x-Radio v-model="inputValue" :options="options" border />
    </p>
    <p>
      只读边框：
      <x-Radio readonly v-model="inputValue" :options="options" border />
    </p>
    <p>
      禁用边框：
      <x-Radio disabled v-model="inputValue" :options="options" border />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputValue: '',
      options: [
        { label: '选项A', value: 'A' },
        { label: '选项B', value: 'B' },
        { label: '选项C', value: 'C' },
      ],
    };
  },
};
</script>
