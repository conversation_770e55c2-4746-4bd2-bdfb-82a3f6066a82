<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-03 17:18:39
 * @FilePath: /vite-element-components/packages/radio/doc/button.vue
 * @Description: 按钮样式的单选框示例
-->
<template>
  <div class="component-view">
    <p>
      普通按钮：
      <x-Radio v-model="inputValue" :options="options" button />
    </p>
    <p>
      只读按钮：
      <x-Radio readonly v-model="inputValue" :options="options" button />
    </p>
    <p>
      禁用按钮：
      <x-Radio disabled v-model="inputValue" :options="options" button />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputValue: '',
      options: [
        { label: '北京', value: 'beijing' },
        { label: '上海', value: 'shanghai' },
        { label: '广州', value: 'guangzhou' },
      ],
    };
  },
};
</script>
