<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-11 10:29:18
 * @FilePath: /vite-element-components/packages/radio/doc/size.vue
 * @Description: 不同尺寸的单选框示例
-->
<template>
  <div class="component-view">
    <p>
      大尺寸仅在border为true时：
      <x-Radio v-model="inputValue" :options="options" border size="medium" />
    </p>
    <p>
      默认尺寸：
      <x-Radio v-model="inputValue" :options="options" border size="small" />
    </p>
    <p>
      小尺寸：
      <x-Radio v-model="inputValue" :options="options" border size="mini" />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputValue: '',
      options: [
        { label: '选项1', value: 1 },
        { label: '选项2', value: 2 },
        { label: '选项3', value: 3 },
      ],
    };
  },
};
</script>
