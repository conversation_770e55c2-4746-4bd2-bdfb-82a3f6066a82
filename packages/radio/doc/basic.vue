<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-08 16:22:27
 * @FilePath: /vite-element-components/packages/radio/doc/basic.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <p>
      普通单选框：
      <x-Radio v-model="inputValue" :options="options" @change="handleChange" />
    </p>
    <p>
      详情单选框：
      <x-Radio mode="view" v-model="inputValue" :options="options" />
    </p>
    <p>
      禁用单选框：
      <x-Radio disabled v-model="inputValue" :options="options" />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputValue: '',
      options: [
        { label: '张三', value: 1 },
        { label: '李四', value: 2 },
        { label: '王五', value: 3 },
      ],
    };
  },
  methods: {
    handleChange(val) {
      console.log('%c [  ]-41-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', val);
    },
  },
};
</script>
