<template>
  <div class="test-container">
    <div class="test-section">
      <h3>1. 基础selectType测试 - 只允许选择type为'folder'的节点</h3>
      <x-select-tree
        v-model="value1"
        :options="treeData"
        select-type="folder"
        placeholder="只能选择文件夹类型节点"
        style="width: 300px"
      />
      <p>选中值: {{ value1 }}</p>
    </div>

    <div class="test-section">
      <h3>2. 多选模式 + selectType测试 - 只允许选择type为'file'的节点</h3>
      <x-select-tree
        v-model="value2"
        :options="treeData"
        select-type="file"
        multiple
        placeholder="只能选择文件类型节点"
        style="width: 300px"
      />
      <p>选中值: {{ value2 }}</p>
    </div>

    <div class="test-section">
      <h3>3. selectType + onlyLeaf 组合测试</h3>
      <x-select-tree
        v-model="value3"
        :options="treeData"
        select-type="file"
        only-leaf
        placeholder="只能选择文件类型的叶子节点"
        style="width: 300px"
      />
      <p>选中值: {{ value3 }}</p>
    </div>

    <div class="test-section">
      <h3>4. 懒加载 + selectType测试</h3>
      <x-select-tree
        v-model="value4"
        :options="[]"
        select-type="folder"
        lazy
        :load="loadNode"
        placeholder="懒加载 + 只能选择文件夹"
        style="width: 300px"
      />
      <p>选中值: {{ value4 }}</p>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value1: '',
      value2: [],
      value3: '',
      value4: '',
      treeData: [
        {
          value: '1',
          label: '根目录',
          type: 'folder',
          children: [
            {
              value: '1-1',
              label: '子文件夹1',
              type: 'folder',
              children: [
                {
                  value: '1-1-1',
                  label: 'document.txt',
                  type: 'file',
                },
                {
                  value: '1-1-2',
                  label: 'image.png',
                  type: 'file',
                },
              ],
            },
            {
              value: '1-2',
              label: 'readme.md',
              type: 'file',
            },
          ],
        },
        {
          value: '2',
          label: '项目文件夹',
          type: 'folder',
          children: [
            {
              value: '2-1',
              label: 'src',
              type: 'folder',
              children: [
                {
                  value: '2-1-1',
                  label: 'main.js',
                  type: 'file',
                },
                {
                  value: '2-1-2',
                  label: 'App.vue',
                  type: 'file',
                },
              ],
            },
            {
              value: '2-2',
              label: 'package.json',
              type: 'file',
            },
          ],
        },
      ],
    };
  },
  methods: {
    loadNode(node, resolve) {
      if (node.level === 0) {
        // 根节点
        resolve([
          {
            value: 'lazy-1',
            label: '懒加载文件夹1',
            type: 'folder',
            leaf: false,
          },
          {
            value: 'lazy-2',
            label: '懒加载文件夹2',
            type: 'folder',
            leaf: false,
          },
        ]);
      } else if (node.level === 1) {
        // 一级节点
        resolve([
          {
            value: `${node.data.value}-1`,
            label: '子文件夹',
            type: 'folder',
            leaf: false,
          },
          {
            value: `${node.data.value}-file`,
            label: '文件.txt',
            type: 'file',
            leaf: true,
          },
        ]);
      } else {
        // 二级及以下节点
        resolve([
          {
            value: `${node.data.value}-file1`,
            label: '文件1.txt',
            type: 'file',
            leaf: true,
          },
          {
            value: `${node.data.value}-file2`,
            label: '文件2.txt',
            type: 'file',
            leaf: true,
          },
        ]);
      }
    },
  },
};
</script>
