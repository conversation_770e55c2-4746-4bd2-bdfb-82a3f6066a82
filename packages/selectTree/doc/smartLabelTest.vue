<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-19 10:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-19 10:00:00
 * @FilePath: /vite-element-components/packages/selectTree/doc/smartLabelTest.vue
 * @Description: 测试智能标签合并逻辑
-->
<template>
  <div class="component-view">
    <h3>智能标签合并逻辑测试</h3>

    <div style="margin-bottom: 20px; padding: 15px; background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 4px">
      <h4 style="color: #0ea5e9; margin-top: 0">新逻辑说明：</h4>
      <ul>
        <li>1. 当初始传入valueLabel时使用valueLabel作为基础</li>
        <li>2. 如果有新选中节点，则在初始值基础上新增（value和label都是）</li>
        <li>3. 如果有取消选中初始的节点，则从value和label中删除对应项</li>
        <li>4. 保持value和label的顺序一致性</li>
      </ul>
    </div>

    <div style="margin-bottom: 20px">
      <h4>测试场景1：多选模式 - 初始值 + 新选中</h4>
      <p>
        当前值：<code>{{ value1 }}</code
        ><br />
        当前标签：<code>{{ valueLabel1 }}</code
        ><br />
        <x-select-tree
          v-model="value1"
          :value-label="valueLabel1"
          :lazy="true"
          :load="loadNode"
          :remote-search="true"
          :remote-method="remoteSearchWithLazy"
          :props="{ value: 'id', label: 'label' }"
          multiple
          placeholder="请选择节点（多选）"
          style="width: 400px"
          @change="handleChange1"
        />
      </p>

      <div style="margin-top: 10px">
        <el-button @click="setInitialValue1" type="primary">设置初始值</el-button>
        <el-button @click="addMoreInitialValue1" type="success">追加初始值</el-button>
        <el-button @click="clearValue1">清空值</el-button>
      </div>
    </div>

    <div style="margin-bottom: 20px">
      <h4>测试场景2：单选模式 - 初始值切换</h4>
      <p>
        当前值：<code>{{ value2 }}</code
        ><br />
        当前标签：<code>{{ valueLabel2 }}</code
        ><br />
        <x-select-tree
          v-model="value2"
          :value-label="valueLabel2"
          :lazy="true"
          :load="loadNode"
          :remote-search="true"
          :remote-method="remoteSearchWithLazy"
          :props="{ value: 'id', label: 'label' }"
          placeholder="请选择节点（单选）"
          style="width: 400px"
          @change="handleChange2"
        />
      </p>

      <div style="margin-top: 10px">
        <el-button @click="setInitialValue2" type="primary">设置初始值</el-button>
        <el-button @click="changeInitialValue2" type="warning">更改初始值</el-button>
        <el-button @click="clearValue2">清空值</el-button>
      </div>
    </div>

    <div style="margin-top: 20px; padding: 10px; background: #f5f5f5">
      <h4>测试步骤：</h4>
      <ol>
        <li><strong>设置初始值</strong> - 观察是否正确显示初始valueLabel</li>
        <li><strong>打开下拉框搜索并选择新节点</strong> - 观察是否在初始label后追加新选中的label</li>
        <li><strong>取消选中初始节点</strong> - 观察是否正确删除对应的label</li>
        <li><strong>取消选中新选择的节点</strong> - 观察是否正确删除对应的label</li>
        <li><strong>检查change事件</strong> - 验证value和label数组是否正确对应</li>
      </ol>
    </div>

    <div style="margin-top: 20px">
      <h4>Change事件日志：</h4>
      <div style="max-height: 300px; overflow-y: auto; background: #f9f9f9; padding: 10px">
        <div
          v-for="(log, index) in changeLogs"
          :key="index"
          style="margin-bottom: 8px; padding: 8px; background: white; border-radius: 3px"
        >
          <div style="font-weight: bold; color: #409eff">{{ log.timestamp }}</div>
          <div><strong>Value:</strong> {{ log.value }}</div>
          <div><strong>Label:</strong> {{ log.label }}</div>
          <div><strong>Option Count:</strong> {{ log.optionCount }}</div>
        </div>
        <div v-if="changeLogs.length === 0" style="color: #999; text-align: center; padding: 20px">暂无日志</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 场景1：多选
      value1: [],
      valueLabel1: [],

      // 场景2：单选
      value2: '',
      valueLabel2: '',

      changeLogs: [],

      // 模拟的懒加载数据
      lazyData: [
        {
          label: '一级 1',
          id: '1',
          isLeaf: false,
        },
        {
          label: '一级 2',
          id: '2',
          isLeaf: false,
        },
        {
          label: '一级 3',
          id: '3',
          isLeaf: true,
        },
      ],
      childrenData: {
        1: [
          {
            label: '二级 1-1',
            id: '1-1',
            isLeaf: false,
          },
          {
            label: '二级 1-2',
            id: '1-2',
            isLeaf: true,
          },
        ],
        2: [
          {
            label: '二级 2-1',
            id: '2-1',
            isLeaf: false,
          },
          {
            label: '二级 2-2',
            id: '2-2',
            isLeaf: true,
          },
        ],
        '1-1': [
          {
            label: '三级 1-1-1',
            id: '1-1-1',
            isLeaf: true,
          },
          {
            label: '三级 1-1-2',
            id: '1-1-2',
            isLeaf: true,
          },
        ],
        '2-1': [
          {
            label: '三级 2-1-1',
            id: '2-1-1',
            isLeaf: true,
          },
          {
            label: '三级 2-1-2',
            id: '2-1-2',
            isLeaf: true,
          },
        ],
      },
    };
  },
  methods: {
    // 懒加载节点
    loadNode(node, resolve) {
      if (!node) {
        resolve(this.lazyData);
      } else {
        setTimeout(() => {
          const children = this.childrenData[node.data ? node.data.id : null] || [];
          resolve(children);
        }, 300);
      }
    },

    // 远程搜索（结合懒加载）
    remoteSearchWithLazy(query) {
      return new Promise((resolve) => {
        setTimeout(() => {
          if (!query) {
            resolve(this.lazyData);
            return;
          }

          // 模拟搜索所有数据（包括懒加载的数据）
          const allData = [
            {
              label: '一级 1',
              id: '1',
              children: [
                {
                  label: '二级 1-1',
                  id: '1-1',
                  children: [
                    { label: '三级 1-1-1', id: '1-1-1' },
                    { label: '三级 1-1-2', id: '1-1-2' },
                  ],
                },
                { label: '二级 1-2', id: '1-2' },
              ],
            },
            {
              label: '一级 2',
              id: '2',
              children: [
                {
                  label: '二级 2-1',
                  id: '2-1',
                  children: [
                    { label: '三级 2-1-1', id: '2-1-1' },
                    { label: '三级 2-1-2', id: '2-1-2' },
                  ],
                },
                { label: '二级 2-2', id: '2-2' },
              ],
            },
            { label: '一级 3', id: '3' },
          ];

          // 递归搜索
          const searchInTree = (nodes, keyword) => {
            const result = [];

            nodes.forEach((node) => {
              const isMatch = node.label.toLowerCase().includes(keyword.toLowerCase());
              const childrenResult = node.children ? searchInTree(node.children, keyword) : [];

              if (isMatch || childrenResult.length > 0) {
                result.push({
                  ...node,
                  children: childrenResult.length > 0 ? childrenResult : node.children,
                });
              }
            });

            return result;
          };

          const searchResult = searchInTree(allData, query);
          resolve(searchResult);
        }, 400);
      });
    },

    // 场景1：设置初始值
    setInitialValue1() {
      this.value1 = ['1-1-1', '2-2'];
      this.valueLabel1 = ['三级 1-1-1', '二级 2-2'];
      this.addLog('场景1', '设置初始值', this.value1, this.valueLabel1, 0);
    },

    // 场景1：追加初始值
    addMoreInitialValue1() {
      this.value1 = ['1-1-1', '2-2', '3'];
      this.valueLabel1 = ['三级 1-1-1', '二级 2-2', '一级 3'];
      this.addLog('场景1', '追加初始值', this.value1, this.valueLabel1, 0);
    },

    // 场景1：清空值
    clearValue1() {
      this.value1 = [];
      this.valueLabel1 = [];
      this.addLog('场景1', '清空值', this.value1, this.valueLabel1, 0);
    },

    // 场景2：设置初始值
    setInitialValue2() {
      this.value2 = '1-1-2';
      this.valueLabel2 = '二级 1-2';
      this.addLog('场景2', '设置初始值', this.value2, this.valueLabel2, 0);
    },

    // 场景2：更改初始值
    changeInitialValue2() {
      this.value2 = '2-1-1';
      this.valueLabel2 = '三级 2-1-1';
      this.addLog('场景2', '更改初始值', this.value2, this.valueLabel2, 0);
    },

    // 场景2：清空值
    clearValue2() {
      this.value2 = '';
      this.valueLabel2 = '';
      this.addLog('场景2', '清空值', this.value2, this.valueLabel2, 0);
    },

    // 处理change事件
    handleChange1({ value, label, option }) {
      this.addLog('场景1', '选择变化', value, label, option.length);
    },

    handleChange2({ value, label, option }) {
      this.addLog('场景2', '选择变化', value, label, option.length);
    },

    // 添加日志
    addLog(scene, action, value, label, optionCount) {
      const timestamp = new Date().toLocaleTimeString();
      this.changeLogs.unshift({
        timestamp: `${timestamp} - [${scene}] ${action}`,
        value: JSON.stringify(value),
        label: JSON.stringify(label),
        optionCount,
      });

      // 限制日志数量
      if (this.changeLogs.length > 15) {
        this.changeLogs = this.changeLogs.slice(0, 15);
      }
    },
  },
};
</script>

<style scoped>
.component-view {
  padding: 20px;
}

.component-view h3 {
  color: #409eff;
  margin-bottom: 20px;
}

.component-view h4 {
  color: #606266;
  margin-bottom: 10px;
}

.component-view ul,
.component-view ol {
  padding-left: 20px;
}

.component-view li {
  margin-bottom: 5px;
}

.component-view code {
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}
</style>
