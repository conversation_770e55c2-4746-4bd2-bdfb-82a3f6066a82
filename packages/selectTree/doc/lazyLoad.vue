<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-04 10:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-08 17:51:42
 * @FilePath: /vite-element-components/packages/selectTree/doc/lazyLoad.vue
 * @Description: 增强版下拉树组件懒加载示例
-->
<template>
  <div class="component-view">
    <p>
      懒加载：
      <x-select-tree
        v-model="value1"
        value-label="二级 1-1"
        :lazy="true"
        :props="{ value: 'id' }"
        :load="loadNode"
        placeholder="请选择节点（支持懒加载）"
        style="width: 300px"
        @change="handleChange"
      />
      选中值：{{ value1 }}
    </p>

    <p>
      懒加载（多选）：
      <x-select-tree
        v-model="value2"
        :value-label="['二级 1-1', '二级 1-2']"
        :lazy="true"
        :props="{ value: 'id' }"
        :load="loadNode"
        multiple
        placeholder="请选择节点（支持懒加载）"
        style="width: 300px"
      />
      选中值：{{ value2 }}
    </p>

    <p>
      懒加载 + 远程搜索：
      <x-select-tree
        v-model="value4"
        :lazy="true"
        :load="loadNode"
        :props="{ value: 'id' }"
        :remote-search="true"
        :remote-method="remoteSearchWithLazy"
        placeholder="请选择节点（懒加载+远程搜索）"
        style="width: 300px"
      />
      选中值：{{ value4 }}
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value1: '1-1',
      value2: ['1-1', '1-2'],
      value4: '',
      lazyData: [
        {
          label: '一级 1',
          id: '1',
          isLeaf: false,
        },
        {
          label: '一级 2',
          id: '2',
          isLeaf: false,
        },
        {
          label: '一级 3',
          id: '3',
          isLeaf: true, // 叶子节点
        },
      ],
      // 模拟的子节点数据
      childrenData: {
        1: [
          {
            label: '二级 1-1',
            id: '1-1',
            class: 'el-icon-burger',
            isLeaf: false,
          },
          {
            label: '二级 1-2',
            id: '1-2',
            class: 'el-icon-burger',
            isLeaf: true,
          },
        ],
        2: [
          {
            label: '二级 2-1',
            id: '2-1',
            isLeaf: false,
          },
          {
            label: '二级 2-2',
            id: '2-2',
            isLeaf: true,
          },
        ],
        '1-1': [
          {
            label: '三级 1-1-1',
            id: '1-1-1',
            isLeaf: true,
          },
          {
            label: '三级 1-1-2',
            id: '1-1-2',
            isLeaf: true,
          },
        ],
        '2-1': [
          {
            label: '三级 2-1-1',
            id: '2-1-1',
            isLeaf: true,
          },
          {
            label: '三级 2-1-2',
            id: '2-1-2',
            isLeaf: true,
          },
        ],
      },
    };
  },
  methods: {
    // 懒加载节点
    loadNode(node, resolve) {
      // 此处仅为示例，实际场景调用接口获取数据
      if (!node) {
        resolve(this.lazyData);
      } else {
        if (node?.level === 0) {
          resolve(this.lazyData);
        }
        // 模拟异步加载
        setTimeout(() => {
          const children = this.childrenData[node.data ? node.data.id : null] || [];
          resolve(children);
        }, 500);
      }
    },

    // 远程搜索（结合懒加载）
    remoteSearchWithLazy(query) {
      return new Promise((resolve) => {
        setTimeout(() => {
          if (!query) {
            resolve(this.lazyData);
            return;
          }

          // 模拟搜索所有数据（包括懒加载的数据）
          const allData = [
            {
              label: '一级 1',
              value: '1',
              children: [
                {
                  label: '二级 1-1',
                  value: '1-1',
                  children: [
                    { label: '三级 1-1-1', value: '1-1-1' },
                    { label: '三级 1-1-2', value: '1-1-2' },
                  ],
                },
                { label: '二级 1-2', value: '1-2' },
              ],
            },
            {
              label: '一级 2',
              value: '2',
              children: [
                {
                  label: '二级 2-1',
                  value: '2-1',
                  children: [
                    { label: '三级 2-1-1', value: '2-1-1' },
                    { label: '三级 2-1-2', value: '2-1-2' },
                  ],
                },
                { label: '二级 2-2', value: '2-2' },
              ],
            },
            { label: '一级 3', value: '3' },
          ];

          // 递归搜索
          const searchInTree = (nodes, keyword) => {
            const result = [];

            nodes.forEach((node) => {
              const isMatch = node.label.toLowerCase().includes(keyword.toLowerCase());
              const childrenResult = node.children ? searchInTree(node.children, keyword) : [];

              if (isMatch || childrenResult.length > 0) {
                result.push({
                  ...node,
                  children: childrenResult.length > 0 ? childrenResult : node.children,
                });
              }
            });

            return result;
          };

          const searchResult = searchInTree(allData, query);
          resolve(searchResult);
        }, 600);
      });
    },
    handleChange({ value, option, label }) {
      console.log('%c [  ]-224-「lazyLoad」', 'font-size:13px; background:pink; color:#bf2c9f;', value, option, label);
    },
  },
};
</script>

<style scoped>
.component-view p {
  margin-bottom: 20px;
}
</style>
