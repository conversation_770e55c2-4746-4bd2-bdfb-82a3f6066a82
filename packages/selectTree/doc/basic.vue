<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-04 10:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-08 09:55:07
 * @FilePath: /vite-element-components/packages/selectTree/doc/basic.vue
 * @Description: 增强版下拉树组件基础示例
-->
<template>
  <div class="component-view">
    <p>
      单选：
      <x-select-tree v-model="value1" :options="treeData" placeholder="请选择节点" style="width: 300px" />
      选中值：{{ value1 }}
    </p>

    <p>
      多选：
      <x-select-tree v-model="value2" :options="treeData" multiple placeholder="请选择节点" style="width: 300px" />
      选中值：{{ value2 }}
    </p>

    <p>
      本地搜索（支持高亮）：
      <x-select-tree
        v-model="value3"
        :options="treeData"
        placeholder="请选择节点"
        :highlight-search="true"
        style="width: 300px"
      />
      选中值：{{ value3 }}
    </p>

    <p>
      禁用状态：
      <x-select-tree v-model="value4" :options="treeData" disabled placeholder="请选择节点" style="width: 300px" />
    </p>

    <p>
      查看模式：
      <x-select-tree v-model="value5" :options="treeData" mode="view" style="width: 300px" />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value1: '',
      value2: ['1-1', '2-1'],
      value3: '',
      value4: '1-1-1',
      value5: '2-1',
      treeData: [
        {
          label: '一级 1',
          value: '1',
          children: [
            {
              label: '二级 1-1',
              value: '1-1',
              children: [
                {
                  label: '三级 1-1-1',
                  value: '1-1-1',
                },
                {
                  label: '三级 1-1-2',
                  value: '1-1-2',
                },
              ],
            },
            {
              label: '二级 1-2',
              value: '1-2',
              children: [
                {
                  label: '三级 1-2-1',
                  value: '1-2-1',
                },
              ],
            },
          ],
        },
        {
          label: '一级 2',
          value: '2',
          children: [
            {
              label: '二级 2-1',
              value: '2-1',
              children: [
                {
                  label: '三级 2-1-1',
                  value: '2-1-1',
                },
              ],
            },
            {
              label: '二级 2-2',
              value: '2-2',
            },
          ],
        },
        {
          label: '一级 3',
          value: '3',
          children: [
            {
              label: '二级 3-1',
              value: '3-1',
            },
            {
              label: '二级 3-2',
              value: '3-2',
            },
          ],
        },
      ],
    };
  },
  methods: {
    handleChange(value) {
      console.log('选中值变化：', value);
    },
  },
};
</script>

<style scoped>
.component-view p {
  margin-bottom: 20px;
}
</style>
