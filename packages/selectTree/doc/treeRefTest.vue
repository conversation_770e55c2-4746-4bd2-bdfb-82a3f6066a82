<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-04 10:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-08 17:21:10
 * @FilePath: /vite-element-components/packages/selectTree/doc/treeRefTest.vue
 * @Description: 测试树组件ref问题和选中状态同步
-->
<template>
  <div class="component-view">
    <p>
      多选初始值测试（有初始值）：
      <x-select-tree
        v-model="value1"
        :options="treeData"
        multiple
        placeholder="请选择节点"
        style="width: 300px"
        @change="handleChange"
      />
      选中值：{{ value1 }}
    </p>

    <p>
      单选初始值测试（有初始值）：
      <x-select-tree
        v-model="value2"
        :options="treeData"
        placeholder="请选择节点"
        style="width: 300px"
        @change="handleChange"
      />
      选中值：{{ value2 }}
    </p>

    <p>
      懒加载多选初始值测试：
      <x-select-tree
        v-model="value3"
        :lazy="true"
        :load="loadNode"
        :props="{ value: 'id' }"
        multiple
        placeholder="请选择节点（懒加载）"
        style="width: 300px"
        @change="handleChange"
      />
      选中值：{{ value3 }}
    </p>

    <p>
      动态设置初始值：
      <x-select-tree
        v-model="value4"
        :options="treeData"
        multiple
        placeholder="请选择节点"
        style="width: 300px"
        @change="handleChange"
      />
      <el-button @click="setInitialValue" style="margin-left: 10px">设置初始值</el-button>
      <el-button @click="clearValue" style="margin-left: 10px">清空值</el-button>
      选中值：{{ value4 }}
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value1: ['1-1', '1-2', '2-1'], // 多选有初始值
      value2: '1-1', // 单选有初始值
      value3: ['1-1', '1-2'], // 懒加载多选初始值
      value4: [], // 动态设置初始值
      treeData: [
        {
          label: '一级 1',
          value: '1',
          children: [
            {
              label: '二级 1-1',
              value: '1-1',
              children: [
                {
                  label: '三级 1-1-1',
                  value: '1-1-1',
                },
                {
                  label: '三级 1-1-2',
                  value: '1-1-2',
                },
              ],
            },
            {
              label: '二级 1-2',
              value: '1-2',
              children: [
                {
                  label: '三级 1-2-1',
                  value: '1-2-1',
                },
              ],
            },
          ],
        },
        {
          label: '一级 2',
          value: '2',
          children: [
            {
              label: '二级 2-1',
              value: '2-1',
              children: [
                {
                  label: '三级 2-1-1',
                  value: '2-1-1',
                },
              ],
            },
            {
              label: '二级 2-2',
              value: '2-2',
            },
          ],
        },
        {
          label: '一级 3',
          value: '3',
          children: [
            {
              label: '二级 3-1',
              value: '3-1',
            },
            {
              label: '二级 3-2',
              value: '3-2',
            },
          ],
        },
      ],
      // 模拟的懒加载数据
      lazyData: [
        {
          label: '一级 1',
          id: '1',
          isLeaf: false,
        },
        {
          label: '一级 2',
          id: '2',
          isLeaf: false,
        },
        {
          label: '一级 3',
          id: '3',
          isLeaf: true,
        },
      ],
      childrenData: {
        1: [
          {
            label: '二级 1-1',
            id: '1-1',
            isLeaf: false,
          },
          {
            label: '二级 1-2',
            id: '1-2',
            isLeaf: true,
          },
        ],
        2: [
          {
            label: '二级 2-1',
            id: '2-1',
            isLeaf: false,
          },
          {
            label: '二级 2-2',
            id: '2-2',
            isLeaf: true,
          },
        ],
        '1-1': [
          {
            label: '三级 1-1-1',
            id: '1-1-1',
            isLeaf: true,
          },
          {
            label: '三级 1-1-2',
            id: '1-1-2',
            isLeaf: true,
          },
        ],
        '2-1': [
          {
            label: '三级 2-1-1',
            id: '2-1-1',
            isLeaf: true,
          },
          {
            label: '三级 2-1-2',
            id: '2-1-2',
            isLeaf: true,
          },
        ],
      },
    };
  },
  methods: {
    // 懒加载节点
    loadNode(node, resolve) {
      if (!node) {
        resolve(this.lazyData);
      } else {
        setTimeout(() => {
          const children = this.childrenData[node.data ? node.data.id : null] || [];
          resolve(children);
        }, 500);
      }
    },

    // 设置初始值
    setInitialValue() {
      this.value4 = ['1-1', '2-1', '3'];
    },

    // 清空值
    clearValue() {
      this.value4 = [];
    },

    handleChange({ value, option, label }) {
      console.log('选中值变化：', value, option, label);
    },
  },
};
</script>

<style scoped>
.component-view p {
  margin-bottom: 20px;
}
</style>
