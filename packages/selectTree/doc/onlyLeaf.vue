<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-10 10:40:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-10 10:43:15
 * @FilePath: /vite-element-components/packages/selectTree/doc/onlyLeaf.vue
 * @Description: 增强版下拉树组件 - 只选择叶子节点示例
-->
<template>
  <div class="component-view">
    <h3>只选择叶子节点功能</h3>

    <p>
      单选模式 - 只能选择叶子节点：
      <x-select-tree
        v-model="value1"
        :options="treeData"
        :only-leaf="true"
        placeholder="只能选择叶子节点"
        style="width: 300px"
      />
      选中值：{{ value1 }}
    </p>

    <p>
      多选模式 - 只能选择叶子节点：
      <x-select-tree
        v-model="value2"
        :options="treeData"
        :only-leaf="true"
        multiple
        placeholder="只能选择叶子节点"
        style="width: 300px"
      />
      选中值：{{ value2 }}
    </p>

    <p>
      对比 - 普通模式（可选择任意节点）：
      <x-select-tree
        v-model="value3"
        :options="treeData"
        :only-leaf="false"
        placeholder="可选择任意节点"
        style="width: 300px"
      />
      选中值：{{ value3 }}
    </p>

    <p>
      懒加载模式 - 只能选择叶子节点：
      <x-select-tree
        v-model="value4"
        :options="lazyTreeData"
        :only-leaf="true"
        :lazy="true"
        :load="loadNode"
        placeholder="懒加载模式，只能选择叶子节点"
        style="width: 300px"
      />
      选中值：{{ value4 }}
    </p>

    <div class="description">
      <h4>功能说明：</h4>
      <ul>
        <li>设置 <code>:only-leaf="true"</code> 后，只能选择叶子节点（没有子节点的节点）</li>
        <li>非叶子节点会被禁用，显示为灰色且不可点击</li>
        <li>单选模式下，点击非叶子节点会显示提示信息</li>
        <li>多选模式下，非叶子节点的复选框会被禁用</li>
        <li>支持懒加载模式下的叶子节点判断</li>
        <li>叶子节点判断优先级：isLeaf字段 > children字段</li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value1: '',
      value2: [],
      value3: '',
      value4: '',
      treeData: [
        {
          label: '部门管理',
          value: 'dept',
          children: [
            {
              label: '技术部',
              value: 'tech',
              children: [
                {
                  label: '前端组',
                  value: 'frontend',
                },
                {
                  label: '后端组',
                  value: 'backend',
                },
                {
                  label: '测试组',
                  value: 'test',
                },
              ],
            },
            {
              label: '产品部',
              value: 'product',
              children: [
                {
                  label: '产品设计',
                  value: 'design',
                },
                {
                  label: '产品运营',
                  value: 'operation',
                },
              ],
            },
            {
              label: '人事部',
              value: 'hr',
            },
          ],
        },
        {
          label: '系统管理',
          value: 'system',
          children: [
            {
              label: '用户管理',
              value: 'user',
            },
            {
              label: '角色管理',
              value: 'role',
            },
            {
              label: '权限管理',
              value: 'permission',
            },
          ],
        },
      ],
      lazyTreeData: [],
    };
  },
  methods: {
    // 懒加载方法
    loadNode(node, resolve) {
      if (node.level === 0) {
        // 根节点
        resolve([
          {
            label: '根节点1',
            value: 'root1',
            isLeaf: false,
          },
          {
            label: '根节点2',
            value: 'root2',
            isLeaf: false,
          },
        ]);
      } else if (node.level === 1) {
        // 一级节点
        if (node.data.value === 'root1') {
          resolve([
            {
              label: '子节点1-1',
              value: 'child1-1',
              isLeaf: true,
            },
            {
              label: '子节点1-2',
              value: 'child1-2',
              isLeaf: false,
            },
          ]);
        } else if (node.data.value === 'root2') {
          resolve([
            {
              label: '子节点2-1',
              value: 'child2-1',
              isLeaf: true,
            },
          ]);
        }
      } else if (node.level === 2) {
        // 二级节点
        if (node.data.value === 'child1-2') {
          resolve([
            {
              label: '叶子节点1-2-1',
              value: 'leaf1-2-1',
              isLeaf: true,
            },
            {
              label: '叶子节点1-2-2',
              value: 'leaf1-2-2',
              isLeaf: true,
            },
          ]);
        }
      } else {
        resolve([]);
      }
    },
  },
};
</script>

<style scoped>
.component-view {
  padding: 20px;
}

.component-view p {
  margin-bottom: 20px;
  line-height: 1.6;
}

.description {
  margin-top: 30px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.description h4 {
  margin-top: 0;
  color: #333;
}

.description ul {
  margin: 10px 0;
  padding-left: 20px;
}

.description li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.description code {
  background-color: #e6f7ff;
  padding: 2px 4px;
  border-radius: 2px;
  color: #1890ff;
}
</style>
