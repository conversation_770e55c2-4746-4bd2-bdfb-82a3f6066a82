<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-04 10:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-19 09:36:27
 * @FilePath: /vite-element-components/packages/selectTree/doc/doc.md
 * @Description: 增强版下拉树组件文档
-->
<script>
  import BasicVue from './basic.vue';
  import RemoteSearchVue from './remoteSearch.vue';
  import LazyLoadVue from './lazyLoad.vue';
  import OnlyLeafVue from './onlyLeaf.vue';
  import SelectTypeVue from './selectType.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      RemoteSearchVue,
      LazyLoadVue,
      OnlyLeafVue,
      SelectTypeVue,
      Preview
    }
  };
</script>

## SelectTree 增强版下拉树组件

增强版树形选择器组件，基于 Element UI 的 Select 和 Tree 组件封装，支持单选、多选、搜索、懒加载、远程搜索、搜索高亮等功能。

### 基础用法

通过 `v-model` 进行双向绑定，`options` 属性设置展示数据。

<!-- <basic-vue/>
<preview comp-name='selectTree' demo-name='basic'/> -->

### 远程搜索

启用 `remote-search` 属性并提供 `remote-method` 方法，支持远程搜索功能。

<!-- <remote-search-vue/>
<preview comp-name='selectTree' demo-name='remoteSearch'/> -->

### 懒加载

启用 `lazy` 属性并提供 `load` 方法，支持懒加载子节点。

<lazy-load-vue/>
<preview comp-name='selectTree' demo-name='lazyLoad'/>

### 只选择叶子节点

启用 `only-leaf` 属性，限制只能选择叶子节点（没有子节点的节点）。

<!-- <only-leaf-vue/>
<preview comp-name='selectTree' demo-name='onlyLeaf'/>

### 只选择指定类型节点

启用 `select-type` 属性，限制只能选择指定类型的节点（树节点需包含type属性）。

<select-type-vue/>
<preview comp-name='selectTree' demo-name='selectType'/> -->

### 属性

| 参数              | 说明                                                     | 类型                    | 可选值             | 默认值         |
| ----------------- | -------------------------------------------------------- | ----------------------- | ------------------ | -------------- |
| value / v-model   | 绑定值                                                   | string/number/array     | —                  | ''             |
| multiple          | 是否多选                                                 | boolean                 | —                  | false          |
| mode              | 编辑模式                                                 | string                  | edit/view/disabled | edit           |
| size              | 输入框尺寸                                               | string                  | medium/small/mini  | —              |
| clearable         | 是否可清空                                               | boolean                 | —                  | true           |
| placeholder       | 占位文本                                                 | string                  | —                  | 请选择         |
| options           | 展示数据                                                 | array                   | —                  | []             |
| props             | 配置选项，具体见下表                                     | object                  | —                  | —              |
| checkStrictly     | 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法 | boolean                 | —                  | false          |
| lazy              | 是否懒加载子节点                                         | boolean                 | —                  | false          |
| load              | 加载子树数据的方法，仅当 lazy 为 true 时生效             | function(node, resolve) | —                  | —              |
| filterNodeMethod  | 对树节点进行筛选时执行的方法                             | function(value, data)   | —                  | —              |
| remoteSearch      | 是否启用远程搜索                                         | boolean                 | —                  | false          |
| remoteMethod      | 远程搜索方法，当 remoteSearch 为 true 时生效             | function(query)         | —                  | —              |
| searchDebounce    | 搜索防抖延迟时间（毫秒）                                 | number                  | —                  | 300            |
| showSearchLoading | 是否在搜索时显示加载状态                                 | boolean                 | —                  | true           |
| searchPlaceholder | 搜索时的占位文本                                         | string                  | —                  | 输入关键字搜索 |
| highlightSearch   | 是否支持搜索高亮                                         | boolean                 | —                  | true           |
| onlyLeaf          | 是否只能选择叶子节点                                     | boolean                 | —                  | false          |

### props

| 参数     | 说明                                                   | 类型   | 可选值 | 默认值   |
| -------- | ------------------------------------------------------ | ------ | ------ | -------- |
| label    | 指定节点标签为节点对象的某个属性值                     | string | —      | label    |
| value    | 指定节点值为节点对象的某个属性值                       | string | —      | value    |
| children | 指定子树为节点对象的某个属性值                         | string | —      | children |
| disabled | 指定节点选择框是否禁用为节点对象的某个属性值           | string | —      | disabled |
| isLeaf   | 指定节点是否为叶子节点，仅在指定了 lazy 属性的时候生效 | string | —      | isLeaf   |

### 事件

| 事件名称   | 说明                 | 回调参数                                   |
| ---------- | -------------------- | ------------------------------------------ |
| change     | 选中值发生变化时触发 | 目前的选中值                               |
| clear      | 点击清空按钮时触发   | —                                          |
| node-click | 节点被点击时触发     | 传递给 data 属性的数组中该节点所对应的对象 |

### 方法

| 方法名              | 说明                   | 参数 |
| ------------------- | ---------------------- | ---- |
| updateSelectedLabel | 更新选中节点的显示文本 | —    |
| handleClear         | 清空选择               | —    |

### 注意事项

1. **远程搜索**：当启用 `remote-search` 时，`remote-method` 应返回 Promise，resolve 搜索结果数据
2. **懒加载+搜索**：支持懒加载和搜索功能同时使用，远程搜索时会使用搜索结果数据
3. **搜索高亮**：启用 `highlight-search` 时，搜索关键字会在树节点中高亮显示
4. **防抖搜索**：通过 `search-debounce` 控制搜索防抖延迟，避免频繁请求
5. **只选择叶子节点**：启用 `only-leaf` 时，非叶子节点会被禁用，单选模式下点击非叶子节点会显示提示信息

树形数据格式示例：

```javascript
[
  {
    value: '1',
    label: '一级 1',
    children: [
      {
        value: '1-1',
        label: '二级 1-1',
        children: [
          {
            value: '1-1-1',
            label: '三级 1-1-1',
          },
        ],
      },
    ],
  },
  {
    value: '2',
    label: '一级 2',
    children: [
      {
        value: '2-1',
        label: '二级 2-1',
        children: [
          {
            value: '2-1-1',
            label: '三级 2-1-1',
          },
        ],
      },
    ],
  },
];
```

每个节点对象中的属性名可以通过 `props` 配置进行自定义。
