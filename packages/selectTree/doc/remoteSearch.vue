<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-04 10:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-09 11:22:32
 * @FilePath: /vite-element-components/packages/selectTree/doc/remoteSearch.vue
 * @Description: 增强版下拉树组件远程搜索示例
-->
<template>
  <div class="component-view">
    <p>
      远程搜索：
      <x-select-tree
        v-model="value1"
        :options="initialData"
        :remote-search="true"
        :remote-method="remoteSearch"
        :search-debounce="500"
        placeholder="输入关键字进行远程搜索"
        style="width: 300px"
      />
      选中值：{{ value1 }}
    </p>

    <p>
      远程搜索（多选）：
      <x-select-tree
        v-model="value2"
        :options="initialData"
        :remote-search="true"
        :remote-method="remoteSearch"
        multiple
        placeholder="输入关键字进行远程搜索"
        style="width: 300px"
      />
      选中值：{{ value2 }}
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value1: '',
      value2: [],
      initialData: [
        {
          label: '默认节点 1',
          value: 'default-1',
        },
        {
          label: '默认节点 2',
          value: 'default-2',
        },
      ],
      // 模拟的完整数据源
      fullDataSource: [
        {
          label: '北京市',
          value: 'beijing',
          children: [
            {
              label: '朝阳区',
              value: 'beijing-chaoyang',
              children: [
                { label: '三里屯', value: 'beijing-chaoyang-sanlitun' },
                { label: '国贸', value: 'beijing-chaoyang-guomao' },
              ],
            },
            {
              label: '海淀区',
              value: 'beijing-haidian',
              children: [
                { label: '中关村', value: 'beijing-haidian-zhongguancun' },
                { label: '五道口', value: 'beijing-haidian-wudaokou' },
              ],
            },
          ],
        },
        {
          label: '上海市',
          value: 'shanghai',
          children: [
            {
              label: '浦东新区',
              value: 'shanghai-pudong',
              children: [
                { label: '陆家嘴', value: 'shanghai-pudong-lujiazui' },
                { label: '张江', value: 'shanghai-pudong-zhangjiang' },
              ],
            },
            {
              label: '黄浦区',
              value: 'shanghai-huangpu',
              children: [
                { label: '外滩', value: 'shanghai-huangpu-waitan' },
                { label: '南京路', value: 'shanghai-huangpu-nanjinglu' },
              ],
            },
          ],
        },
        {
          label: '广州市',
          value: 'guangzhou',
          children: [
            {
              label: '天河区',
              value: 'guangzhou-tianhe',
              children: [
                { label: '珠江新城', value: 'guangzhou-tianhe-zhujiangxincheng' },
                { label: '体育中心', value: 'guangzhou-tianhe-tiyuzhongxin' },
              ],
            },
          ],
        },
        {
          label: '深圳市',
          value: 'shenzhen',
          children: [
            {
              label: '南山区',
              value: 'shenzhen-nanshan',
              children: [
                { label: '科技园', value: 'shenzhen-nanshan-kejiyuan' },
                { label: '蛇口', value: 'shenzhen-nanshan-shekou' },
              ],
            },
          ],
        },
      ],
    };
  },
  methods: {
    // 模拟远程搜索
    remoteSearch(query) {
      return new Promise((resolve) => {
        // 模拟网络延迟
        setTimeout(() => {
          if (!query) {
            resolve(this.initialData);
            return;
          }

          // 递归搜索匹配的节点
          const searchInTree = (nodes, keyword) => {
            const result = [];

            nodes.forEach((node) => {
              const isMatch = node.label.toLowerCase().includes(keyword.toLowerCase());
              const childrenResult = node.children ? searchInTree(node.children, keyword) : [];

              if (isMatch || childrenResult.length > 0) {
                result.push({
                  ...node,
                  children: childrenResult.length > 0 ? childrenResult : node.children,
                });
              }
            });

            return result;
          };

          const searchResult = searchInTree(this.fullDataSource, query);
          resolve(searchResult);
        }, 800); // 模拟800ms的网络延迟
      });
    },

    handleChange(value) {
      console.log('选中值变化：', value);
    },
  },
};
</script>

<style scoped>
.component-view p {
  margin-bottom: 20px;
}
</style>
