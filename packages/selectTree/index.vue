<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-04 10:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-19 10:04:00
 * @FilePath: /vite-element-components/packages/selectTree/index.vue
 * @Description: 增强版树形选择器组件，支持懒加载、过滤、搜索，以及懒加载+搜索组合使用，支持只选择叶子节点
-->
<script lang="jsx">
import Tooltip from '../tooltip/index.vue';

/**
 * @description 增强版树形选择器组件，基于 Element UI 的 Select 和 Tree 组件封装，支持单选、多选、搜索、懒加载、远程搜索等功能
 * <AUTHOR>
 */
export default {
  name: 'XSelectTree',
  components: { Tooltip },
  model: {
    prop: 'value',
    event: 'update:value',
  },
  title: '增强版下拉树组件',
  props: {
    /**
     * @description 选中的值，单选时为字符串或数字，多选时为数组
     */
    value: {
      type: [String, Number, Array],
      default() {
        return this.multiple ? [] : '';
      },
    },
    /**
     * @description 选中值对应的标签，单选时为字符串，多选时为数组
     * 如果传入此属性，将优先使用传入的标签进行显示，避免从树数据中查找
     */
    valueLabel: {
      type: [String, Array],
      default: '',
    },
    /**
     * @description 是否多选
     */
    multiple: {
      type: Boolean,
      default: false,
    },
    /**
     * 编辑模式
     * @type {String}
     * @default 'edit'
     */
    mode: {
      type: String,
      default: 'edit',
    },
    /**
     * 是否禁用
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 输入框尺寸，可选值：medium / small / mini
     */
    size: {
      type: String,
      default: '',
    },
    /**
     * @description 是否可清空
     */
    clearable: {
      type: Boolean,
      default: true,
    },
    /**
     * @description 占位文本
     */
    placeholder: {
      type: String,
      default: '请选择',
    },
    /**
     * @description 展示数据
     */
    options: {
      type: Array,
      default: () => [],
    },
    /**
     * @description 配置选项，具体见下表
     */
    props: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法
     */
    checkStrictly: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 是否懒加载子节点，需与 load 方法结合使用
     */
    lazy: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 加载子树数据的方法，仅当 lazy 为 true 时生效
     */
    load: {
      type: Function,
      default: null,
    },
    /**
     * @description 对树节点进行筛选时执行的方法，返回 true 表示这个节点可以显示，返回 false 则表示这个节点会被隐藏
     */
    filterNodeMethod: {
      type: Function,
      default: null,
    },
    /**
     * @description 是否启用远程搜索
     */
    remoteSearch: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 远程搜索方法，当 remoteSearch 为 true 时生效
     */
    remoteMethod: {
      type: Function,
      default: null,
    },
    /**
     * @description 搜索防抖延迟时间（毫秒）
     */
    searchDebounce: {
      type: Number,
      default: 300,
    },
    /**
     * @description 是否在搜索时显示加载状态
     */
    showSearchLoading: {
      type: Boolean,
      default: true,
    },
    /**
     * @description 搜索时的占位文本
     */
    searchPlaceholder: {
      type: String,
      default: '输入关键字搜索',
    },
    /**
     * @description 是否支持搜索高亮
     */
    highlightSearch: {
      type: Boolean,
      default: true,
    },
    /**
     * @description 可编辑表格使用，绑定值
     * @type {Object}
     * @default ''
     */
    context: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 是否只能选择叶子节点
     */
    onlyLeaf: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 允许选择的节点类型，如果设置了该值，只有该类型的节点可以被选择，其他节点将被禁用
     * 通过节点数据中的type字段进行匹配
     */
    selectType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      selectedLabel: '',
      visible: false,
      filterText: '',
      searchLoading: false,
      searchTimer: null,
      originalOptions: [], // 保存原始数据，用于本地搜索恢复
      searchResultOptions: [], // 搜索结果数据
      isSearching: false, // 是否正在搜索状态
      internalOptions: [], // 内部树数据，初始化为空
      hasLoadedRoot: false, // 是否已加载过根节点
      selectedNodesCache: new Map(), // 缓存已选中节点的完整信息，key为节点value，value为节点数据
      selectedOptions: [], // 当前选中的节点集合
      isInternalChange: false, // 标记是否为内部触发的变化，避免重复触发change事件
      isClearOperation: false, // 标记是否为清空操作
      // 新增：用于跟踪初始值和新选中值
      initialValues: [], // 初始传入的value值
      initialLabels: [], // 初始传入的valueLabel值
      treeSelectedValues: [], // 从树组件中选中的value值
      treeSelectedLabels: [], // 从树组件中选中的label值
      defaultProps: {
        label: 'label',
        value: 'value',
        children: 'children',
        disabled: 'disabled',
        isLeaf: 'isLeaf',
      },
    };
  },
  computed: {
    inputValue: {
      get() {
        return this.value;
      },
      set(val) {
        // 确保多选模式下返回数组，单选模式下返回单个值
        let finalValue = val;
        if (this.multiple) {
          finalValue = Array.isArray(val) ? val : [];
        }
        this.$emit('update:value', finalValue);
      },
    },
    // 合并后的props
    finallyProps() {
      return {
        ...this.defaultProps,
        ...this.props,
      };
    },
    /**
     * @description 当前显示的树数据
     * 在懒加载+远程搜索场景下提供更好的支持
     */
    currentTreeOptions() {
      let result;
      // 远程搜索模式下，如果正在搜索则显示搜索结果
      if (this.isSearching && this.remoteSearch) {
        result = this.searchResultOptions;
      } else {
        // 其他情况显示内部 options 数据
        result = this.internalOptions;
      }
      return result;
    },

    /**
     * @description 是否应该显示tree组件
     * 在懒加载模式下，即使options为空也应该显示tree组件以触发load方法
     */
    shouldShowTree() {
      // 如果正在搜索加载，不显示tree
      if (this.searchLoading) {
        return false;
      }
      // 懒加载模式下，总是显示tree组件（让el-tree自己处理懒加载）
      if (this.lazy) {
        return true;
      }
      // 非懒加载模式下，总是显示tree组件，让el-tree自己处理空数据状态
      // 这样可以避免重复显示"暂无数据"的问题
      return true;
    },

    /**
     * @description 是否允许过滤输入
     * 懒加载模式下只有配置了远程搜索才允许输入过滤
     */
    isFilterable() {
      if (this.lazy) {
        // 懒加载模式下，只有配置了远程搜索才允许过滤
        return this.remoteSearch && typeof this.remoteMethod === 'function';
      }
      // 非懒加载模式下，默认允许过滤
      return true;
    },

    // 获取显示值，包含后缀处理
    displayValue() {
      let content = this.selectedLabel || '';
      if (this.$attrs?.suffix && content) {
        content += this.$attrs.suffix;
      }
      return content || '-';
    },
  },
  watch: {
    value: {
      handler(val) {
        // 如果是清空操作，跳过处理，避免重复触发change事件
        if (this.isClearOperation) {
          this.inputValue = val;
          return;
        }

        this.inputValue = val;
        // 重新初始化初始值（当外部value变化时）
        this.initializeInitialValues();
        this.$nextTick(() => {
          this.setTreeSelectionWithRetry();
          // 标记为内部变化，避免重复触发change事件
          this.isInternalChange = true;
          this.updateSelectedLabel();
          this.isInternalChange = false;
        });
      },
      immediate: true,
    },
    options: {
      handler(newOptions) {
        // 保存原始数据
        if (!this.originalOptions.length && newOptions.length) {
          this.originalOptions = JSON.parse(JSON.stringify(newOptions));
        }
        // 只有外部传入非空数组才同步
        if (Array.isArray(newOptions) && newOptions.length > 0) {
          this.internalOptions = [...newOptions];
          this.hasLoadedRoot = true; // 外部传入数据视为已加载

          // 预先缓存当前选中值对应的节点信息
          this.cacheSelectedNodesFromOptions(newOptions);
        }
        // 否则不动，等下拉树打开时再懒加载
        this.$nextTick(() => {
          this.setTreeSelectionWithRetry();
          this.updateSelectedLabel();
        });
      },
      immediate: true,
    },
    valueLabel: {
      handler() {
        // 当valueLabel发生变化时，重新初始化初始值并更新显示标签
        this.initializeInitialValues();
        this.$nextTick(() => {
          this.updateSelectedLabel();
        });
      },
      immediate: true,
    },
    filterText(val) {
      this.handleSearch(val);
    },
    // 监听内部数据变化，确保懒加载模式下数据加载完成后重新设置选中状态
    internalOptions: {
      handler(newOptions) {
        // 只有在懒加载模式下且数据发生变化时才重新设置选中状态
        if (this.lazy && newOptions.length > 0 && this.visible) {
          this.$nextTick(() => {
            this.setTreeSelectionWithRetry();
          });
        }
      },
      deep: true,
    },
  },
  mounted() {
    // 组件挂载后初始化初始值和选中状态标签
    this.initializeInitialValues();
    this.$nextTick(() => {
      this.updateSelectedLabel();
    });
  },
  beforeDestroy() {
    // 组件销毁时清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
      this.searchTimer = null;
    }
  },
  methods: {
    /**
     * @description 初始化初始值和标签
     * 用于跟踪初始传入的value和valueLabel
     */
    initializeInitialValues() {
      // 初始化初始值
      if (this.value) {
        if (this.multiple) {
          this.initialValues = Array.isArray(this.value) ? [...this.value] : [];
        } else {
          this.initialValues = [this.value];
        }
      } else {
        this.initialValues = [];
      }

      // 初始化初始标签
      if (this.valueLabel) {
        if (this.multiple) {
          this.initialLabels = Array.isArray(this.valueLabel) ? [...this.valueLabel] : [];
        } else {
          this.initialLabels = Array.isArray(this.valueLabel) ? [...this.valueLabel] : [this.valueLabel];
        }
      } else {
        this.initialLabels = [];
      }

      // 初始化树选中值为空
      this.treeSelectedValues = [];
      this.treeSelectedLabels = [];
    },

    /**
     * @description 处理搜索逻辑
     * 注意：懒加载模式下仅支持远程搜索，不支持本地过滤
     * @param {string} searchText 搜索文本
     */
    handleSearch(searchText) {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      if (!searchText || searchText.trim() === '') {
        // 清空搜索时恢复原始状态
        this.isSearching = false;
        this.searchLoading = false;
        this.searchResultOptions = [];

        if (!this.remoteSearch) {
          // 在懒加载+过滤场景下，确保tree组件引用可用
          this.$nextTick(() => {
            if (this.$refs.tree && typeof this.$refs.tree.filter === 'function') {
              this.$refs.tree.filter('');
            }
          });
        }
        return;
      }

      this.isSearching = true;

      if (this.remoteSearch) {
        // 远程搜索
        if (this.showSearchLoading) {
          this.searchLoading = true;
        }

        this.searchTimer = setTimeout(() => {
          if (this.remoteMethod && typeof this.remoteMethod === 'function') {
            this.remoteMethod(searchText)
              .then((data) => {
                this.searchResultOptions = data || [];
                this.searchLoading = false;
              })
              .catch((error) => {
                console.error('XSelectTree: 远程搜索失败', error);
                this.searchResultOptions = [];
                this.searchLoading = false;
              });
          } else {
            console.warn('XSelectTree: 启用了远程搜索但未提供remoteMethod方法');
            this.searchLoading = false;
          }
        }, this.searchDebounce);
      } else {
        // 本地搜索
        if (this.lazy) {
          console.warn('XSelectTree: 懒加载模式下不支持本地过滤，请使用 remoteSearch + remoteMethod 进行远程搜索');
          return;
        }

        this.searchTimer = setTimeout(() => {
          // 普通模式下的本地搜索
          this.$nextTick(() => {
            if (this.$refs.tree && typeof this.$refs.tree.filter === 'function') {
              this.$refs.tree.filter(searchText.trim());
            } else {
              // 如果tree组件暂时不可用，再次尝试
              const retryTimer = setTimeout(() => {
                if (this.$refs.tree && typeof this.$refs.tree.filter === 'function') {
                  this.$refs.tree.filter(searchText.trim());
                } else {
                  console.warn('XSelectTree: tree组件未找到或filter方法不可用');
                }
              }, 100);

              // 清理重试定时器
              this.$once('hook:beforeDestroy', () => {
                clearTimeout(retryTimer);
              });
            }
          });
        }, this.searchDebounce);
      }
    },
    /**
     * @description 默认的树节点过滤方法
     * @param {string} value 搜索关键字
     * @param {object} data 树节点数据
     * @returns {boolean} 是否匹配
     */
    filterNode(value, data) {
      if (!value) return true;

      // 确保data存在且有效
      if (!data || typeof data !== 'object') {
        return false;
      }

      // 获取节点标签
      const labelKey = this.finallyProps?.label;
      const label = data[labelKey];

      // 确保label是字符串类型
      if (typeof label !== 'string' && typeof label !== 'number') {
        return false;
      }

      const labelStr = String(label).toLowerCase();
      const searchStr = String(value).toLowerCase();

      // 支持多种匹配方式：包含匹配、拼音首字母匹配等
      return labelStr.includes(searchStr);
    },

    /**
     * @description 更新选中节点的显示文本
     * 智能合并初始valueLabel和新选中的节点label
     * 逻辑：初始valueLabel作为基础，新选中节点追加，取消选中的节点删除
     */
    updateSelectedLabel() {
      if (!this.inputValue) {
        this.selectedLabel = '';
        this.treeSelectedValues = [];
        this.treeSelectedLabels = [];
        return;
      }

      // 获取当前实际的value值
      let currentValues;
      if (this.multiple) {
        currentValues = Array.isArray(this.inputValue) ? this.inputValue : [];
      } else {
        currentValues = [this.inputValue];
      }

      // 更新树选中的值和标签
      this.updateTreeSelectedData(currentValues);

      // 合并初始标签和树选中标签
      const mergedLabels = this.mergeLabels();

      // 更新显示标签
      this.selectedLabel = mergedLabels.join(', ');

      // 标签更新完成后，触发change事件（只在非内部变化时触发）
      if (!this.isInternalChange) {
        this.$nextTick(() => {
          this.emitChangeEvent();
        });
      }
    },

    /**
     * @description 更新树选中的数据
     * @param {Array} currentValues 当前的value值数组
     */
    updateTreeSelectedData(currentValues) {
      // 从tree组件获取选中节点
      let checkedNodes;
      if (this.multiple) {
        checkedNodes = this.$refs.tree?.getCheckedNodes() || [];
      } else {
        const currentNode = this.$refs.tree?.getCurrentNode();
        checkedNodes = currentNode ? [currentNode] : [];
      }

      // 更新树选中的值和标签
      this.treeSelectedValues = checkedNodes.map((node) => node[this.finallyProps.value]);
      this.treeSelectedLabels = checkedNodes.map((node) => node[this.finallyProps.label]);

      // 如果tree组件中没有找到节点，尝试从options或缓存中查找
      const missingValues = currentValues.filter(
        (value) => !this.initialValues.includes(value) && !this.treeSelectedValues.includes(value),
      );

      if (missingValues.length > 0) {
        const foundLabels = this.findLabelsFromOptions(missingValues);
        missingValues.forEach((value, index) => {
          if (foundLabels[index]) {
            this.treeSelectedValues.push(value);
            this.treeSelectedLabels.push(foundLabels[index]);
          }
        });
      }
    },

    /**
     * @description 合并初始标签和树选中标签
     * @returns {Array} 合并后的标签数组
     */
    mergeLabels() {
      const mergedLabels = [];
      let currentValues;
      if (this.multiple) {
        currentValues = Array.isArray(this.inputValue) ? this.inputValue : [];
      } else {
        currentValues = [this.inputValue];
      }

      // 按照当前value的顺序来组织标签
      currentValues.forEach((value) => {
        // 优先从初始值中查找
        const initialIndex = this.initialValues.indexOf(value);
        if (initialIndex !== -1 && this.initialLabels[initialIndex]) {
          mergedLabels.push(this.initialLabels[initialIndex]);
          // 同时缓存初始值节点信息（如果还没有缓存的话）
          if (!this.selectedNodesCache.has(value)) {
            const tempNode = {
              [this.finallyProps.value]: value,
              [this.finallyProps.label]: this.initialLabels[initialIndex],
            };
            this.selectedNodesCache.set(value, tempNode);
          }
          return;
        }

        // 其次从树选中值中查找
        const treeIndex = this.treeSelectedValues.indexOf(value);
        if (treeIndex !== -1 && this.treeSelectedLabels[treeIndex]) {
          mergedLabels.push(this.treeSelectedLabels[treeIndex]);
          return;
        }

        // 最后尝试从缓存中查找
        if (this.selectedNodesCache.has(value)) {
          const cachedNode = this.selectedNodesCache.get(value);
          mergedLabels.push(cachedNode[this.finallyProps.label]);
        }
      });

      return mergedLabels;
    },

    /**
     * @description 智能合并初始值和树选中值
     * @returns {Array} 合并后的值数组
     */
    mergeValues() {
      const mergedValues = [];

      // 首先添加所有初始值（保持原有的初始值）
      this.initialValues.forEach((value) => {
        if (!mergedValues.includes(value)) {
          mergedValues.push(value);
        }
      });

      // 然后添加树选中的值（新选中的节点）
      this.treeSelectedValues.forEach((value) => {
        if (!mergedValues.includes(value)) {
          mergedValues.push(value);
        }
      });

      // 最后，需要检查哪些值被取消选中了
      // 如果某个初始值不在树选中值中，且树组件已经加载了该节点，则说明该值被取消选中
      const finalValues = mergedValues.filter((value) => {
        // 如果是初始值
        if (this.initialValues.includes(value)) {
          // 检查该值是否在树组件中被取消选中
          // 这里需要判断树组件是否已经加载了该节点
          const isInTreeSelected = this.treeSelectedValues.includes(value);
          const nodeInCache = this.selectedNodesCache.has(value);

          // 如果节点在缓存中（说明已经被渲染过），但不在树选中值中，说明被取消选中
          if (nodeInCache && !isInTreeSelected) {
            // 检查树组件中是否真的没有选中该节点
            let checkedNodes;
            if (this.multiple) {
              checkedNodes = this.$refs.tree?.getCheckedNodes() || [];
            } else {
              const currentNode = this.$refs.tree?.getCurrentNode();
              checkedNodes = currentNode ? [currentNode] : [];
            }

            const isReallySelected = checkedNodes.some((node) => node[this.finallyProps.value] === value);
            return isReallySelected;
          }

          // 如果节点还没有被渲染过，保留初始值
          return true;
        }

        // 如果是树选中的值，直接保留
        return this.treeSelectedValues.includes(value);
      });

      return finalValues;
    },

    /**
     * @description 从options中递归查找节点标签
     * 支持懒加载模式下的标签查找
     * @param {Array} values 要查找的值数组
     * @returns {Array} 找到的标签数组
     */
    findLabelsFromOptions(values) {
      if (!Array.isArray(values) || values.length === 0) {
        return [];
      }

      const labels = [];
      const remainingValues = [...values];

      // 优先从缓存中查找
      for (let i = remainingValues.length - 1; i >= 0; i--) {
        const value = remainingValues[i];
        if (this.selectedNodesCache.has(value)) {
          const cachedNode = this.selectedNodesCache.get(value);
          labels.push(cachedNode[this.finallyProps.label]);
          remainingValues.splice(i, 1);
        }
      }

      // 如果还有未找到的值，继续在树数据中查找
      if (remainingValues.length > 0) {
        // 递归查找函数
        const findInNodes = (nodes) => {
          if (!Array.isArray(nodes)) return;

          for (const node of nodes) {
            if (remainingValues.includes(node[this.finallyProps.value])) {
              labels.push(node[this.finallyProps.label]);
              // 将找到的节点缓存起来
              this.selectedNodesCache.set(node[this.finallyProps.value], node);
              // 从剩余值中移除
              const index = remainingValues.indexOf(node[this.finallyProps.value]);
              if (index > -1) {
                remainingValues.splice(index, 1);
              }
            }

            // 递归查找子节点
            if (node[this.finallyProps.children] && Array.isArray(node[this.finallyProps.children])) {
              findInNodes(node[this.finallyProps.children]);
            }
          }
        };

        // 在当前options中查找
        findInNodes(this.currentTreeOptions);

        // 如果在当前数据中没找到，且有原始数据，也在原始数据中查找
        if (remainingValues.length > 0 && this.originalOptions.length > 0) {
          findInNodes(this.originalOptions);
        }
      }

      return labels;
    },

    /**
     * @description 从options中缓存选中节点信息
     * 用于解决懒加载+搜索场景下label无法回显的问题
     * @param {Array} options 树数据
     */
    cacheSelectedNodesFromOptions(options) {
      if (!this.inputValue || !Array.isArray(options)) {
        return;
      }

      let selectedValues;
      if (this.multiple) {
        selectedValues = Array.isArray(this.inputValue) ? this.inputValue : [];
      } else {
        selectedValues = [this.inputValue];
      }

      // 递归查找并缓存选中的节点
      const findAndCache = (nodes) => {
        if (!Array.isArray(nodes)) return;

        for (const node of nodes) {
          if (selectedValues.includes(node[this.finallyProps.value])) {
            this.selectedNodesCache.set(node[this.finallyProps.value], node);
          }

          // 递归查找子节点
          if (node[this.finallyProps.children] && Array.isArray(node[this.finallyProps.children])) {
            findAndCache(node[this.finallyProps.children]);
          }
        }
      };

      findAndCache(options);
    },

    /**
     * @description 触发change事件，传递最新的value、label数组和option集合
     */
    emitChangeEvent() {
      if (!this.inputValue) {
        this.$emit('change', {
          value: this.inputValue,
          label: [],
          option: [],
        });
        return;
      }

      // 获取当前选中的标签数组
      let labelArray = [];
      if (this.multiple) {
        // 多选模式：selectedLabel是逗号分隔的字符串，需要分割
        labelArray = this.selectedLabel ? this.selectedLabel.split(', ') : [];
      } else {
        // 单选模式：selectedLabel是字符串，转为数组
        labelArray = this.selectedLabel ? [this.selectedLabel] : [];
      }

      // 获取当前选中的节点集合
      let selectedValues;
      if (this.multiple) {
        selectedValues = Array.isArray(this.inputValue) ? this.inputValue : [];
      } else {
        selectedValues = [this.inputValue];
      }

      const optionArray = [];
      selectedValues.forEach((value) => {
        if (this.selectedNodesCache.has(value)) {
          optionArray.push(this.selectedNodesCache.get(value));
        }
      });

      this.$emit('change', {
        value: this.inputValue,
        label: labelArray,
        option: optionArray,
      });
    },

    /**
     * @description 判断节点是否为叶子节点
     * @param {Object} node 节点数据
     * @returns {Boolean} 是否为叶子节点
     */
    isLeafNode(node) {
      if (!node) return false;

      // 如果配置了isLeaf字段，优先使用该字段
      if (this.finallyProps.isLeaf && node[this.finallyProps.isLeaf] !== undefined) {
        return Boolean(node[this.finallyProps.isLeaf]);
      }

      // 否则通过children字段判断
      const children = node[this.finallyProps.children];
      return !children || !Array.isArray(children) || children.length === 0;
    },

    /**
     * @description 判断节点是否应该被禁用
     * @param {Object} node 节点数据
     * @returns {Boolean} 是否应该被禁用
     */
    isNodeDisabled(node) {
      if (!node) return false;

      // 如果节点本身设置了disabled，直接返回
      if (this.finallyProps.disabled && node[this.finallyProps.disabled]) {
        return true;
      }

      // 如果设置了只能选择叶子节点，非叶子节点应该被禁用
      if (this.onlyLeaf && !this.isLeafNode(node)) {
        return true;
      }

      // 如果设置了selectType，只有匹配的类型才能被选择
      if (this.selectType && node.type !== this.selectType) {
        return true;
      }

      return false;
    },

    // 处理节点选中状态变化
    // eslint-disable-next-line no-unused-vars
    handleNodeCheck(nodeOptions, { checkedNodes }) {
      // 过滤有效的节点
      let validNodes = checkedNodes;

      // 如果设置了只能选择叶子节点，过滤掉非叶子节点
      if (this.onlyLeaf) {
        validNodes = validNodes.filter((node) => this.isLeafNode(node));
      }

      // 如果设置了selectType，过滤掉不匹配类型的节点
      if (this.selectType) {
        validNodes = validNodes.filter((node) => node.type === this.selectType);
      }

      // 缓存选中的节点信息
      validNodes.forEach((node) => {
        this.selectedNodesCache.set(node[this.finallyProps.value], node);
      });

      // 更新树选中的值和标签
      this.treeSelectedValues = validNodes.map((node) => node[this.finallyProps.value]);
      this.treeSelectedLabels = validNodes.map((node) => node[this.finallyProps.label]);

      // 智能合并初始值和树选中值
      const mergedValues = this.mergeValues();

      // 标记为内部变化，避免重复触发change事件
      this.isInternalChange = true;
      this.inputValue = this.multiple ? mergedValues : mergedValues[0] || '';
      this.isInternalChange = false;

      // 同步两个树组件的选中状态
      this.$nextTick(() => {
        this.setTreeSelectionWithRetry();
        this.updateSelectedLabel();
        this.emitChangeEvent();
      });
    },

    // 处理节点点击
    handleNodeClick(data) {
      if (!this.multiple) {
        // 如果设置了只能选择叶子节点，检查当前节点是否为叶子节点
        if (this.onlyLeaf && !this.isLeafNode(data)) {
          return;
        }

        // 如果设置了selectType，检查当前节点类型是否匹配
        if (this.selectType && data.type !== this.selectType) {
          return;
        }

        // 缓存选中的节点信息
        this.selectedNodesCache.set(data[this.finallyProps.value], data);

        // 单选模式下，直接替换值（不需要合并）
        // 更新树选中的值和标签
        this.treeSelectedValues = [data[this.finallyProps.value]];
        this.treeSelectedLabels = [data[this.finallyProps.label]];

        // 标记为内部变化，避免重复触发change事件
        this.isInternalChange = true;
        this.inputValue = data[this.finallyProps.value];
        this.isInternalChange = false;

        this.$nextTick(() => {
          // 同步两个树组件的选中状态
          this.setTreeSelectionWithRetry();
          this.updateSelectedLabel();
          // 手动触发change事件
          this.emitChangeEvent();
          // 单选模式下选中节点后自动收起下拉框
          this.visible = false;
          if (this.$refs.select) {
            this.$refs.select.blur();
          }
        });
      }
    },

    // 清空选择
    handleClear() {
      // 标记为清空操作
      this.isClearOperation = true;

      // 先清除 tree 组件的选中状态，确保视觉效果立即更新
      this.clearTreeSelectionWithRetry();

      // 清空相关状态
      this.selectedLabel = '';
      this.filterText = '';
      this.isSearching = false;
      // 清空搜索结果数据，避免影响后续显示
      this.searchResultOptions = [];
      // 清空节点缓存
      this.selectedNodesCache.clear();
      // 清空初始值和树选中值
      this.initialValues = [];
      this.initialLabels = [];
      this.treeSelectedValues = [];
      this.treeSelectedLabels = [];

      // 直接通过$emit更新外部value，避免触发内部逻辑
      const newValue = this.multiple ? [] : '';
      this.$emit('update:value', newValue);
      this.$emit('clear');

      // 手动触发change事件（只触发一次）
      this.$nextTick(() => {
        this.emitChangeEvent();
        // 重置清空操作标志
        this.isClearOperation = false;
      });
    },

    // 处理下拉框显示状态变化
    handleVisibleChange(visible) {
      this.visible = visible;
      if (!visible) {
        // 关闭时清空搜索状态和结果
        this.filterText = '';
        this.isSearching = false;
        this.searchResultOptions = [];
        this.searchLoading = false;
        // 懒加载+远程搜索场景，关闭时恢复 internalOptions
        if (this.lazy && this.remoteSearch && typeof this.load === 'function') {
          this.internalOptions = [];
          this.hasLoadedRoot = false; // 关闭时重置
          this.$nextTick(() => {
            this.load(null, (data) => {
              this.internalOptions = data || [];
              this.hasLoadedRoot = true;
            });
          });
        }
      } else {
        // 打开时，重新设置选中状态，确保多选场景下的初始值能正确显示
        this.setTreeSelectionWithRetry();

        // 如果是懒加载+远程搜索模式，确保状态正确
        if (this.lazy && this.remoteSearch) {
          this.isSearching = false;
          this.searchResultOptions = [];
          this.searchLoading = false;
          if (this.$refs.select) {
            this.$refs.select.query = '';
            this.filterText = '';
          }
        }
      }
    },

    /**
     * @description 设置树组件选中状态
     * 同时设置隐藏的树组件和显示的树组件的选中状态
     */
    setTreeSelectionWithRetry() {
      // 设置隐藏的树组件（用于获取选中节点信息）
      if (this.$refs.tree) {
        if (this.multiple) {
          this.$refs.tree.setCheckedKeys(Array.isArray(this.inputValue) ? this.inputValue : []);
        } else {
          this.$refs.tree.setCurrentKey(this.inputValue);
        }
      }

      // 设置显示的树组件（用于视觉效果）
      if (this.$refs.visibleTree) {
        if (this.multiple) {
          this.$refs.visibleTree.setCheckedKeys(Array.isArray(this.inputValue) ? this.inputValue : []);
        } else {
          this.$refs.visibleTree.setCurrentKey(this.inputValue);
        }
      }
    },

    /**
     * @description 清除树组件选中状态
     * 同时清除隐藏的树组件和显示的树组件的选中状态
     */
    clearTreeSelectionWithRetry() {
      // 清除隐藏的树组件
      if (this.$refs.tree) {
        if (this.multiple) {
          this.$refs.tree.setCheckedKeys([]);
        } else {
          this.$refs.tree.setCurrentKey(null);
        }
      }

      // 清除显示的树组件
      if (this.$refs.visibleTree) {
        if (this.multiple) {
          this.$refs.visibleTree.setCheckedKeys([]);
        } else {
          this.$refs.visibleTree.setCurrentKey(null);
        }
      }
    },

    /**
     * @description 获取空数据时的提示信息
     * @returns {string} 提示信息
     */
    getEmptyMessage() {
      if (this.isSearching) {
        return '暂无搜索结果';
      }
      if (!this.lazy) {
        return '暂无数据';
      }
      return '';
    },

    /**
     * @description 高亮搜索关键字
     * @param {string} text 要高亮的文本
     * @param {string} keyword 搜索关键字
     * @returns {string} 高亮后的HTML字符串
     */
    highlightKeyword(text, keyword) {
      if (!this.highlightSearch || !keyword || !text) {
        return text || '';
      }
      const textStr = String(text);

      // 转义特殊字符，避免正则表达式错误
      const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const regex = new RegExp(`(${escapedKeyword})`, 'gi');

      return textStr.replace(regex, '<span style="color: var(--brand-6, #0f45ea); font-weight: bold;">$1</span>');
    },

    /**
     * @description 渲染树内容
     * 确保tree组件的ref始终可用，避免在懒加载+过滤场景下找不到tree组件
     */
    renderTreeContent(treeProps) {
      // 构建el-tree的属性对象
      const treeAttrs = {
        ...treeProps.props,
        style: {
          display: this.shouldShowTree ? 'block' : 'none',
        },
      };

      // 构建el-tree的事件对象
      const treeEvents = {
        ...treeProps.on,
      };

      // 构建scoped slots
      const treeScopedSlots = treeProps.scopedSlots || {
        default: ({ node, data }) => (
          <span class="custom-tree-node">
            {data.class && <i class={data.class} style="display:inline-block;margin-right:5px" />}
            <span>{node.label}</span>
          </span>
        ),
      };

      return (
        <div>
          {/* 始终渲染tree组件，确保ref可用 */}
          <el-tree {...{ props: treeAttrs, on: treeEvents, scopedSlots: treeScopedSlots }} ref="visibleTree" />
          {/* 加载状态 */}
          {this.searchLoading && (
            <div style="text-align: center; padding: 20px;">
              <i class="el-icon-loading" /> 搜索中...
            </div>
          )}
          {/* 统一的提示信息显示逻辑 - 只在远程搜索且没有结果时显示，避免与el-tree内部的空数据提示重复 */}
          {this.remoteSearch &&
            this.isSearching &&
            this.currentTreeOptions.length === 0 &&
            !this.searchLoading &&
            this.getEmptyMessage() && (
              <div style="text-align: center; padding: 20px; color: #999;">{this.getEmptyMessage()}</div>
            )}
        </div>
      );
    },
  },

  render() {
    // 创建自定义的props配置，包含disabled处理
    const customProps = {
      ...this.finallyProps,
      disabled: (data) => this.isNodeDisabled(data),
    };

    const treeProps = {
      props: {
        data: this.currentTreeOptions,
        props: customProps,
        nodeKey: this.finallyProps.value,
        checkStrictly: this.checkStrictly,
        showCheckbox: this.multiple,
        lazy: this.lazy,
        load: this.load,
        filterNodeMethod: this.filterNodeMethod || this.filterNode,
        defaultExpandAll: Boolean(this.filterText && !this.remoteSearch),
      },
      on: {
        check: this.handleNodeCheck,
        'node-click': this.handleNodeClick,
      },
    };

    // 自定义树节点内容（支持搜索高亮）
    if (this.highlightSearch && this.filterText) {
      treeProps.scopedSlots = {
        // eslint-disable-next-line no-unused-vars
        default: ({ node, data }) => <span domPropsInnerHTML={this.highlightKeyword(node.label, this.filterText)} />,
      };
    }

    return (
      <div class="x-select-tree">
        {this.mode === 'view' ? (
          <Tooltip content={this.displayValue} />
        ) : (
          <el-select
            ref="select"
            v-model={this.selectedLabel}
            size={this.size}
            clearable={this.clearable}
            placeholder={this.placeholder}
            filterable={this.isFilterable}
            remote={false}
            reserve-keyword={!this.lazy || !this.remoteSearch}
            loading={this.searchLoading}
            popper-class="x-select-tree__dropdown"
            value-key={this.finallyProps.value}
            filter-method={(val) => {
              // 使用filter-method来监听搜索输入变化
              this.filterText = val || '';
              this.handleSearch(this.filterText);
            }}
            on-clear={this.handleClear}
            on-visible-change={this.handleVisibleChange}
            disabled={this.disabled || this.mode === 'disabled'}
            {...{
              on: Object.entries(this.$listeners).reduce((acc, [event, handler]) => {
                acc[event] = (...args) => handler(...args, this.context);
                return acc;
              }, {}),
            }}
          >
            <el-option value={this.selectedLabel} style="height: auto;padding: 0;">
              {this.renderTreeContent(treeProps)}
            </el-option>
            {/* 初始化时渲染一个隐藏的树组件，确保ref可用 */}
            <el-option value="__hidden__" style="display: none;">
              <el-tree
                ref="tree"
                data={this.currentTreeOptions}
                props={customProps}
                node-key={this.finallyProps.value}
                check-strictly={this.checkStrictly}
                show-checkbox={this.multiple}
                lazy={this.lazy}
                load={this.load}
                filter-node-method={this.filterNodeMethod || this.filterNode}
                default-expand-all={Boolean(this.filterText && !this.remoteSearch)}
                on-check={this.handleNodeCheck}
                on-node-click={this.handleNodeClick}
                style="display: none;"
              />
            </el-option>
          </el-select>
        )}
      </div>
    );
  },
};
</script>

<style lang="scss">
@import '../styles/index.scss';

.x-select-tree {
  .el-select {
    width: 100%;
  }
  &__dropdown {
    .el-select-dropdown__item {
      padding: 0;
      &.selected {
        font-weight: normal;
      }
      &.hover {
        background-color: transparent;
      }
    }
  }

  .el-tree {
    padding: 5px;
    min-width: 200px;
    max-height: 300px;
    overflow-y: auto;

    .el-tree-node__content {
      height: 40px;
    }

    // 禁用状态的节点样式
    .el-tree-node.is-disabled {
      .el-tree-node__content {
        color: #c0c4cc;
        cursor: not-allowed;

        &:hover {
          background-color: transparent;
        }
      }

      .el-checkbox {
        cursor: not-allowed;

        .el-checkbox__input {
          cursor: not-allowed;
        }
      }
    }
  }
  .custom-tree-node {
    display: flex;
    align-items: center;
  }
}
</style>
