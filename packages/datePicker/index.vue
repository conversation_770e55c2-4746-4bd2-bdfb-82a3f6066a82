<!--
 * @Description:日期选择组件
 * @Version:
 * @Author: <PERSON><PERSON>
 * @Date: 2022-06-21 09:08:23
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-18 18:01:16
-->
<script lang="jsx">
import Tooltip from '../tooltip/index.vue';
import dayjs from 'dayjs';
import isObject from 'lodash/isObject';

/**
 * 日期选择器组件
 * @component XDatePicker
 * @description 基于 Element UI 的日期选择器组件封装，支持多种日期时间选择模式
 */
export default {
  name: 'XDatePicker',
  title: '日期选择组件',
  components: { Tooltip },
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    /**
     * 绑定值，支持多种类型输入
     * @type {number|string|date|array}
     */
    value: {
      type: [Number, String, Date, Array],
      default: '',
    },
    /**
     * 选择器类型
     * @type {string}
     * @values year/years/date/dates/daterange/month/months/monthrange/week/datetime/datetimerange/time/timerange
     */
    type: {
      type: String,
      default: 'date',
    },
    /**
     * 可选日期范围的开始时间
     * @type {string}
     */
    dateRangeStart: {
      type: [String, Number, Date, Function],
      default: '',
    },
    /**
     * 可选日期范围的结束时间
     * @type {string}
     */
    dateRangeEnd: {
      type: [String, Number, Date, Function],
      default: '',
    },
    /**
     * 输入框占位文本
     * @type {string}
     */
    placeholder: {
      type: String,
      default: '请选择日期',
    },
    /**
     * 编辑模式
     * @type {String}
     * @default 'edit'
     */
    mode: {
      type: String,
      default: 'edit',
    },
    /**
     * 是否禁用
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * 选择范围时的分隔符
     * @type {string}
     */
    rangeSeparator: {
      type: String,
      default: ' 至 ',
    },
    /**
     * 显示在输入框中的格式
     * @type {string}
     */
    format: {
      type: String,
      default: null,
    },
    /**
     * 输入框尺寸
     * @type {string}
     * @values medium/small/mini
     */
    size: {
      type: String,
      default: '',
    },
    /**
     * @description 可编辑表格使用，绑定值
     * @type {Object}
     * @default ''
     */
    context: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 是否展示自定义场景
     * @type {Boolean}
     * @default false
     */
    showCustom: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 自定义场景的labelName
     * @type {Boolean}
     * @default false
     */
    customLabel: {
      type: String,
      default: '至今',
    },
    /**
     * @description 日期区间选择时，是否包含起止时间的时分秒
     * @type {Boolean}
     * @default true
     */
    includingTime: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      /**
       * 日期选择器配置项
       * @type {Object}
       */
      pickerOptions: {
        disabledDate: (time) => {
          const dateRangeStart = isObject(this.dateRangeStart)
            ? this.dateRangeStart(this.context)
            : this.dateRangeStart;
          const dateRangeEnd = isObject(this.dateRangeEnd) ? this.dateRangeEnd(this.context) : this.dateRangeEnd;

          const now = dayjs(time).unix();
          const start = dateRangeStart ? dayjs(dateRangeStart).unix() : 0;
          const end = dateRangeEnd ? dayjs(dateRangeEnd).unix() : 0;
          return (start ? start > now : false) || (end ? end < now : false);
        },
      },
      /**
       * 不同类型日期的显示格式映射
       * @type {Object}
       */
      showDateFormat: {
        year: 'YYYY',
        years: 'YYYY',
        date: 'YYYY-MM-DD',
        dates: 'YYYY-MM-DD',
        daterange: 'YYYY-MM-DD',
        month: 'YYYY-MM',
        months: 'YYYY-MM',
        monthrange: 'YYYY-MM',
        week: 'YYYY-MM-DD',
        datetime: 'YYYY-MM-DD HH:mm:ss',
        datetimerange: 'YYYY-MM-DD HH:mm:ss',
        time: 'HH:mm:ss',
        timerange: 'HH:mm:ss',
      },
      /**
       * 不同类型日期的值格式映射
       * @type {Object}
       */
      valueDateFormat: {
        year: 'yyyy',
        years: 'yyyy',
        date: 'yyyy-MM-dd',
        dates: 'yyyy-MM-dd',
        daterange: 'yyyy-MM-dd',
        month: 'yyyy-MM',
        months: 'yyyy-MM',
        monthrange: 'yyyy-MM',
        week: 'yyyy-MM-dd',
        datetime: 'yyyy-MM-dd HH:mm:ss',
        datetimerange: 'yyyy-MM-dd HH:mm:ss',
        time: 'HH:mm:ss',
        timerange: 'HH:mm:ss',
      },
      /**
       * 范围选择类型列表
       * @type {Array}
       */
      rangeMap: ['daterange', 'monthrange', 'datetimerange', 'timerange'],
      /**
       * 范围选择类型对应的中文名称
       * @type {Object}
       */
      typeMap: {
        timerange: '时间',
        daterange: '日期',
        datetimerange: '时间',
        monthrange: '月份',
      },
      isCustom: false, // 选择了自定义
    };
  },
  computed: {
    /**
     * 输入值的计算属性
     * @returns {string|array} 处理后的输入值
     */
    inputValue: {
      get() {
        if (this.showCustom) {
          return this.value;
        }
        // eslint-disable-next-line no-nested-ternary
        return this.rangeMap.includes(this.type)
          ? Array.isArray(this.value) && this.value.filter(Boolean).length
            ? this.value
            : []
          : this.value;
      },
      set(value) {
        if (this.isCustom) {
          this.$emit('update:value', null);
          return;
        }

        if (this.type === 'daterange') {
          // 当是时间区间选择器并禁用时间选择时，默认加上起止时间的时分秒
          try {
            const [start, end] = value;
            // 这里dayjs的format时间格式和element的不一样
            if (this.includingTime) {
              this.$emit(
                'update:value',
                start
                  ? [`${dayjs(start).format('YYYY-MM-DD')} 00:00:00`, `${dayjs(end).format('YYYY-MM-DD')} 23:59:59`]
                  : [],
              );
            } else {
              this.$emit('update:value', value);
            }
          } catch (error) {
            this.$emit('update:value', []);
          }
        } else {
          this.$emit('update:value', value || (this.rangeMap.includes(this.type) ? [] : ''));
        }
      },
    },
    /**
     * 计算当前类型对应的值格式
     * @returns {string|null} 日期格式字符串
     */
    calcValueFormat() {
      return this.showDateFormat[this.type];
    },
    /**
     * 是否禁用日期选择器
     * @returns {boolean}
     */
    isDatePickerDisabled() {
      return this.mode === 'disabled' || this.disabled || this.isCustom;
    },
  },
  watch: {
    /**
     * 监听value变化，当value为null且showCustom为true时，设置自定义状态
     */
    value: {
      immediate: true,
      handler(newVal) {
        if (this.showCustom && newVal === null) {
          this.isCustom = true;
        } else if (newVal !== null) {
          this.isCustom = false;
        }
      },
    },
  },
  methods: {
    /**
     * 获取格式化后的值
     * @returns {string} 格式化后的日期字符串
     */
    getValue() {
      // 如果选择了自定义，显示"自定义"
      if (this.isCustom) {
        return this.customLabel;
      }

      let formattedTimeArray;
      const inputValue = Array.isArray(this.inputValue) ? this.inputValue : [];
      const hasInputValue = inputValue.length > 0;

      // 处理时间选择
      if (['time', 'timerange'].includes(this.type)) {
        switch (this.type) {
          case 'time':
            return this.inputValue || '-';
          case 'timerange':
            return hasInputValue ? inputValue.join(this.rangeSeparator || ' - ') : '-';
          default:
            return '-';
        }
      } else {
        const format = this.format || this.calcValueFormat || 'YYYY-MM-DD'; // 确保有默认格式
        switch (this.type) {
          case 'daterange':
          case 'monthrange':
          case 'datetimerange':
            if (hasInputValue) {
              formattedTimeArray = inputValue.map((i) => dayjs(i).format(format));
              return formattedTimeArray.join(this.rangeSeparator || ' - ');
            }
            return '-';
          default:
            try {
              return this.inputValue ? dayjs(this.inputValue).format(format) : '-';
            } catch (error) {
              console.error('Invalid input value:', error);
              return '-';
            }
        }
      }
    },
    /**
     * 处理自定义选择变化
     * @param {boolean} checked 是否选中自定义
     */
    handleCustomChange(checked) {
      this.isCustom = checked;
      let newValue;
      if (checked) {
        newValue = null;
      } else {
        newValue = this.rangeMap.includes(this.type) ? [] : '';
      }

      // 发送update:value事件
      this.$emit('update:value', newValue);

      // 如果有change监听器，也要传递context
      if (this.$listeners.change) {
        this.$listeners.change(newValue, this.context);
      }
    },
    /**
     * 渲染日期时间选择器
     * @returns {VNode} 渲染的选择器节点
     */
    renderDatePicker() {
      return this.rangeMap.includes(this.type) ? (
        <el-date-picker
          vModel={this.inputValue}
          type={this.type}
          readonly={this.isDatePickerDisabled}
          picker-options={this.pickerOptions}
          format={this.format}
          value-format={this.valueDateFormat[this.type]}
          style="width: 100%"
          startPlaceholder={this.mode === 'edit' ? `开始${this.typeMap[this.type]}` : ''}
          endPlaceholder={this.mode === 'edit' ? `结束${this.typeMap[this.type]}` : ''}
          default-time={['00:00:00', '23:59:59']}
          size={this.size}
          {...{
            on: {
              ...Object.entries(this.$listeners).reduce((acc, [event, handler]) => {
                acc[event] = (...args) => handler(...args, this.context);
                return acc;
              }, {}),
            },
            props: this.$attrs,
          }}
        />
      ) : (
        <div class="x-date-picker-container">
          <el-date-picker
            vModel={this.inputValue}
            type={this.type}
            readonly={this.isDatePickerDisabled}
            picker-options={this.pickerOptions}
            format={this.format}
            value-format={this.valueDateFormat[this.type]}
            style="width: 100%"
            placeholder={this.mode === 'edit' ? this.placeholder : ''}
            size={this.size}
            {...{
              on: {
                ...Object.entries(this.$listeners).reduce((acc, [event, handler]) => {
                  acc[event] = (...args) => handler(...args, this.context);
                  return acc;
                }, {}),
              },
              props: this.$attrs,
            }}
          />
          {this.showCustom && (
            <div class="x-date-custom">
              <el-checkbox
                value={this.isCustom}
                onChange={this.handleCustomChange}
                disabled={this.mode === 'disabled' || this.disabled}
              >
                {this.customLabel}
              </el-checkbox>
            </div>
          )}
        </div>
      );
    },
    /**
     * 渲染时间选择器
     * @returns {VNode} 渲染的选择器节点
     */
    renderTimePicker() {
      return (
        <div class="x-date-picker-container">
          <el-time-picker
            vModel={this.inputValue}
            placeholder={this.placeholder}
            readonly={this.isDatePickerDisabled}
            rangeSeparator={this.rangeSeparator}
            value-format={this.valueDateFormat[this.type]}
            is-range={this.type === 'timerange'}
            startPlaceholder={this.mode === 'edit' ? `开始${this.typeMap[this.type]}` : ''}
            endPlaceholder={this.mode === 'edit' ? `结束${this.typeMap[this.type]}` : ''}
            {...{
              on: {
                ...Object.entries(this.$listeners).reduce((acc, [event, handler]) => {
                  acc[event] = (...args) => handler(...args, this.context);
                  return acc;
                }, {}),
              },
              props: this.$attrs,
            }}
          />
          {this.showCustom && (
            <div class="x-date-custom">
              <el-checkbox
                value={this.isCustom}
                onChange={this.handleCustomChange}
                disabled={this.mode === 'disabled' || this.disabled}
              >
                {this.customLabel}
              </el-checkbox>
            </div>
          )}
        </div>
      );
    },
    /**
     * 根据类型渲染对应的选择器
     * @returns {VNode} 渲染的选择器节点
     */
    renderPicker() {
      return ['time', 'timerange'].includes(this.type) ? this.renderTimePicker() : this.renderDatePicker();
    },
  },
  render() {
    return (
      <div class="x-date-picker">
        {this.mode === 'view' ? <Tooltip content={this.getValue()} /> : this.renderPicker()}
      </div>
    );
  },
};
</script>

<style lang="scss" scoped>
@import '../styles/index.scss';
.el-date-editor.el-input {
  vertical-align: middle;
  width: 100%;
}
.x-date-picker {
  display: flex;
  align-items: center;
}
.x-date-picker-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}
.x-date-custom {
  flex-shrink: 0;
  white-space: nowrap;
}
</style>
