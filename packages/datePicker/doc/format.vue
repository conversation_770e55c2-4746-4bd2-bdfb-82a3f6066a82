<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 16:08:39
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-10 16:08:51
 * @FilePath: /vite-element-components/packages/datePicker/doc/format.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <p>
      自定义日期格式：
      <x-datePicker v-model="inputValue1" type="date" format="YYYY年MM月DD日" placeholder="选择日期" />
    </p>
    <p>
      自定义日期时间格式：
      <x-datePicker
        v-model="inputValue2"
        type="datetime"
        format="YYYY年MM月DD日 HH时mm分ss秒"
        placeholder="选择日期时间"
      />
    </p>
    <p>
      自定义周格式：
      <x-datePicker v-model="inputValue3" type="week" format="YYYY 第 ww 周" placeholder="选择周" />
    </p>
    <p>
      自定义月份格式：
      <x-datePicker v-model="inputValue4" type="month" format="YYYY年MM月" placeholder="选择月份" />
    </p>
    <p>
      自定义日期范围格式：
      <x-datePicker
        v-model="inputValue5"
        type="daterange"
        format="YYYY/MM/DD"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputValue1: '',
      inputValue2: '',
      inputValue3: '',
      inputValue4: '',
      inputValue5: [],
    };
  },
};
</script>
