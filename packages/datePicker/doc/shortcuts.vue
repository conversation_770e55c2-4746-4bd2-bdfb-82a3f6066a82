<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 16:08:59
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-09 14:46:56
 * @FilePath: /vite-element-components/packages/datePicker/doc/shortcuts.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <p>
      日期快捷选项带日期限制：
      <x-datePicker v-model="inputValue1" type="date" :picker-options="pickerOptions" placeholder="选择日期" />
    </p>
    <p>
      日期范围快捷选项：
      <x-datePicker
        v-model="inputValue2"
        type="daterange"
        :picker-options="{
          shortcuts: [
            {
              text: '最近一周',
              onClick: (picker) => {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                picker.$emit('pick', [start, end]);
              },
            },
            {
              text: '最近一个月',
              onClick: (picker) => {
                const end = new Date();
                const start = new Date();
                start.setMonth(start.getMonth() - 1);
                picker.$emit('pick', [start, end]);
              },
            },
            {
              text: '最近三个月',
              onClick: (picker) => {
                const end = new Date();
                const start = new Date();
                start.setMonth(start.getMonth() - 3);
                picker.$emit('pick', [start, end]);
              },
            },
          ],
        }"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputValue1: '',
      inputValue2: [],
      pickerOptions: {
        disabledDate(time) {
          console.log(
            '%c [  ]-68-「shortcuts」',
            'font-size:13px; background:pink; color:#bf2c9f;',
            time,
            time.getTime() > Date.now(),
          );
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: '今天',
            onClick: (picker) => {
              picker.$emit('pick', new Date());
            },
          },
          {
            text: '昨天',
            onClick: (picker) => {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit('pick', date);
            },
          },
          {
            text: '一周前',
            onClick: (picker) => {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', date);
            },
          },
        ],
      },
    };
  },
};
</script>
