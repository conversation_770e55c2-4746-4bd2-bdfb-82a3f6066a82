<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:43:59
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-13 16:28:30
 * @FilePath: /vite-element-components/packages/datePicker/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import FormatVue from './format.vue';
  import RangeLimitVue from './range-limit.vue';
  import ShortcutsVue from './shortcuts.vue';
  import CustomVue from './custom.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      FormatVue,
      RangeLimitVue,
      ShortcutsVue,
      CustomVue,
      Preview
    }
  };
</script>

## DatePicker 日期选择器

用于选择或输入日期的组件，支持多种日期时间选择模式，包括年、月、日、周、时间等，以及它们的范围选择。

### 基础用法

基础的日期选择器用法，支持多种选择类型：

- 年份选择：选择具体的年份
- 月份选择：选择具体的月份
- 日期选择：选择具体的某一天
- 日期范围：选择一个时间区间
- 日期时间：选择具体的日期和时间
- 时间选择：仅选择时间

<basic-vue/>
<preview  comp-name='datePicker' demo-name='basic'/>

### 自定义label选择功能

当设置 `showCustom` 属性为 `true` 时，组件会显示一个"自定义label"复选框。用户可以选择"自定义label"来表示一个持续到当前时间的状态：

- 选择"自定义label"时，日期选择器会被禁用，组件值变为 `null`
- 取消"自定义label"时，日期选择器重新启用，可以正常选择日期
- 当外部传入的 `value` 为 `null` 且 `showCustom` 为 `true` 时，组件会自动勾选"自定义label"选项
- 在查看模式下，如果值为 `null`，会显示"自定义label"文本

<custom-vue/>
<preview  comp-name='datePicker' demo-name='custom'/>

### 自定义格式

通过设置 format 属性可以自定义日期的显示格式，支持多种格式化选项：

- 日期格式：YYYY年MM月DD日
- 日期时间格式：YYYY年MM月DD日 HH时mm分ss秒
- 周格式：YYYY 第 ww 周
- 月份格式：YYYY年MM月
- 日期范围格式：YYYY/MM/DD

<format-vue/>
<preview  comp-name='datePicker' demo-name='format'/>

### 日期范围限制

通过设置 date-range-start 和 date-range-end 属性可以限制可选择的日期范围：

- 限制可选日期范围
- 限制日期时间范围
- 限制日期范围选择

<range-limit-vue/>
<preview  comp-name='datePicker' demo-name='range-limit'/>

### 快捷选项

通过设置 picker-options 的 shortcuts 属性可以设置快捷选项，方便用户快速选择常用的日期：

- 日期快捷选项：今天、昨天、一周前
- 日期范围快捷选项：最近一周、最近一个月、最近三个月

<shortcuts-vue/>
<preview  comp-name='datePicker' demo-name='shortcuts'/>

### Attributes

| 参数             | 说明                                     | 类型                     | 可选值                                                                                             | 默认值                   |
| ---------------- | ---------------------------------------- | ------------------------ | -------------------------------------------------------------------------------------------------- | ------------------------ |
| value            | 绑定值，支持 v-model 双向绑定            | number/string/date/array | —                                                                                                  | —                        |
| type             | 日期选择器类型                           | string                   | year/years/date/dates/daterange/month/months/monthrange/week/datetime/datetimerange/time/timerange | date                     |
| dateRangeStart   | 可选日期范围的开始时间                   | string                   | —                                                                                                  | —                        |
| dateRangeEnd     | 可选日期范围的结束时间                   | string                   | —                                                                                                  | —                        |
| placeholder      | 输入框占位文本                           | string                   | —                                                                                                  | 请选择日期               |
| readonly         | 是否只读                                 | boolean                  | —                                                                                                  | false                    |
| rangeSeparator   | 选择范围时的分隔符                       | string                   | —                                                                                                  | 至                       |
| format           | 显示在输入框中的格式                     | string                   | 见日期格式                                                                                         | —                        |
| size             | 输入框尺寸                               | string                   | medium/small/mini                                                                                  | small                    |
| clearable        | 是否显示清除按钮                         | boolean                  | —                                                                                                  | true                     |
| disabled         | 是否禁用                                 | boolean                  | —                                                                                                  | false                    |
| editable         | 文本框是否可输入                         | boolean                  | —                                                                                                  | true                     |
| startPlaceholder | 范围选择时开始日期的占位内容             | string                   | —                                                                                                  | —                        |
| endPlaceholder   | 范围选择时结束日期的占位内容             | string                   | —                                                                                                  | —                        |
| defaultTime      | 范围选择时选中日期所使用的当日内具体时刻 | string[]                 | —                                                                                                  | ['00:00:00', '23:59:59'] |
| includingTime    | 日期区间选择时，是否包含起止时间的时分秒 | boolean                  | true/false                                                                                         | true                     |
| pickerOptions    | 当前时间日期选择器特有的选项             | object                   | shortcuts: { text: string, onClick: function }, disabledDate: function                             | —                        |
| showCustom       | 是否显示自定义label选择框                | boolean                  | —                                                                                                  | false                    |
| customLabel      | 自定义label内容                          | string                   | —                                                                                                  | '至今'                   |

### Events

| 事件名称       | 说明                         | 回调参数                      |
| -------------- | ---------------------------- | ----------------------------- |
| change         | 用户确认选定的值时触发       | 组件绑定值                    |
| blur           | 当 input 失去焦点时触发      | 组件实例                      |
| focus          | 当 input 获得焦点时触发      | 组件实例                      |
| visible-change | 当日期选择器显示或隐藏时触发 | true 表示显示，false 表示隐藏 |

### Methods

| 方法名 | 说明              | 参数 |
| ------ | ----------------- | ---- |
| focus  | 使 input 获取焦点 | —    |
| blur   | 使 input 失去焦点 | —    |

### Slots

| 插槽名  | 说明           |
| ------- | -------------- |
| default | 自定义内容插槽 |

### 日期格式

| 格式 | 含义 | 示例  |
| ---- | ---- | ----- |
| yyyy | 年   | 2025  |
| MM   | 月   | 01-12 |
| dd   | 日   | 01-31 |
| HH   | 小时 | 00-23 |
| mm   | 分钟 | 00-59 |
| ss   | 秒   | 00-59 |
| W    | 周   | 1-7   |
