<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-29 09:30:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-29 09:30:00
 * @FilePath: /vite-element-components/packages/datePicker/doc/custom.vue
 * @Description: 至今选择功能示例
-->
<template>
  <div class="component-view">
    <h3>至今选择功能</h3>

    <p>
      日期选择（支持至今）：
      <x-datePicker v-model="customValue1" type="date" clearable showcustom />
      当前值: {{ customValue1 === null ? '至今' : customValue1 }}
    </p>

    <p>
      日期时间选择（支持至今）：
      <x-datePicker v-model="customValue2" type="datetime" clearable showcustom />
      当前值: {{ customValue2 === null ? '至今' : customValue2 }}
    </p>

    <p>
      月份选择（支持至今）：
      <x-datePicker v-model="customValue3" type="month" clearable showcustom />
      当前值: {{ customValue3 === null ? '至今' : customValue3 }}
    </p>

    <p>
      时间选择（支持至今）：
      <x-datePicker v-model="customValue4" type="time" clearable showcustom />
      当前值: {{ customValue4 === null ? '至今' : customValue4 }}
    </p>

    <p>
      年份选择（支持至今）：
      <x-datePicker v-model="customValue5" type="year" clearable showcustom />
      当前值: {{ customValue5 === null ? '至今' : customValue5 }}
    </p>

    <h4>不同模式下的显示</h4>

    <p>
      查看模式（至今值）：
      <x-datePicker mode="view" v-model="customValue1" type="date" showcustom />
    </p>

    <p>
      查看模式（普通值）：
      <x-datePicker mode="view" v-model="normalValue" type="date" showcustom />
    </p>

    <p>
      禁用模式（至今值）：
      <x-datePicker disabled v-model="customValue1" type="date" showcustom />
    </p>

    <p>
      禁用模式（普通值）：
      <x-datePicker disabled v-model="normalValue" type="date" showcustom />
    </p>

    <h4>功能测试</h4>

    <p>
      <el-button @click="setcustom">设置为至今</el-button>
      <el-button @click="setNormalValue">设置为普通值</el-button>
      <el-button @click="clearValue">清空值</el-button>
    </p>

    <p>
      测试值：
      <x-datePicker v-model="testValue" type="date" clearable showcustom />
      当前值: {{ testValue === null ? '至今' : testValue }}
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 至今选择功能测试数据
      customValue1: null, // 设置为null测试至今状态
      customValue2: '',
      customValue3: '',
      customValue4: '',
      customValue5: '',
      normalValue: '2025-07-29',
      testValue: '',
    };
  },
  methods: {
    setcustom() {
      this.testValue = null;
    },
    setNormalValue() {
      this.testValue = '2025-07-29';
    },
    clearValue() {
      this.testValue = '';
    },
  },
};
</script>
