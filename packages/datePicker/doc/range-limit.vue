<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 16:08:20
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-10 16:14:56
 * @FilePath: /vite-element-components/packages/datePicker/doc/range-limit.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <p>
      限制可选日期范围：
      <x-datePicker
        v-model="inputValue1"
        type="date"
        date-range-start="2025-03-01"
        date-range-end="2025-4-31"
        placeholder="选择2025年内的日期"
      />
    </p>
    <p>
      限制日期时间范围：
      <x-datePicker
        v-model="inputValue2"
        type="datetime"
        date-range-start="2025-03-01 00:00:00"
        date-range-end="2025-03-31 23:59:59"
        placeholder="选择2025年3月内的日期时间"
      />
    </p>
    <p>
      限制日期范围：
      <x-datePicker
        v-model="inputValue2"
        type="date"
        date-range-start="2025-03-01"
        date-range-end="2025-03-31"
        placeholder="选择2025年3月内的日期"
      />
    </p>
    <p>
      限制日期范围选择：
      <x-datePicker
        v-model="inputValue3"
        type="daterange"
        date-range-start="2025-01-01"
        date-range-end="2025-12-31"
        placeholder="选择2025年内的日期范围"
      />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputValue1: '',
      inputValue2: '',
      inputValue3: [],
    };
  },
};
</script>
