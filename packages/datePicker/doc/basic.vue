<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:43:59
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 13:49:03
 * @FilePath: /vite-element-components/packages/datePicker/doc/basic.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <p>
      年份选择：
      <x-datePicker v-model="inputValue0" clearable />
      开始时间：
      <x-datePicker v-model="startDate" :date-range-start="startDate" :date-range-end="endDate" clearable />
      结束时间：
      <x-datePicker v-model="endDate" :date-range-start="startDate" :date-range-end="endDate" clearable />
    </p>
    <p>
      多年份选择：
      <x-datePicker v-model="inputValue1" type="years" clearable />
    </p>
    <p>
      日期选择：{{ inputValue2 }}
      <x-datePicker v-model="inputValue2" type="date" clearable />
    </p>
    <p>
      多日期选择：
      <x-datePicker v-model="inputValue3" type="dates" clearable />
    </p>
    <p>
      日期区间选择：{{ inputValue4 }}
      <x-datePicker v-model="inputValue4" type="daterange" clearable />
    </p>
    <p>
      月份选择：
      <x-datePicker v-model="inputValue5" type="month" clearable />
    </p>
    <p>
      多月份选择：
      <x-datePicker v-model="inputValue6" type="months" clearable />
    </p>
    <p>
      月度区间选择：
      <x-datePicker v-model="inputValue7" type="monthrange" clearable />
    </p>
    <p>
      周选择：
      <x-datePicker v-model="inputValue8" type="week" format="yyyy 第 WW 周" clearable />
    </p>
    <p>
      日期时间选择：
      <x-datePicker v-model="inputValue9" type="datetime" clearable />
    </p>
    <p>
      日期时间区间选择：
      <x-datePicker v-model="inputValue10" type="datetimerange" clearable :default-time="['00:00:00', '23:59:59']" />
    </p>
    <p>
      时间选择：
      <x-datePicker v-model="inputValue11" type="time" clearable editable />
    </p>
    <p>
      详情日期区间选择：
      <x-datePicker mode="view" v-model="inputValue4" type="daterange" />
    </p>
    <p>
      只读日期区间选择：
      <x-datePicker readonly v-model="inputValue4" type="daterange" />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      startDate: '',
      endDate: '',
      inputValue0: '',
      inputValue1: [],
      inputValue2: '',
      inputValue3: [],
      inputValue4: [],
      inputValue5: '',
      inputValue6: [],
      inputValue7: [],
      inputValue8: '',
      inputValue9: '',
      inputValue10: [],
      inputValue11: '',
      inputValue12: [],
    };
  },
};
</script>
