<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-27 15:17:52
 * @FilePath: /vite-element-components/packages/text/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      Preview
    }
  };
</script>

# Text 文本组件

用于展示文本内容的基础组件，支持文本溢出时显示tooltip提示。

## 基础用法

基础的文本展示用法。

<basic-vue/>
<preview comp-name='text' demo-name='basic'/>

## 组件属性

| 参数     | 说明                     | 类型          | 可选值     | 默认值 |
| -------- | ------------------------ | ------------- | ---------- | ------ |
| value    | 显示的文本内容           | string/number | —          | ''     |
| ellipsis | 是否启用文本溢出隐藏功能 | boolean       | true/false | true   |

## 注意事项

1. 当启用溢出隐藏功能时（ellipsis=true），文本超出容器宽度会显示省略号，鼠标悬停时会显示完整内容的tooltip提示。
2. 当禁用溢出隐藏功能时（ellipsis=false），文本会按照自然换行方式显示。
