<template>
  <div class="component-view">
    <div style="width: 200px; margin-bottom: 20px">
      <!-- 基础用法 -->
      <x-text :value="text" />
    </div>

    <div style="width: 200px; margin-bottom: 20px">
      <!-- 长文本自动省略 -->
      <x-text :value="longText" />
    </div>

    <div style="width: 200px">
      <!-- 禁用文本溢出隐藏 -->
      <x-text :value="longText" :ellipsis="false" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      text: '基础文本',
      longText: '这是一段很长的文本内容，当文本内容超出容器宽度时会自动显示省略号，并在鼠标悬停时显示完整内容',
    };
  },
};
</script>
