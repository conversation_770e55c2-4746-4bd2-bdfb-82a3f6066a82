<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-27 15:18:07
 * @FilePath: /vite-element-components/packages/text/index.vue
 * @Description:
-->
<script lang="jsx">
import Tooltip from '../tooltip/index.vue';

/**
 * 文本组件
 * @component XText
 * @description 基于 Element UI 的文本显示组件，支持文本溢出时显示tooltip提示，可配置是否启用溢出隐藏功能
 */
export default {
  name: 'XText',
  title: '普通文本组件',
  components: { Tooltip },
  props: {
    /**
     * 显示的文本内容
     * @type {string|number}
     * @default ''
     */
    value: {
      type: [String, Number],
      default: '',
    },
    /**
     * 是否启用文本溢出隐藏功能
     * @type {boolean}
     * @default true
     */
    ellipsis: {
      type: Boolean,
      default: true,
    },
  },
  render() {
    return <Tooltip content={this.value} ellipsis={this.ellipsis} />;
  },
};
</script>
<style lang="scss" scoped>
@import '../styles/index.scss';
</style>
