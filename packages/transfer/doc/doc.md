<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-26 17:38:13
 * @FilePath: /vite-element-components/packages/transfer/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,Preview
    }
  };
</script>

## Transfer 组件

基于 Element UI 的穿梭框组件，支持数据双向绑定、只读模式和禁用状态。用于在两个数据源之间进行数据项的移动。

### 基础用法

<basic-vue/>
<preview  comp-name='transfer' demo-name='basic'/>

### 属性配置

| 属性名                | 说明                                    | 类型                            | 可选值                | 默认值             |
| --------------------- | --------------------------------------- | ------------------------------- | --------------------- | ------------------ |
| v-model/value         | 绑定值，选中项的 key 数组               | array                           | —                     | —                  |
| data                  | Transfer 的数据源                       | array[{ key, label, disabled }] | —                     | []                 |
| filterable            | 是否可搜索                              | boolean                         | —                     | false              |
| filter-placeholder    | 搜索框占位符                            | string                          | —                     | 请输入搜索内容     |
| filter-method         | 自定义搜索方法                          | function                        | —                     | —                  |
| target-order          | 右侧列表元素的排序策略                  | string                          | original/push/unshift | original           |
| titles                | 自定义列表标题                          | array                           | —                     | ['列表1', '列表2'] |
| button-texts          | 自定义按钮文案                          | array                           | —                     | []                 |
| render-content        | 自定义数据项渲染函数                    | function(h, option)             | —                     | —                  |
| format                | 列表顶部勾选状态文案                    | object{noChecked, hasChecked}   | —                     | —                  |
| mode                  | 组件显示模式（编辑/只读）               | string                          | edit/view             | edit               |
| props                 | 数据源的字段别名                        | object{key, label, disabled}    | —                     | —                  |
| left-default-checked  | 初始状态下左侧列表的已勾选项的 key 数组 | array                           | —                     | []                 |
| right-default-checked | 初始状态下右侧列表的已勾选项的 key 数组 | array                           | —                     | []                 |

### 事件

| 事件名             | 说明                                  | 参数                                                                |
| ------------------ | ------------------------------------- | ------------------------------------------------------------------- |
| change             | 右侧列表元素变化时触发                | 当前值、数据移动的方向（'left' / 'right'）、发生移动的数据 key 数组 |
| left-check-change  | 左侧列表元素被用户选中/取消选中时触发 | 当前被选中的元素的 key 数组、选中状态发生变化的元素的 key 数组      |
| right-check-change | 右侧列表元素被用户选中/取消选中时触发 | 当前被选中的元素的 key 数组、选中状态发生变化的元素的 key 数组      |

### 插槽

| 插槽名       | 说明               |
| ------------ | ------------------ |
| left-footer  | 左侧列表底部的内容 |
| right-footer | 右侧列表底部的内容 |

### 方法

| 方法名     | 说明                     | 参数             |
| ---------- | ------------------------ | ---------------- |
| clearQuery | 清空某个面板的搜索关键词 | 'left' / 'right' |
