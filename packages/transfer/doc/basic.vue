<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-25 21:08:18
 * @FilePath: /vite-element-components/packages/transfer/doc/basic.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <p>
      普通穿梭框：
      <x-transfer v-model="inputValue" :options="options" filterable />
    </p>
    <p>
      详情穿梭框：
      <x-transfer mode="view" v-model="inputValue" :options="options" />
    </p>
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      inputValue: [],
      options: [
        { label: '张三', key: 1, a: 1, b: 2, c: 3, d: 4 },
        { label: '李四', key: 2, a: 1, b: 2, c: 3, d: 4, disabled: true },
        { label: '王五', key: 3, a: 1, b: 2, c: 3, d: 4 },
      ],
    };
  },
};
</script>
