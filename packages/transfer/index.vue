<script lang="jsx">
/**
 * @component XTransfer
 * @description 穿梭框组件，基于Element UI的el-transfer组件封装，支持数据双向绑定、只读模式和禁用状态
 */
export default {
  name: 'XTransfer',
  title: '穿梭框组件',
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    /**
     * @description 选中项的值数组
     * @default []
     */
    value: {
      type: [Array],
      default: () => [],
    },
    /**
     * 编辑模式
     * @type {String}
     * @default 'edit'
     */
    mode: {
      type: String,
      default: 'edit',
    },
    /**
     * @description 数据源选项数组，每项需包含 key 和 label 属性
     * @default []
     */
    options: {
      type: [Array],
      default: () => [],
    },
    /**
     * @description 穿梭框的高度
     * @default '400px'
     */
    height: {
      type: [String, Number],
      default: '400px',
    },
    /**
     * @description 是否显示加载状态
     * @default false
     */
    loading: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 是否禁用
     * @default false
     */
    disabled: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
  },
  computed: {
    inputValue: {
      get() {
        return this.value || [];
      },
      set(value) {
        this.$emit('update:value', value);
      },
    },
  },
  methods: {
    renderSelected() {
      if (this.inputValue.length > 0) {
        const selected = this.inputValue.map((item) => {
          const model = this.options.find((model) => model.key === item);
          return model ? model.label : '';
        });

        return selected.join('，');
      }
      return '-';
    },
  },
  render() {
    return (
      <div class="x-transfer" vLoading={this.loading}>
        {this.mode === 'view' || this.disabled ? (
          this.renderSelected()
        ) : (
          <el-transfer
            vModel={this.inputValue}
            data={this.options}
            style={{ height: this.height }}
            {...{
              props: this.$attrs,
              on: {
                ...this.$listeners,
              },
            }}
          />
        )}
      </div>
    );
  },
};
</script>
<style lang="scss" scoped>
@import '../styles/index.scss';
.x-transfer {
  width: 100%;
  .el-transfer {
    width: 100%;
    display: flex;

    ::v-deep {
      .el-transfer-panel {
        flex: 1 1 0;
        height: inherit;
      }
      .el-transfer__search .el-transfer-panel {
        height: inherit;
      }
      .el-pagination {
        justify-content: center;
      }
    }
  }
}
</style>
