<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:44:18
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-22 10:23:41
 * @FilePath: /vite-element-components/packages/empty/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      Preview
    }
  };
</script>

## Empty 组件

空状态组件用于在页面中展示空状态时的占位提示。当页面或容器中没有数据时，可以展示的用户提示。

### 基础用法

基础的空状态占位组件，可以通过 `size` 属性设置组件的大小，通过 `description` 属性设置描述文案。

<basic-vue/>
<preview  comp-name='empty' demo-name='basic'/>

### Attributes

| 参数        | 说明               | 类型   | 可选值 | 默认值   | 必填 |
| ----------- | ------------------ | ------ | ------ | -------- | ---- |
| size        | 空状态的尺寸大小   | number | 150    | 120      | N    |
| description | 空状态下的文字内容 | string | —      | 暂无内容 | N    |

### Slots

| 插槽名  | 说明                   |
| ------- | ---------------------- |
| default | 自定义空状态展示的内容 |
