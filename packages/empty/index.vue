<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-17 14:19:11
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-22 10:20:10
 * @FilePath: /vite-element-components/packages/empty/index.vue
 * @Description: 空状态组件，用于展示暂无数据、搜索无结果等场景
-->
<script lang="jsx">
/**
 * @description 空状态组件
 * @module XEmpty
 */
import result404Icon from './assets/empty.png';

export default {
  name: 'XEmpty',
  components: {
    result404Icon,
  },
  props: {
    /**
     * @description 空状态图标的大小，支持数字或带单位的字符串
     * @type {number|string}
     * @default 120
     * @example size="80" / size={80}
     */
    size: {
      type: [Number],
      default: 120,
      validator: (value) => {
        if (typeof value === 'number') return value >= 0;
        return false;
      },
    },
    /**
     * @description 空状态的描述文本
     * @type {number|string}
     * @default '暂无数据'
     */
    description: {
      type: [Number, String],
      default: '暂无数据',
    },
  },
  render() {
    return <el-empty image-size={this.size} image={result404Icon} description={this.description} />;
  },
};
</script>
