<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-26 09:03:32
 * @FilePath: /vite-element-components/packages/switch/index.vue
 * @Description: 基于Element UI的开关组件封装，提供开关状态切换功能
-->
<script lang="jsx">
import Tooltip from '../tooltip/index.vue';
/**
 * @component XSwitch
 * @description 开关组件，基于Element UI的el-switch组件封装，支持v-model双向绑定
 */
export default {
  name: 'XSwitch',
  title: '开关组件',
  components: { Tooltip },
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    /**
     * @description 开关的值，支持多种类型
     * @default null
     */
    value: {
      type: [String, Number, Boolean],
      default: () => null,
    },
    /**
     * 编辑模式
     * @type {String}
     * @default 'edit'
     */
    mode: {
      type: String,
      default: 'edit',
    },
    /**
     * @description 是否禁用
     * @default false
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 开关的尺寸，可选值：large/default/small
     * @default small
     */
    size: {
      type: String,
      default: '',
    },
  },
  computed: {
    /**
     * @description 用于v-model双向绑定的计算属性
     */
    inputValue: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('update:value', value);
      },
    },
  },
  render() {
    return (
      <div class="x-switch">
        <el-switch
          vModel={this.inputValue}
          disabled={['view', 'disabled'].includes(this.mode) || this.disabled}
          size={this.size}
          {...{
            on: this.$listeners,
            props: this.$attrs,
          }}
        >
          {this.$slots.default}
        </el-switch>
      </div>
    );
  },
};
</script>
<style lang="scss" scoped>
@import '../styles/index.scss';
</style>
