<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-19 09:17:46
 * @FilePath: /vite-element-components/packages/switch/doc/doc.md
 * @Description: Switch组件文档
-->
<script>
  import BasicVue from './basic.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,Preview
    }
  };
</script>

## Switch 组件

用于在两种状态间切换的开关组件。

### 基础用法

基础的开关组件用法，支持v-model进行双向数据绑定。

<basic-vue/>
<preview  comp-name='switch' demo-name='basic'/>

### 属性

| 参数           | 说明                               | 类型                      | 可选值                  | 默认值  |
| -------------- | ---------------------------------- | ------------------------- | ----------------------- | ------- |
| v-model        | 绑定值                             | string / number / boolean | —                       | null    |
| mode           | 编辑模式                           | string                    | edit / view / disabled  | edit    |
| disabled       | 是否禁用                           | boolean                   | —                       | false   |
| size           | 开关的尺寸                         | string                    | large / default / small | small   |
| active-text    | 打开时的文字描述                   | string                    | —                       | —       |
| inactive-text  | 关闭时的文字描述                   | string                    | —                       | —       |
| active-value   | switch打开时的值                   | string / number / boolean | —                       | true    |
| inactive-value | switch关闭时的值                   | string / number / boolean | —                       | false   |
| active-color   | switch打开时的背景色               | string                    | —                       | #0f45ea |
| inactive-color | switch关闭时的背景色               | string                    | —                       | #C0CCDA |
| name           | switch对应的name属性               | string                    | —                       | —       |
| validate-event | 改变switch状态时是否触发表单的校验 | boolean                   | —                       | true    |

### 事件

| 事件名称 | 说明                           | 回调参数   |
| -------- | ------------------------------ | ---------- |
| change   | switch状态发生变化时的回调函数 | 新状态的值 |
| input    | switch状态发生变化时的回调函数 | 新状态的值 |
| click    | switch被点击时的回调函数       | event      |
