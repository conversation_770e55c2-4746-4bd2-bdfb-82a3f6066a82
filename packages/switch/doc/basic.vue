<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-18 11:46:13
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-25 21:06:31
 * @FilePath: /vite-element-components/packages/switch/doc/basic.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <p>
      普通复选框：
      <x-switch v-model="inputValue" active-text="开" inactive-text="关" />
      <br />
    </p>
    <p>
      禁用复选框：
      <x-switch disabled v-model="inputValue" />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputValue: '',
    };
  },
};
</script>
