<script>
  import BasicVue from './basic.vue';
  import SingleVue from './single.vue';
  import AvatarVue from './avatar.vue';
  import DragVue from './drag.vue';
  import CustomVue from './custom.vue';
  import PictureVue from './picture.vue';
  import MultiLimitVue from './multi-limit.vue';
  import ProgressVue from './progress.vue';
  import PreviewImageVue from './preview-image.vue';
  import CustomListVue from './custom-list.vue';
  import DragSortVue from './drag-sort.vue';
  import ValidationFailVue from './validation-fail.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      SingleVue,
      AvatarVue,
      DragVue,
      CustomVue,
      PictureVue,
      MultiLimitVue,
      ProgressVue,
      PreviewImageVue,
      CustomListVue,
      DragSortVue,
      ValidationFailVue,
      Preview,
    }
  };
</script>

## Upload 组件

基于Element UI的文件上传组件，支持文件类型限制、大小限制、数量限制等功能。组件提供了灵活的自定义配置，支持自定义上传按钮、文件列表展示等。

### 基础用法

基础的文件上传示例，支持点击或拖拽上传文件。可以通过 `action` 指定上传接口，通过 `accept` 限制文件类型，通过 `size-limit` 限制文件大小。

<basic-vue/>
<preview  comp-name='upload' demo-name='basic'/>

### 手动上传

<single-vue/>
<preview  comp-name='upload' demo-name='single'/>

### 头像上传

头像上传示例。

<avatar-vue/>
<preview  comp-name='upload' demo-name='avatar'/>

### 单文件/图片拖拽上传

<drag-vue/>
<preview  comp-name='upload' demo-name='drag'/>

### 自定义上传方法

<custom-vue/>
<preview  comp-name='upload' demo-name='custom'/>

### 自定义风格方法

<picture-vue/>
<preview  comp-name='upload' demo-name='picture'/>

### 多文件上传限制

最多上传3个文件，每个文件不超过2MB，只能上传图片格式。上传过程中会显示统计信息。

<multi-limit-vue/>
<preview  comp-name='upload' demo-name='multi-limit'/>

### 上传进度展示

上传文件时展示进度条，直观显示上传状态和进度。

<progress-vue/>
<preview  comp-name='upload' demo-name='progress'/>

### 图片预览功能

上传图片后可以预览图片，支持多种图片格式。

<preview-image-vue/>
<preview  comp-name='upload' demo-name='preview-image'/>

### 自定义文件列表样式

自定义上传文件的列表样式，支持预览、下载和删除操作。

<custom-list-vue/>
<preview  comp-name='upload' demo-name='custom-list'/>

### 拖拽排序

支持对已上传的文件列表进行拖拽排序。

<drag-sort-vue/>
<preview  comp-name='upload' demo-name='drag-sort'/>

### 校验失败

展示文件上传校验失败的情况，如文件类型不符、文件大小超限等。

<validation-fail-vue/>
<preview  comp-name='upload' demo-name='validation-fail'/>

### 属性

| 属性名           | 说明                                     | 类型                 | 可选值           | 默认值          |
| ---------------- | ---------------------------------------- | -------------------- | ---------------- | --------------- |
| value            | 已上传的文件列表                         | Array/String         | -                | []              |
| type             | 上传文件的返回类型                       | String               | 'default'/'coos' | 'default'       |
| proxy            | coos的接口代理地址                       | String               | -                | '/api/coos_api' |
| action           | 上传接口地址                             | String               | -                | ''              |
| headers          | 上传请求头                               | Object               | -                | {}              |
| accept           | 允许上传的文件类型                       | String               | '.jpg,.png,.pdf' | ''              |
| fileSizeLimit    | 文件大小限制，当传入数字时，单位是byte。 | Number/Object/String | 1kb/1024         | null            |
| limit            | 文件上传数量限制                         | Number               | -                | 0               |
| slots            | 自定义插槽配置                           | Object               | -                | {}              |
| refName          | 组件ref引用名称                          | String               | -                | ''              |
| disabled         | 是否禁用                                 | Boolean              | -                | false           |
| mode             | 编辑模式                                 | String               | -                | 'edit'          |
| showFileList     | 是否显示已上传文件列表                   | Boolean              | -                | true            |
| coosShowFileList | coos模式时是否显示已上传文件列表         | Boolean              | -                | false           |
| canDownload      | 是否允许下载                             | Boolean              | -                | true            |
| emptyText        | 自定义空状态的描述文本                   | String               | -                | '暂无文件'      |
| ...              | el-upload的其他属性                      | -                    | -                | -               |

### 方法

| 方法名     | 说明                 | 参数 |
| ---------- | -------------------- | ---- |
| clearFiles | 清空已上传的文件列表 | -    |
| abort      | 取消上传请求         | file |
| submit     | 手动上传文件         | -    |
| preview    | 预览文件             | file |
| download   | 下载文件             | file |

### 事件

| 事件名   | 说明               | 参数                     |
| -------- | ------------------ | ------------------------ |
| change   | 文件状态改变时触发 | file, fileList           |
| success  | 文件上传成功时触发 | response, file, fileList |
| error    | 文件上传失败时触发 | err, file, fileList      |
| preview  | 点击文件预览时触发 | file                     |
| download | 点击文件下载时触发 | file                     |

### Slots

| 插槽名  | 说明           | 示例                                         |
| ------- | -------------- | -------------------------------------------- |
| default | 自定义触发元素 | `<el-button>上传</el-button>`                |
| tip     | 底部提示内容   | `<div class="el-upload__tip">提示内容</div>` |
