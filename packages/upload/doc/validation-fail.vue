<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-15 11:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-14 16:51:12
 * @FilePath: /vite-element-components/packages/upload/doc/validation-fail.vue
 * @Description: 文件类型验证失败示例
-->
<template>
  <div class="component-view">
    <x-upload
      v-model="fileList"
      action="https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo"
      accept="pdf"
      :on-error="handleError"
      :before-upload="beforeUpload"
      multiple
    >
      <el-button type="primary">选择PDF文件</el-button>
      <div class="el-upload__tip" slot="tip">只能上传PDF格式文件（演示验证失败场景）</div>
    </x-upload>

    <div v-if="errorMessages.length > 0" class="error-container">
      <h5>验证失败记录：</h5>
      <ul>
        <li v-for="(msg, index) in errorMessages" :key="index">{{ msg }}</li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      fileList: [],
      errorMessages: [],
    };
  },
  methods: {
    beforeUpload(file) {
      const isPDF = file.type === 'application/pdf';
      if (!isPDF) {
        this.errorMessages.push(`文件 ${file.name} 类型验证失败：要求PDF格式`);
        this.$message.error('仅支持PDF文件上传');
        return false;
      }
      return true;
    },
    handleError(err, file) {
      this.errorMessages.push(`文件 ${file.name} 上传失败：${err.message}`);
    },
  },
};
</script>

<style scoped>
.error-container {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #ff4d4f;
  border-radius: 4px;
  background-color: #fef0f0;
}
.error-container h5 {
  color: #ff4d4f;
  margin-bottom: 10px;
}
</style>
