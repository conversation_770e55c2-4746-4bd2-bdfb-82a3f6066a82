<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 10:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-14 16:51:01
 * @FilePath: /vite-element-components/packages/upload/doc/multi-limit.vue
 * @Description: 多文件上传限制示例
-->
<template>
  <div class="component-view">
    <p>最多上传3个文件，每个文件不超过2MB，只能上传图片格式</p>
    <x-upload
      v-model="fileList"
      action="https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo"
      :limit="3"
      :file-size="'2MB'"
      accept=".jpg,.jpeg,.png,.gif"
      :on-exceed="handleExceed"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      multiple
    >
      <el-button type="primary">选择文件</el-button>
      <div class="el-upload__tip" slot="tip">只能上传jpg/png/gif文件，且不超过2MB</div>
    </x-upload>

    <div class="upload-stats" v-if="uploadStats.total > 0">
      <h4>上传统计</h4>
      <p>总计尝试: {{ uploadStats.total }}</p>
      <p>成功: {{ uploadStats.success }}</p>
      <p>失败: {{ uploadStats.error }}</p>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      fileList: [],
      uploadStats: {
        total: 0,
        success: 0,
        error: 0,
      },
      mockResponses: [
        { status: 'success', url: 'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg' },
        { status: 'success', url: 'https://fuss10.elemecdn.com/1/34/19aa98b1fcb2781c4fba33d850549jpeg.jpeg' },
        { status: 'error', message: '服务器错误' },
      ],
    };
  },
  methods: {
    // 处理超出文件数量限制
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`,
      );
    },
    // 上传前检查文件
    beforeUpload(file) {
      const isImage = file.type.startsWith('image/');
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$message.error('上传文件只能是图片格式!');
        return false;
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
        return false;
      }

      this.uploadStats.total += 1;
      return true;
    },
    // 上传成功处理
    handleSuccess(response, file, fileList) {
      // 模拟随机响应
      const mockResponse = this.mockResponses[Math.floor(Math.random() * this.mockResponses.length)];

      if (mockResponse.status === 'success') {
        this.uploadStats.success += 1;
        this.$message.success(`文件 ${file.name} 上传成功`);
        // 模拟设置文件URL
        file.url = mockResponse.url;
      } else {
        this.uploadStats.error += 1;
        this.$message.error(`文件 ${file.name} 上传失败: ${mockResponse.message}`);
        // 从列表中移除失败文件
        const index = fileList.indexOf(file);
        if (index !== -1) {
          fileList.splice(index, 1);
        }
      }
    },
    // 上传失败处理
    handleError(err, file, fileList) {
      this.uploadStats.error += 1;
      this.$message.error(`文件 ${file.name} 上传失败`);
    },
  },
};
</script>

<style lang="scss" scoped>
.upload-stats {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f5f7fa;
}
</style>
