<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 11:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-25 11:42:40
 * @FilePath: /vite-element-components/packages/upload/doc/preview-image.vue
 * @Description: 文件预览功能示例
-->
<template>
  <div class="component-view">
    <x-upload
      v-model="fileList"
      action="https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo"
      list-type="picture-card"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      multiple
    >
      <i class="el-icon-plus"></i>
    </x-upload>

    <el-dialog :visible.sync="previewVisible" append-to-body>
      <div class="preview-container">
        <img v-if="previewType === 'image'" :src="previewUrl" class="preview-image" />
        <div v-else class="preview-file">
          <i class="el-icon-document" style="font-size: 48px"></i>
          <p>{{ previewName }}</p>
          <el-button type="primary" size="small" @click="downloadFile">下载文件</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      fileList: [
        {
          name: 'mountain.jpeg',
          url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
        },
      ],
      previewVisible: false,
      previewUrl: '',
      previewName: '',
      previewType: 'image',
      mockResponses: [
        { status: 'success', url: 'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg' },
        { status: 'success', url: 'https://fuss10.elemecdn.com/1/34/19aa98b1fcb2781c4fba33d850549jpeg.jpeg' },
      ],
    };
  },
  methods: {
    // 处理文件预览
    handlePreview(file) {
      this.previewName = file.name;
      this.previewUrl = file.url;

      // 判断文件类型
      if (file.raw) {
        // 如果是图片类型
        if (file.raw.type.indexOf('image') !== -1) {
          this.previewType = 'image';
          this.previewUrl = URL.createObjectURL(file.raw);
        } else {
          this.previewType = 'file';
        }
      } else {
        // 已上传的文件，根据扩展名判断
        const fileExt = file.name.split('.').pop().toLowerCase();
        const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];

        if (imageExts.includes(fileExt)) {
          this.previewType = 'image';
        } else {
          this.previewType = 'file';
        }
      }

      this.previewVisible = true;
    },
    // 处理文件移除
    handleRemove(file, fileList) {
      this.$message.info(`已移除文件: ${file.name}`);
    },
    // 上传前检查
    beforeUpload(file) {
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!');
        return false;
      }

      return true;
    },
    // 上传成功处理
    handleSuccess(response, file, fileList) {
      // 模拟随机响应
      const mockResponse = this.mockResponses[Math.floor(Math.random() * this.mockResponses.length)];

      this.$message.success(`文件 ${file.name} 上传成功`);
      file.url = mockResponse.url;
    },
    // 下载文件
    downloadFile() {
      if (this.previewUrl) {
        const link = document.createElement('a');
        link.href = this.previewUrl;
        link.download = this.previewName;
        link.click();
      } else {
        this.$message.warning('文件链接不存在');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
}

.preview-file {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;

  p {
    margin: 10px 0;
    font-size: 16px;
    color: #606266;
  }
}
</style>
