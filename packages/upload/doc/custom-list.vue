<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 11:30:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-15 14:06:00
 * @FilePath: /vite-element-components/packages/upload/doc/custom-list.vue
 * @Description: 自定义文件列表样式示例
-->
<template>
  <div class="component-view">
    <x-upload
      ref="upload"
      v-model="fileList"
      action="https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :show-file-list="false"
      multiple
    >
      <el-button type="primary">选择文件</el-button>
      <div class="el-upload__tip" slot="tip">点击文件名可以预览文件</div>
    </x-upload>

    <div v-if="fileList.length > 0" class="custom-file-list">
      <div v-for="(file, index) in fileList" :key="index" class="file-item">
        <div class="file-info" @click="handlePreview(file)">
          <i :class="getFileIcon(file)"></i>
          <span class="file-name">{{ file.name }}</span>
        </div>
        <div class="file-actions">
          <el-button type="text" icon="el-icon-view" @click="handlePreview(file)" title="预览"></el-button>
          <el-button type="text" icon="el-icon-download" @click="downloadFile(file)" title="下载"></el-button>
          <el-button type="text" icon="el-icon-delete" @click="handleRemove(file)" title="删除"></el-button>
        </div>
      </div>
    </div>

    <el-dialog :visible.sync="previewVisible" append-to-body>
      <div class="preview-container">
        <img v-if="previewType === 'image'" :src="previewUrl" class="preview-image" />
        <div v-else class="preview-file">
          <i class="el-icon-document" style="font-size: 48px"></i>
          <p>{{ previewName }}</p>
          <el-button type="primary" size="small" @click="downloadFile(previewFile)">下载文件</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      fileList: [
        {
          name: 'document.pdf',
          url: 'https://example.com/document.pdf',
        },
        {
          name: 'image.jpg',
          url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
        },
        {
          name: 'spreadsheet.xlsx',
          url: 'https://example.com/spreadsheet.xlsx',
        },
      ],
      previewVisible: false,
      previewUrl: '',
      previewName: '',
      previewType: 'image',
      previewFile: null,
      mockResponses: [
        { status: 'success', url: 'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg' },
        { status: 'success', url: 'https://example.com/document.pdf' },
        { status: 'success', url: 'https://example.com/spreadsheet.xlsx' },
      ],
    };
  },
  methods: {
    // 获取文件图标
    getFileIcon(file) {
      const fileExt = file.name.split('.').pop().toLowerCase();

      // 根据文件扩展名返回不同的图标
      const iconMap = {
        pdf: 'el-icon-document',
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        xls: 'el-icon-tickets',
        xlsx: 'el-icon-tickets',
        ppt: 'el-icon-document',
        pptx: 'el-icon-document',
        jpg: 'el-icon-picture',
        jpeg: 'el-icon-picture',
        png: 'el-icon-picture',
        gif: 'el-icon-picture',
        zip: 'el-icon-folder',
        rar: 'el-icon-folder',
        txt: 'el-icon-document',
      };

      return iconMap[fileExt] || 'el-icon-document';
    },
    // 处理文件预览
    handlePreview(file) {
      this.previewName = file.name;
      this.previewUrl = file.url;
      this.previewFile = file;

      // 判断文件类型
      const fileExt = file.name.split('.').pop().toLowerCase();
      const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];

      if (imageExts.includes(fileExt)) {
        this.previewType = 'image';
      } else {
        this.previewType = 'file';
      }

      this.previewVisible = true;
    },
    // 处理文件移除
    handleRemove(file) {
      const index = this.fileList.indexOf(file);
      if (index !== -1) {
        this.fileList.splice(index, 1);
      }
      this.$message.info(`已移除文件: ${file.name}`);
    },
    // 上传前检查
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!');
        return false;
      }

      return true;
    },
    // 上传成功处理
    handleSuccess(response, file) {
      // 模拟随机响应
      const mockResponse = this.mockResponses[Math.floor(Math.random() * this.mockResponses.length)];

      this.$message.success(`文件 ${file.name} 上传成功`);
      file.url = mockResponse.url;
    },
    // 下载文件
    downloadFile(file) {
      if (file && file.url) {
        const link = document.createElement('a');
        link.href = file.url;
        link.download = file.name;
        link.click();
      } else {
        this.$message.warning('文件链接不存在');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.custom-file-list {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #ebeef5;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f5f7fa;
  }
}

.file-info {
  display: flex;
  align-items: center;
  cursor: pointer;

  i {
    font-size: 20px;
    margin-right: 10px;
    color: #909399;
  }
}

.file-name {
  font-size: 14px;
  color: #606266;
}

.file-actions {
  display: flex;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
}

.preview-file {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;

  p {
    margin: 10px 0;
    font-size: 16px;
    color: #606266;
  }
}
</style>
