<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 11:46:45
 * @FilePath: /vite-element-components/packages/upload/doc/avatar.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <div>
      <x-upload
        v-model="fileIds"
        type="coos"
        class="avatar-uploader"
        action="https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo"
        list-type="picture-card"
        :on-success="handleAvatarSuccess"
      >
        <i class="el-icon-plus avatar-uploader-icon"></i>
      </x-upload>
    </div>
    <div>
      查看模式
      <x-upload
        v-model="fileIds"
        type="coos"
        class="avatar-uploader"
        mode="disabled"
        action="https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo"
        list-type="picture-card"
        coosShowFileList
        :on-success="handleAvatarSuccess"
      >
        <i class="el-icon-plus avatar-uploader-icon"></i>
      </x-upload>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      imageUrl: '',
      fileIds: '1904110950394576896,1909428534858301440,1905163410579505152,1907610930219376640',
      fileList: [
        {
          name: 'food.jpeg',
          url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100',
        },
        {
          name: 'food2.jpeg',
          url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100',
        },
      ],
    };
  },
  methods: {
    handleAvatarSuccess(res, file) {
      this.imageUrl = URL.createObjectURL(file.raw);
    },
    // beforeAvatarUpload(file) {
    //   const isJPG = file.type === 'image/png';
    //   const isLt2M = file.size / 1024 / 1024 < 2;

    //   if (!isJPG) {
    //     this.$message.error('上传头像图片只能是 PNG 格式!');
    //     return isJPG;
    //   }
    //   if (!isLt2M) {
    //     this.$message.error('上传头像图片大小不能超过 2MB!');
    //     return isLt2M;
    //   }
    //   return isJPG && isLt2M;
    // },
  },
};
</script>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: var(--brand-6, #0f45ea);
}
</style>
