<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 10:30:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-25 11:42:44
 * @FilePath: /vite-element-components/packages/upload/doc/progress.vue
 * @Description: 上传进度展示示例
-->
<template>
  <div class="component-view">
    <x-upload
      v-model="fileList"
      action="https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo"
      :on-progress="onProgress"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :mock-progress-duration="300"
      multiple
    >
      <el-button type="primary">选择文件</el-button>
      <div class="el-upload__tip" slot="tip">上传过程中会显示进度</div>
    </x-upload>

    <div v-if="uploadingFiles.length > 0" class="progress-container">
      <div v-for="(file, index) in uploadingFiles" :key="index" class="file-progress">
        <div class="file-info">
          <span class="file-name">{{ file.name }}</span>
          <span class="file-percent">{{ file.percent }}%</span>
        </div>
        <el-progress :percentage="file.percent" :status="file.status || undefined"></el-progress>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      fileList: [],
      uploadingFiles: [],
      mockResponses: [
        { status: 'success', url: 'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg' },
        { status: 'success', url: 'https://fuss10.elemecdn.com/1/34/19aa98b1fcb2781c4fba33d850549jpeg.jpeg' },
        { status: 'error', message: '服务器错误' },
      ],
    };
  },
  methods: {
    // 上传前处理
    beforeUpload(file) {
      // 添加文件到上传列表
      this.uploadingFiles.push({
        uid: file.uid,
        name: file.name,
        percent: 0,
        status: 'normal',
      });
      return true;
    },
    // 处理上传进度
    onProgress({ e, file, currentFiles, percent, type }) {
      // 查找并更新对应文件的进度
      const uploadingFile = this.uploadingFiles.find((item) => item.uid === file.uid);
      if (uploadingFile) {
        uploadingFile.percent = Math.floor(percent);
      }
    },
    // 上传成功处理
    handleSuccess(response, file, fileList) {
      // 模拟随机响应
      const mockResponse = this.mockResponses[Math.floor(Math.random() * this.mockResponses.length)];

      // 查找对应的上传文件
      const uploadingFile = this.uploadingFiles.find((item) => item.uid === file.uid);

      if (mockResponse.status === 'success') {
        if (uploadingFile) {
          uploadingFile.percent = 100;
          uploadingFile.status = 'success';

          // 3秒后从上传列表中移除
          setTimeout(() => {
            const index = this.uploadingFiles.findIndex((item) => item.uid === file.uid);
            if (index !== -1) {
              this.uploadingFiles.splice(index, 1);
            }
          }, 3000);
        }

        this.$message.success(`文件 ${file.name} 上传成功`);
        file.url = mockResponse.url;
      } else {
        if (uploadingFile) {
          uploadingFile.status = 'exception';
        }

        this.$message.error(`文件 ${file.name} 上传失败: ${mockResponse.message}`);
        // 从列表中移除失败文件
        const index = fileList.indexOf(file);
        if (index !== -1) {
          fileList.splice(index, 1);
        }
      }
    },
    // 上传失败处理
    handleError(err, file, fileList) {
      // 查找对应的上传文件
      const uploadingFile = this.uploadingFiles.find((item) => item.uid === file.uid);
      if (uploadingFile) {
        uploadingFile.status = 'exception';
      }

      this.$message.error(`文件 ${file.name} 上传失败`);
    },
  },
};
</script>

<style lang="scss" scoped>
.progress-container {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.file-progress {
  margin-bottom: 15px;
}

.file-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.file-name {
  font-size: 14px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 80%;
}

.file-percent {
  font-size: 14px;
  color: #909399;
}
</style>
