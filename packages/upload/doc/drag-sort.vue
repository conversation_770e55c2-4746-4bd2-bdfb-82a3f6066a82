<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-15 10:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-09 14:11:32
 * @FilePath: /vite-element-components/packages/upload/doc/drag-sort.vue
 * @Description: 拖拽排序上传示例
-->
<template>
  <div class="component-view">
    <x-upload
      v-model="fileList"
      action="https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo"
      :drag-sort="true"
      :on-sort="handleSort"
      list-type="picture"
      multiple
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
    </x-upload>
  </div>
</template>

<script>
export default {
  data() {
    return {
      fileList: [
        {
          name: 'food1.jpeg',
          url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg',
        },
        {
          name: 'food2.jpeg',
          url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg',
        },
      ],
    };
  },
  methods: {
    handleSort(res) {
      console.log('%c [  ]-44-「drag-sort」', 'font-size:13px; background:pink; color:#bf2c9f;', res);
    },
  },
};
</script>
