<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-25 17:50:15
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-29 17:21:20
 * @FilePath: /vite-element-components/packages/upload/doc/basic.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <div>
      普通上传：
      <x-upload
        v-model="fileList"
        class="mgt-10"
        multiple
        accept=".jpeg,.xlsx"
        :action="action"
        file-size-limit="1mb"
        :limit="3"
        :on-remove="onRemove"
        :on-change="onChange"
      />
    </div>
    <div style="margin-top: 10px">
      coos通用上传：主要设置type='coos'，headers=coos中utils的uploadParams，proxy=coos中config的baseUrl,走coos的通用上传接口
      <x-upload
        v-model="fileIds"
        class="mgt-10"
        multiple
        type="coos"
        :headers="{}"
        :on-remove="onRemove"
        :on-change="onChange"
      />
      因coos通用上传需token及header参数，故此不支持展示上传成功
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      action: 'https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo',
      fileList: [
        {
          name: 'X)F%8FT6MWGQDC$F1WMWUU0.png',
          url: 'http://dayuding.eimm.wisesoft.net.cn:8099/minio/coos/1909428534472425472.png',
        },
        {
          name: '科研项目申报管理系统需求说明.docx',
          url: 'http://dayuding.eimm.wisesoft.net.cn:8099/minio/coos/1904110949866094592.docx',
        },
      ],
      fileIds: '1904110950394576896,1909428534858301440,1905163410579505152,1907610930219376640',
    };
  },
  methods: {
    onRemove(file, fileList) {
      console.log('%c [ onRemove ]-118-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', file, fileList);
    },
    onChange(file, fileList) {
      console.log('%c [ onChange ]-118-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', file, fileList);
    },
  },
};
</script>
<style lang="scss" scoped>
.mgt-10 {
  margin-top: 10px;
}
</style>
