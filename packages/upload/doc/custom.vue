<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-18 11:46:13
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-25 11:41:32
 * @FilePath: /vite-element-components/packages/upload/doc/custom.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <x-upload v-model="fileList" list-type="picture" :action="action" :on-remove="onRemove" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      action: 'https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo',
      fileList: [
        {
          name: 'food.jpeg',
          url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100',
        },
        {
          name: 'food2.jpeg',
          url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100',
        },
      ],
    };
  },
  computed: {
    // 弹窗事件
    controlOptions() {
      return {};
    },
  },
  methods: {
    onRemove(file, fileList) {
      console.log('%c [ 1 ]-177-「index」', 'font-size:13px; background:pink; color:#bf2c9f;', file, fileList);
    },
  },
};
</script>
