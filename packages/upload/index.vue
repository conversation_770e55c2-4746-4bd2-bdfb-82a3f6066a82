<script lang="jsx">
import XDialog from '../dialog/index';
import XTooltip from '../tooltip/index';

const REGEXP_DROP0 = /\.00$|(\.[0-9]*[1-9])0+$/; // 匹配小数点后两位的数字，包括小数点本身和末尾的0

/**
 * @description 基于Element UI的文件上传组件，支持文件类型限制、大小限制、数量限制等功能
 * <AUTHOR>
 */
export default {
  name: 'XUpload',
  components: {
    XDialog,
    XTooltip,
  },
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    /**
     * @description 已上传的文件列表
     * @type {Array}
     */
    value: {
      type: [Array, String],
      default: () => [],
    },
    /**
     * @description 上传文件的返回类型
     * @type {String}
     * @default 'default'
     * @example 'default' | 'coos'
     */
    type: {
      type: String,
      default: 'default',
    },
    // coos上传文件时是否显示文件列表
    coosShowFileList: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 上传文件的代理地址
     * @type {String}
     * @default '/api/coos_api'
     */
    proxy: {
      type: String,
      default: '/api/coos_api',
    },
    /**
     * @description 上传接口地址
     * @type {String}
     */
    action: {
      type: String,
      default: '',
    },
    /**
     * @description 上传请求头
     * @type {Object}
     */
    headers: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 允许上传的文件类型
     * @type {string}
     * @example '.jpg,.png,.pdf'
     */
    accept: {
      type: String,
      default: null,
    },
    /**
     * @description 允许上传的文件大小限制
     * @type {Number|String}
     * @example 1000 // 1000KB
     */
    fileSizeLimit: {
      type: [Number, String],
      default: null,
    },
    /**
     * @description 文件上传数量限制，0表示不限制
     * @type {Number}
     */
    limit: {
      type: [Number],
      default: 0,
    },
    /**
     * @description 自定义插槽配置
     * @type {Object}
     * @property {Function|VNode} trigger - 触发上传的区域
     * @property {Function|VNode} default - 默认内容区域
     * @property {Function|VNode} tip - 底部提示内容
     */
    slots: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 组件ref引用名称
     * @type {String}
     */
    refName: {
      type: String,
      default: '',
    },
    /**
     * @description 是否禁用
     * @type {Boolean}
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * 编辑模式
     * @type {String}
     * @default 'edit'
     */
    mode: {
      type: String,
      default: 'edit',
    },
    /**
     * @description 是否显示已上传文件列表
     * @type {Boolean}
     */
    showFileList: {
      type: Boolean,
      default: true,
    },
    /**
     * @description 是否允许下载
     * @type {Boolean}
     * @default false
     */
    canDownload: {
      type: Boolean,
      default: true,
    },
    /**
     * @description 自定义空状态的描述文本
     * @type {String}
     * @default 暂无文件
     */
    emptyText: {
      type: String,
      default: '暂无文件',
    },
    /**
     * @description 自定义上传文件成功回调函数
     * @type {Function}
     * @default () => null
     */
    onSuccess: {
      type: Function,
      default: () => null,
    },
    /**
     * @description 在线预览的地址
     * @type {String}
     * @default 'http://coos.wisesoft.org.cn/coos_file_preview_server/onlinePreview'
     */
    previewUrl: {
      type: String,
      default: 'http://coos.wisesoft.org.cn/coos_file_preview_server/onlinePreview',
    },
  },
  data() {
    return {
      fileList: [],
      previewDialog: {
        visible: false,
        loading: true,
        title: '',
        content: '',
      },
    };
  },

  computed: {
    inputValue: {
      get() {
        if (this.type === 'default') {
          return this.value || [];
        }
        return this.value || '';
      },
      set(val) {
        this.$emit('update:value', val);
      },
    },
    // 文件大小 单位b
    fileSizeByte() {
      const sizeRegex = /^(\d+)\s*(KB|MB|GB|B)?$/i;
      let fileSize = 0;
      const match = sizeRegex.exec(this.fileSizeLimit);
      if (typeof this.fileSizeLimit === 'number') {
        fileSize = this.fileSizeLimit;
      } else if (match) {
        const [, size, unit = 'B'] = match;
        switch (unit.toUpperCase()) {
          case 'GB':
            fileSize = size * 1024 * 1024 * 1024;
            break;
          case 'MB':
            fileSize = size * 1024 * 1024;
            break;
          case 'KB':
            fileSize = size * 1024;
            break;
          case 'B':
            fileSize = size;
            break;
          default:
            console.log('传入的文件大小不符合规则');
            return false;
        }
      }
      return fileSize;
    },
    // 文件大小 字符串
    fileSizeStr() {
      const sizes = ['KB', 'MB', 'GB'];
      let fileSize = this.fileSizeByte;

      let i = -1;
      while (fileSize >= 1024 && i < sizes.length - 1) {
        fileSize /= 1024;
        i += 1;
      }
      return `${Math.ceil(fileSize)}${sizes[i]}`;
    },
  },
  watch: {
    inputValue: {
      handler(val) {
        if (val) {
          if (!val.length) {
            this.fileList = [];
          }
          if (!this.fileList.length) {
            this.getFileList();
          }
        } else {
          this.fileList = [];
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 格式化文件大小
    formatFileBytes(bytes, isLowercase) {
      const units = ['B', 'KB', 'MB', 'GB'];
      let unitIndex = 0;

      while (bytes >= 1024 && unitIndex < units.length - 1) {
        bytes /= 1024;
        unitIndex += 1;
      }
      const size = bytes?.toFixed(2).replace(REGEXP_DROP0, ''); // 去除无意义的0

      return `${size}${isLowercase ? units[unitIndex].toLowerCase() : units[unitIndex]}`;
    },
    // 获取文件列表
    getFileList() {
      if (this.type === 'coos') {
        // 必须要有值时才更新
        if (this.inputValue) {
          const xhr = new XMLHttpRequest();
          xhr.open('GET', `${this.proxy}/api/sys/files/${this.inputValue}`, true);

          // 设置超时时间（30秒）
          xhr.timeout = 30000;
          xhr.ontimeout = () => {
            console.error('获取文件列表请求超时');
          };

          // 添加请求头
          Object.keys(this.headers).forEach((key) => {
            xhr.setRequestHeader(key, this.headers[key]);
          });

          xhr.onload = () => {
            if (xhr.status === 200) {
              try {
                const { code, result } = JSON.parse(xhr.responseText);
                if (code === 200) {
                  // 更新文件列表
                  this.fileList = result.map((item) => ({
                    size: item.fileSize,
                    type: item.fileType,
                    name: item.originalFileName || item.fileName,
                    url: /^(http|https):\/\/.+/.test(item.fileUrl)
                      ? item.fileUrl
                      : (this.$appConfig.serveUrl || window.location.origin) + item.fileUrl,
                    ...item,
                  }));
                  this.inputValue = this.fileList.map((item) => item.fileId).join(',');
                } else {
                  console.error('获取文件列表失败:');

                  // const res = [
                  //   {
                  //     fileId: '1909428534858301440',
                  //     fileType: 'image',
                  //     fileName: '1909428534472425472.png',
                  //     originalFileName: 'X)F%8FT6MWGQDC$F1WMWUU0.png',
                  //     fileExt: 'png',
                  //     fileSize: 132500,
                  //     filePath: '/minio/coos/1909428534472425472.png',
                  //     realFilePath: '/1909428534472425472.png',
                  //     fileUrl: 'http://dayuding.eimm.wisesoft.net.cn:8099/minio/coos/1909428534472425472.png',
                  //     tenantId: null,
                  //     businessId: null,
                  //   },
                  //   {
                  //     fileId: '1904110950394576896',
                  //     fileType: 'doc',
                  //     fileName: '1904110949866094592.docx',
                  //     originalFileName: '科研项目申报管理系统需求说明科研项目申报管理系统需求说明.docx',
                  //     fileExt: 'docx',
                  //     fileSize: 20718,
                  //     filePath: '/minio/coos/1904110949866094592.docx',
                  //     realFilePath: '/1904110949866094592.docx',
                  //     fileUrl: 'http://dayuding.eimm.wisesoft.net.cn:8099/minio/coos/1904110949866094592.docx',
                  //     tenantId: null,
                  //     businessId: null,
                  //   },
                  //   {
                  //     fileId: '1905163410579505152',
                  //     fileType: 'unknown',
                  //     fileName: '1905163410344624128.url',
                  //     originalFileName: '用户-日志填报.url',
                  //     fileExt: 'url',
                  //     fileSize: 149,
                  //     filePath: '/minio/coos/1905163410344624128.url',
                  //     realFilePath: '/1905163410344624128.url',
                  //     fileUrl: 'http://dayuding.eimm.wisesoft.net.cn:8099/minio/coos/1905163410344624128.url',
                  //     tenantId: null,
                  //     businessId: null,
                  //   },
                  //   {
                  //     fileId: '1907610930219376640',
                  //     fileType: 'excel',
                  //     fileName: '1907610930034827264.xlsx',
                  //     originalFileName: '11-项目质量结果报告模板.xlsx',
                  //     fileExt: 'xlsx',
                  //     fileSize: 19091,
                  //     filePath: '/minio/coos/1907610930034827264.xlsx',
                  //     realFilePath: '/1907610930034827264.xlsx',
                  //     fileUrl: 'http://dayuding.eimm.wisesoft.net.cn:8099/minio/coos/1907610930034827264.xlsx',
                  //     tenantId: null,
                  //     businessId: null,
                  //   },
                  //   {
                  //     fileId: '1907610930500395008',
                  //     fileType: 'excel',
                  //     fileName: '1907610930286485504.xlsx',
                  //     originalFileName: '09-性能测试报告模板.xlsx',
                  //     fileExt: 'xlsx',
                  //     fileSize: 45652,
                  //     filePath: '/minio/coos/1907610930286485504.xlsx',
                  //     realFilePath: '/1907610930286485504.xlsx',
                  //     fileUrl: 'http://dayuding.eimm.wisesoft.net.cn:8099/minio/coos/1907610930286485504.xlsx',
                  //     tenantId: null,
                  //     businessId: null,
                  //   },
                  //   {
                  //     fileId: '1907610931003711488',
                  //     fileType: 'doc',
                  //     fileName: '1907610930596864000.doc',
                  //     originalFileName: '12-测试报告模板-外部.doc',
                  //     fileExt: 'doc',
                  //     fileSize: 199378,
                  //     filePath: '/minio/coos/1907610930596864000.doc',
                  //     realFilePath: '/1907610930596864000.doc',
                  //     fileUrl: 'http://dayuding.eimm.wisesoft.net.cn:8099/minio/coos/1907610930596864000.doc',
                  //     tenantId: null,
                  //     businessId: null,
                  //   },
                  //   {
                  //     fileId: '1912777938609618944',
                  //     fileType: 'excel',
                  //     fileName: '1912777938387320832.xlsx',
                  //     originalFileName: '用车申请信息.xlsx',
                  //     fileExt: 'xlsx',
                  //     fileSize: 34321,
                  //     filePath: '/minio/coos/1912777938387320832.xlsx',
                  //     realFilePath: '/1912777938387320832.xlsx',
                  //     fileUrl: 'http://dayuding.eimm.wisesoft.net.cn:8099/minio/coos/1912777938387320832.xlsx',
                  //     tenantId: 1114,
                  //     businessId: null,
                  //   },
                  //   {
                  //     fileId: '1913037926865104896',
                  //     fileType: 'pdf',
                  //     fileName: '1913037926126907392.pdf',
                  //     originalFileName: '测试pdf.pdf',
                  //     fileExt: 'pdf',
                  //     fileSize: 103951,
                  //     filePath: '/minio/coos/1913037926126907392.pdf',
                  //     realFilePath: '/1913037926126907392.pdf',
                  //     fileUrl: 'http://dayuding.eimm.wisesoft.net.cn:8099/minio/coos/1913037926126907392.pdf',
                  //     tenantId: 1114,
                  //     businessId: null,
                  //   },
                  // ];
                  // // 更新文件列表
                  // this.fileList = res.map((item) => ({
                  //   size: item.fileSize,
                  //   type: item.fileType,
                  //   name: item.originalFileName || item.fileName,
                  //   url: item.fileUrl,
                  //   ...item,
                  // }));
                  // this.inputValue = this.fileList.map((item) => item.fileId).join(',');
                }
              } catch (error) {
                console.error('解析响应数据失败:', error);
              }
            } else {
              console.error(`获取文件列表失败: ${xhr.statusText}`);
            }
          };
          xhr.onerror = (err) => {
            console.error('网络请求失败', err);
          };
          xhr.send();
        }
      } else {
        this.fileList = this.inputValue;
      }
    },
    // 渲染触发器
    renderTrigger() {
      const slot = this.$slots?.trigger || this.slots?.trigger;
      if (slot) {
        if (typeof slot === 'function') {
          return slot();
        }
        return slot;
      }
      return '';
    },
    // 渲染默认内容
    renderDefault() {
      const slot = this.$slots?.default || this.slots?.default;
      if (slot) {
        if (typeof slot === 'function') {
          return slot();
        }
        return slot;
      }
      return (
        <el-button type="primary" disabled={this.mode === 'view' || this.disabled}>
          点击上传
        </el-button>
      );
    },
    // 渲染底部内容
    renderTip() {
      const slot = this.$slots?.tip || this.slots?.tip;
      if (slot && this.mode === 'edit') {
        if (typeof slot === 'function') {
          return slot();
        }
        return slot;
      }
      return '';
    },
    // 手动上传
    submit() {
      return this.$refs[this.refName].submit();
    },
    // 清空已上传的文件列表（该方法不支持在 before-upload 中调用）
    clearFiles() {
      return this.$refs[this.refName].clearFiles();
    },
    // 取消上传请求
    abort(file) {
      return this.$refs[this.refName].abort(file);
    },
    // 上传前校验
    beforeUpload(file) {
      if (!this.$attrs?.beforeUpload) {
        // 获取文件的后缀并转为小写
        const fileExt = file.name.split('.').pop().toLowerCase();
        // 将 accept 拆分为数组，去掉点并转小写
        const acceptList = (this.accept || '')
          .split(',')
          .filter((item) => item)
          .map((ext) => ext.replace(/^\./, '').toLowerCase());
        // 存在文件类型属性则自动校验文件格式
        if (acceptList.length && !acceptList.includes(fileExt)) {
          this.$message.warning({ message: `不允许上传${fileExt}格式的文件` });
          return false;
        }
        if (this.fileSizeLimit) {
          // 判断当前文件是否满足限制的大小 0为不限制
          if (this.fileSizeByte && file.size > this.fileSizeByte) {
            this.$message.warning({
              message: `不允许上传超过${this.fileSizeStr}大小的文件`,
            });
            return false;
          }
        }
      } else {
        // 存在默认校验规则，则根据是否有外部上传前校验方法，否则默认通过
        return this.$attrs?.beforeUpload ? this.$attrs?.beforeUpload(file) : true;
      }

      return true;
    },
    // 获取文件图标
    getFileIcon(file) {
      switch (file.type) {
        case 'image':
          return 'el-icon-picture';
        case 'video':
          return 'el-icon-video-camera';
        default:
          return 'el-icon-document';
      }
    },
    // 渲染文件操作按钮
    renderFileActions(file) {
      return (
        <div class="file-actions">
          <x-button type="text" onClick={() => this.handleAction(file, 'preview')} label="预览" />
          {this.canDownload && (
            <x-button type="text" label="下载" onClick={() => this.handleAction(file, 'download')} />
          )}
          {this.mode === 'edit' && (
            <el-popconfirm title="是否确定删除该文件？" onConfirm={() => this.handleAction(file, 'delete')}>
              <x-button slot="reference" type="text" theme="danger" style="margin-left:10px;" label="删除" />
            </el-popconfirm>
          )}
        </div>
      );
    },
    // 渲染文件列表
    renderFileList() {
      // 非coos上传不渲染
      if (this.type === 'default') {
        return false;
      }
      // coos上传 无数据时
      if (this.fileList.length === 0 && this.mode !== 'edit') {
        return <div class="x-upload-empty">{this.emptyText}</div>;
      }
      // coos上传 显示文件列表场景
      if (this.coosShowFileList) {
        return false;
      }
      return (
        this.fileList.length > 0 && (
          <el-form class="x-upload-files">
            {this.fileList.map((file) => (
              <div class="x-upload-files-item" key={file.uid}>
                <div class="file-name">
                  <i class={this.getFileIcon(file)} style="font-size:18px;"></i>
                  <x-tooltip content={file.name} class="file-name-text" />
                </div>
                <div class="file-other">
                  <div class="file-size">{this.formatFileBytes(file.size)}</div>
                  {this.renderFileActions(file)}
                </div>
              </div>
            ))}
          </el-form>
        )
      );
    },
    // 处理文件操作
    handleAction(file, type) {
      const fileIndex = this.fileList.findIndex((item) => item.uid === file.uid);
      switch (type) {
        case 'delete':
          this.fileList.splice(fileIndex, 1);
          this.inputValue = this.fileList.map((item) => item.fileId).join(',');
          if (this.$attrs['on-remove']) {
            this.$attrs['on-remove'](file, this.fileList);
          }
          if (this.$attrs?.onRemove) {
            this.$attrs.onRemove(file, this.fileList);
          }
          break;
        case 'preview':
          this.handlePreviewFile(file);
          break;
        case 'download':
          this.handleDownloadFile(file);
          if (this.$attrs['on-download']) {
            this.$attrs['on-download'](file);
          }
          if (this.$attrs?.onDownload) {
            this.$attrs.onDownload(file);
          }
          break;
        default:
          this.$emit(type, file);
          break;
      }
    },
    // 下载文件的方法
    handleDownloadFile(file) {
      const fileUrl = /^(http|https):\/\/.+/.test(file.url)
        ? file.url
        : (this.$appConfig.serveUrl || window.location.origin) + file.url;
      const isRemoteFile = /^(http|https):\/\/.+/.test(file.url);
      const fileName = file.name || file.response?.originalFileName || 'download';
      if (!file?.url) {
        this.$message.error('文件地址不存在');
        return; // 直接返回，不执行下载
      }
      if (isRemoteFile) {
        // 远程文件下载
        const xhr = new XMLHttpRequest();
        xhr.open('GET', fileUrl, true);
        xhr.responseType = 'blob';
        xhr.onload = () => {
          if (xhr.status === 200) {
            const blob = new Blob([xhr.response]);
            const downloadUrl = URL.createObjectURL(blob);
            this.downloadFile(downloadUrl, fileName);
            URL.revokeObjectURL(downloadUrl);
          } else {
            this.$message.error('文件下载失败');
          }
        };
        xhr.onerror = () => {
          this.$message.error('文件下载失败');
        };
        xhr.send();
      } else {
        // 本地文件下载
        this.downloadFile(file.url, fileName);
      }
    },
    // 创建下载链接并触发下载
    downloadFile(url, fileName) {
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    // 处理文件预览
    handlePreviewFile(file) {
      if (!file?.url) {
        this.$message.error('文件地址不存在');
        return;
      }
      const fileExt = (file.name || '').split('.').pop()?.toLowerCase();
      const fileType = file.type || '';
      const fileUrl = /^(http|https):\/\/.+/.test(file.url)
        ? file.url
        : (this.$appConfig.serveUrl || window.location.origin) + file.url;
      const previewUrl = `${this.previewUrl}?url=${encodeURIComponent(btoa(encodeURIComponent(fileUrl)))}`;

      // 图片预览
      const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'];
      if (fileType.includes('image') || imageExts.includes(fileExt)) {
        const image = new Image();
        image.src = fileUrl;

        image.onload = () => {
          this.previewDialog = {
            visible: true,
            title: file.name,
            width: '1200px',
            loading: false,
            content: `<div style="display: flex; justify-content: center; align-items: center; min-height: 200px;">
              <img src="${fileUrl}" style="max-width: 100%; max-height: 80vh; object-fit: contain;" />
            </div>`,
          };
        };
        image.onerror = () => {
          this.$message.error('图片加载失败');
        };
        return;
      }

      // 视频预览
      const videoExts = ['mp4', 'webm', 'ogg', 'mov', 'm4v', 'avi'];
      if (fileType.includes('video') || videoExts.includes(fileExt)) {
        this.previewDialog = {
          visible: true,
          title: file.name,
          width: '1200px',
          loading: false,
          content: `<div style="display: flex; justify-content: center; align-items: center; min-height: 200px;">
            <video src="${fileUrl}" controls controlsList="nodownload" style="max-width: 100%; max-height: 80vh;">
              您的浏览器不支持视频播放
            </video>
          </div>`,
        };
        return;
      }

      // 音频预览
      const audioExts = ['mp3', 'wav', 'ogg', 'aac', 'm4a'];
      if (fileType.includes('audio') || audioExts.includes(fileExt)) {
        this.previewDialog = {
          visible: true,
          title: file.name,
          width: '480px',
          loading: false,
          content: `<div style="padding: 20px;">
            <audio src="${fileUrl}" controls controlsList="nodownload" style="width: 100%;">
              您的浏览器不支持音频播放
            </audio>
          </div>`,
        };
        return;
      }
      // PDF预览
      if (fileType.includes('pdf') || fileExt === 'pdf') {
        const xhr = new XMLHttpRequest();
        xhr.open('GET', fileUrl, true);
        xhr.responseType = 'blob';
        xhr.onload = () => {
          if (xhr.status === 200) {
            const blob = new Blob([xhr.response], { type: 'application/pdf' });
            const blobUrl = URL.createObjectURL(blob);
            this.previewDialog = {
              visible: true,
              title: file.name,
              width: '1200px',
              loading: false,
              height: 'calc(80vh - 48px - 56px - 8px)',
              content: `<div style="height:100%;">
                <iframe src="${blobUrl}" style="width: 100%; height: 100%; border: none;" ></iframe>
              </div>`,
            };
            // 在预览对话框关闭时释放blob URL
            this.$watch('previewDialog.visible', (newVal) => {
              if (!newVal) {
                URL.revokeObjectURL(blobUrl);
              }
            });
          } else {
            this.$message.error('PDF预览失败');
          }
        };
        xhr.onerror = () => {
          this.$message.error('PDF预览失败');
        };
        xhr.send();
        return;
      }

      // Office文档预览
      const officeExts = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
      if (officeExts.includes(fileExt)) {
        this.previewDialog = {
          visible: true,
          loading: true,
          title: file.name,
          width: '80%',
          height: '80vh',
          content: `<div style="height: 100%;">
            <iframe src="${previewUrl}" style="width: 100%; height: 100%; border: none;" onload="document.dispatchEvent(new CustomEvent('iframe-loaded'))"></iframe>
          </div>`,
        };

        // 监听iframe加载完成事件
        const handleIframeLoaded = () => {
          this.previewDialog.loading = false;
        };

        // 添加事件监听
        document.addEventListener('iframe-loaded', handleIframeLoaded, { once: true });

        // 在对话框关闭时移除事件监听
        this.$watch('previewDialog.visible', (newVal) => {
          if (!newVal) {
            document.removeEventListener('iframe-loaded', handleIframeLoaded);
          }
        });

        return;
      }

      // 文本文件预览
      const textExts = [
        'txt',
        'log',
        'json',
        'xml',
        'md',
        'csv',
        'url',
        'html',
        'js',
        'css',
        'yml',
        'yaml',
        'ini',
        'conf',
      ];
      if (fileType.includes('text') || textExts.includes(fileExt)) {
        fetch(fileUrl)
          .then((response) => response.text())
          .then((content) => {
            this.previewDialog = {
              visible: true,
              title: file.name,
              width: '80%',
              loading: false,
              content: `<div style="max-height: 80vh; overflow: auto; padding: 20px; background: #f5f7fa;">
                <pre style="white-space: pre-wrap; word-wrap: break-word; margin: 0; font-family: monospace;">${content}</pre>
              </div>`,
            };
          })
          .catch(() => {
            this.$message.error('文件加载失败');
          });
        return;
      }

      // 其他文件类型
      this.$message.warning('暂不支持该文件类型的预览');
    },
    // 处理iframe加载完成
    handleIframeLoaded() {
      // 设置loading为false
      this.previewDialog.loading = false;
    },
    // 渲染上传组件
    renderUpload() {
      const action = this.type === 'default' ? this.action : `${this.proxy}/api/sys/file/upload`;

      return (
        <div class="x-upload-default">
          <el-upload
            ref={this.refName}
            action={action}
            show-file-list={this.type === 'coos' ? this.coosShowFileList : this.showFileList}
            file-list={this.fileList}
            headers={this.headers}
            before-upload={this.beforeUpload}
            limit={this.limit}
            accept={this.accept}
            disabled={this.mode !== 'edit' || this.disabled}
            {...{
              props: {
                'on-success': (response, file, fileList) => {
                  if (this.type === 'default') {
                    this.fileList = fileList;
                    this.inputValue = this.fileList;
                  } else {
                    // 处理返回的文件列表
                    this.fileList = fileList
                      .map((item) => {
                        if (item.response) {
                          if (item.response.code === 200) {
                            return {
                              ...item.response.result,
                              name: item.response.result.originalFileName,
                              url: /^(http|https):\/\/.+/.test(item.response.result.fileUrl)
                                ? item.response.result.fileUrl
                                : (this.$appConfig.serveUrl || window.location.origin) + item.response.result.fileUrl,
                              size: item.response.result.fileSize,
                              type: item.response.result.fileType,
                            };
                          }
                          this.$message.warning(item.response.message);
                          return false;
                        }
                        return item;
                      })
                      .filter((item) => item);
                    this.inputValue = this.fileList.map((item) => item.fileId).join(',');
                  }
                  // 触发成功回调
                  if (this.onSuccess) {
                    this.onSuccess(response, file, this.fileList);
                  }
                },
                'on-exceed': (files, fileList) => {
                  const remainingCount = this.limit - fileList.length;
                  this.$message.warning({
                    message: `当前已上传${fileList.length}个文件，最多还能上传${remainingCount}个文件`,
                    duration: 3000,
                  });
                  this.$emit('on-exceed', files, fileList);
                },
                ...this.$attrs,
              },
              on: {
                ...this.$listeners,
              },
            }}
          >
            {this.renderDefault()}
          </el-upload>
        </div>
      );
    },
    // 渲染预览弹窗
    renderPreviewDialog() {
      return (
        <x-dialog
          vModel={this.previewDialog.visible}
          show-fullscreen
          append-to-body
          width={this.previewDialog.width}
          height={this.previewDialog.height}
          title={`预览【${this.previewDialog.title}】`}
        >
          <div
            domPropsInnerHTML={this.previewDialog.content}
            style="height:100%"
            vLoading={this.previewDialog.loading}
          />
        </x-dialog>
      );
    },
  },
  render() {
    const classes = ['x-upload', this.disabled || this.mode !== 'edit' ? 'x-upload-disabled' : ''];
    return (
      <div class={classes}>
        {this.renderFileList()}
        {this.renderUpload()}
        {this.renderTip()}
        {this.renderPreviewDialog()}
      </div>
    );
  },
};
</script>
<style lang="scss" scoped>
@import '../styles/index.scss';
.x-upload {
  &-files-item {
    width: 100%;
    border: 1px dashed #f0f0f0;
    padding: 3px 24px;
    color: #15224c;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &:not(:first-child) {
      margin-top: 10px;
    }
    .file {
      &-name {
        overflow: hidden;
        flex-grow: 1;
        display: flex;
        align-items: center;
        &-text {
          color: #15224c;
          font-size: 14px;
          line-height: 18px;
          display: inline-block;
          margin-left: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-weight: normal;
        }
      }
      &-size {
        width: 120px;
        font-weight: normal;
        padding: 0 12px;
      }
      &-actions {
        width: 110px;
        flex-shrink: 0;
        text-align: right;
      }
      &-other {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
  }
  .x-upload-files + .x-upload-default {
    margin-top: 10px;
  }
  &-empty {
    font-size: 14px;
    line-height: 20px;
    padding: 10px 0;
  }
  &-image {
    width: 148px;
    height: 148px;
    object-fit: cover;
    cursor: pointer;
  }
  &-disabled {
    ::v-deep {
      .el-upload,
      .el-upload-list__item-actions,
      .el-upload-list__item-status-label {
        display: none !important;
      }
    }
  }
}
</style>
