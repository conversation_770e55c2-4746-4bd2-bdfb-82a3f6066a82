<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 14:01:00
 * @FilePath: /vite-element-components/packages/toolbar/doc/basic.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <x-toolbar :title="title" :toolbars="toolbars" tooltip="我是文字提示">
      <template #titleLeft> 111 </template>
    </x-toolbar>
  </div>
</template>

<script>
export default {
  data() {
    return {
      title: '我是页标题',
      // 页控制栏配置
      toolbars: [
        {
          label: '新增',
          type: 'primary',
          iconName: 'add',
          onClick: () => {
            console.log('%c [ 触发新增事件 ]-143-「App」', 'font-size:13px; background:pink; color:#bf2c9f;');
          },
        },
        {
          label: '批量删除',
          type: 'primary',
          iconName: 'delete',
          onClick: () => {
            console.log('%c [ 触发批量删除事件 ]-143-「App」', 'font-size:13px; background:pink; color:#bf2c9f;');
          },
        },
      ],
    };
  },
};
</script>
