<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 14:05:33
 * @FilePath: /vite-element-components/packages/toolbar/doc/doc.md
 * @Description: Toolbar组件文档
-->
<script>
  import BasicVue from './basic.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      Preview
    }
  };
</script>

## Toolbar 工具栏

用于页面顶部展示标题和操作按钮的工具栏组件。

### 基础用法

通过`title`属性设置标题，`toolbars`属性配置操作按钮。

<basic-vue/>
<preview comp-name='toolbar' demo-name='basic'/>

#### tooltip属性说明

当需要提示信息时，该属性会触发Element UI的el-tooltip组件，支持通过`placement`配置提示位置（默认top），通过`effect`配置主题（默认dark）

### 属性

| 参数     | 说明                                                                                   | 类型                 | 默认值   |
| -------- | -------------------------------------------------------------------------------------- | -------------------- | -------- |
| title    | 标题文本                                                                               | string / number      | ''       |
| toolbars | 操作栏按钮配置数组                                                                     | Array                | []       |
| tooltip  | 标题旁的提示信息                                                                       | string / number      | ''       |
| gutter   | 内边距配置，[左内边距(支持数字/带px单位的字符串), 右内边距(支持数字/带px单位的字符串)] | Array<number/string> | [12, 15] |

### 插槽

| 插槽名    | 说明               | 作用域参数 |
| --------- | ------------------ | ---------- |
| titleLeft | 自定义标题后面内容 | -          |

### Toolbars 按钮配置

| 参数    | 说明                                                       | 类型     | 默认值    |
| ------- | ---------------------------------------------------------- | -------- | --------- |
| label   | 按钮文本                                                   | string   | -         |
| theme   | 按钮主题                                                   | string   | 'default' |
| icon    | 按钮图标名称（需使用Element UI图标，如："el-icon-search"） | string   | -         |
| onClick | 点击按钮时的回调函数                                       | Function | -         |
| ...     | 其他x-button组件的属性                                     | string   | -         |
