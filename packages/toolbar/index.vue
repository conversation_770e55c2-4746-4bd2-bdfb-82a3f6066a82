<template>
  <div class="x-toolbar" :style="`padding:${gutterPx.left} ${gutterPx.right};`">
    <div class="x-toolbar__title">
      <span class="x-toolbar__title-text">{{ title }}</span>
      <el-tooltip v-if="tooltip" :content="tooltip">
        <i class="el-icon-question x-toolbar__title-tooltip" />
      </el-tooltip>
      <span class="x-toolbar__title-left"><slot name="titleLeft"></slot></span>
    </div>
    <div class="x-toolbar__controls">
      <x-button v-for="btn in toolbars" :key="`btn-${btn.label}`" v-bind="btn" />
    </div>
  </div>
</template>

<script>
import XButton from '../button/index.vue';

/**
 * @component XToolbar
 * @description 工具栏组件，用于页面顶部展示标题和操作按钮
 * @example
 * <x-toolbar
 *   title="页面标题"
 *   :toolbars="[{label: '新增', type: 'primary', onClick: () => {}}]"
 *   tooltip="提示信息"
 * />
 */
export default {
  name: 'XToolbar',
  components: { XButton },
  props: {
    /**
     * @description 标题文本
     * @type {string|number}
     * @default ''
     */
    title: {
      type: [Number, String],
      default: '',
    },
    /**
     * @description 操作栏按钮配置数组
     * @type {Array<{label: string, theme?: string, iconName?: string, onClick: Function}>}
     * @default []
     */
    toolbars: {
      type: Array,
      default: () => [],
    },
    /**
     * @description 标题旁的提示信息
     * @type {string|number}
     * @default ''
     */
    tooltip: {
      type: [String, Number],
      default: '',
    },
    /**
     * @description 内边距配置，[左内边距, 右内边距]
     * @type {Array<number|string>}
     * @default [12, 15]
     */
    gutter: {
      type: Array,
      default: () => [12, 15],
    },
  },
  computed: {
    /**
     * @description 处理内边距样式，自动添加px单位
     * @returns {{left: string|null, right: string|null}}
     */
    gutterPx() {
      if (this.gutter) {
        const [left, right] = this.gutter;
        const isPixel = (value) => typeof value === 'string' && value.includes('px');
        return {
          left: isPixel(left) ? left : `${left}px`,
          right: isPixel(right) ? right : `${right}px`,
        };
      }
      return {
        left: null,
        right: null,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
@import '../styles/index.scss';
.x-toolbar {
  background: inherit;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  border-radius: 3px;
  &__title {
    flex-grow: 1;
    line-height: 32px;
    white-space: nowrap;
    display: flex;
    align-items: center;
    &-text {
      font-size: 16px;
      font-weight: 600;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    &-tooltip {
      margin-left: 8px;
      cursor: pointer;
    }
    &-left {
      margin-left: 8px;
    }
  }
  &__controls {
    .el-button:not(:first-child) {
      margin-left: 8px;
    }
  }
}
</style>
