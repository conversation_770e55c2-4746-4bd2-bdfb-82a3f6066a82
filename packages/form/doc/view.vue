<template>
  <div class="component-view">
    <x-form ref="form" v-model="formData" :form-item-span="8" mode="disabled" :form-config="formConfig" />
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      formData: {
        name: '1',
        lat: '2',
        下拉选择: '2',
        lng: '3',
        lngLat: [120, 31],
        时间区间: ['2023-07-21 00:00:00', '2023-07-22 23:59:59'],
      },
      formConfig: [
        {
          prop: 'name',
          label: '名称',
          component: 'input',
          formItemSpan: 8,
          isRequired: 1,
        },
        {
          prop: '下拉选择',
          label: '下拉选择',
          component: 'select',
          formItemSpan: 8,
          formInputConfig: {
            options: [
              { label: '选项一', value: '0' },
              { label: '选项一', value: '1' },
              { label: '选项二', value: '2' },
            ],
          },
          isRequired: 1,
        },
        {
          prop: 'lat',
          label: '数字类',
          formItemSpan: 8,
          component: 'inputNumber',
          isRequired: 1,
          formInputConfig: {
            min: 0,
            controls: false,
          },
        },
        {
          prop: 'address',
          label: '多行文本',
          component: 'input',
          formItemSpan: 24,
          formInputConfig: {
            type: 'textarea',
            maxlength: 100,
            showWordLimit: !!1,
          },
        },
        {
          prop: 'telephone',
          label: '校验规则',
          component: 'input',
          isRequired: 1,
          formItemSpan: 8,
          rules: [{ required: false, message: '自定义校验规则', trigger: 'blur' }],
        },
        { prop: 'lng', label: '名称', component: 'input' },
        {
          label: '普通时间',
          prop: 'date',
          component: 'date',
          formItemSpan: 8,
          formInputConfig: {
            type: 'date',
          },
        },
        {
          label: '数字输入',
          prop: 'inputNumber',
          component: 'inputNumber',
          formItemSpan: 8,
          formInputConfig: {
            digits: '2_2',
          },
        },
        {
          label: '单选框',
          prop: 'radio',
          component: 'radio',
          formItemSpan: 8,
          formInputConfig: {
            options: [
              {
                value: 1,
                label: '1',
              },
              {
                value: 2,
                label: '2',
              },
            ],
          },
        },
        {
          label: '复选框',
          prop: 'checkbox',
          component: 'checkbox',
          formItemSpan: 8,
          formInputConfig: {
            options: [
              {
                value: 1,
                label: '1',
              },
              {
                value: 2,
                label: '2',
              },
              {
                value: 3,
                label: '3',
              },
            ],
          },
        },
      ],
    };
  },
};
</script>
