<template>
  <div class="component-view">
    <div style="height: 800px; position: relative; overflow-y: auto">
      <el-radio-group v-model="mode">
        <el-radio label="edit">开启编辑模式</el-radio>
        <el-radio label="view">开启只读模式</el-radio>
        <el-radio label="disabled">开启禁用模式</el-radio>
      </el-radio-group>
      {{ formData }}
      <x-form
        ref="form"
        v-model="formData"
        title="我是自定义标题"
        actionsPosition="fixed"
        :form-item-span="12"
        :mode="mode"
        :form-config="formConfig"
        :actions="formActions"
      >
        <!--通过插槽插入其他的表单元素-->
        <!-- <el-col slot="formAppend" :span="24">
          <x-form-items
            v-model="formData"
            :mode="mode"
            :form-config="[
              {
                prop: '禁用输入框',
                label: '禁用输入框',
                component: 'input',
                isRequired: 1,
                infoText: '我是禁用的',
                formInputConfig: {
                  // disabled: true,
                  readonly: true,
                  slots: {
                    append: '万元',
                  },
                },
              },
            ]"
          />
        </el-col>
        <el-col slot="formAppend" :span="24"> <div>我是自定义的插入内容</div> </el-col> -->
      </x-form>
    </div>
  </div>
</template>

<script lang="jsx">
const cascaderOptions = [
  {
    value: 'zhinan',
    name: '设计',
    children: [
      {
        value: 'shejiyuanze',
        name: '设计原则设计原则设计原则设计原则设计原则设计原则设计原则设计原则设计原则设计原则',
        children: [
          {
            value: 'yizhi',
            name: '一致',
          },
          {
            value: 'fankui',
            name: '反馈',
          },
        ],
      },
      {
        value: 'daohang',
        name: '导航',
        children: [
          {
            value: 'cexiangdaohang',
            name: '侧向导航',
          },
          {
            value: 'dingbudaohang',
            name: '顶部导航',
          },
        ],
      },
    ],
  },
];
export default {
  data() {
    return {
      mode: 'edit',
      formData: {
        tableData: [],
        fileIds: '1904110950394576896,1909428534858301440,1905163410579505152,1907610930219376640',
        禁用输入框: '12112',
        普通输入框: '',
        多行文本框:
          '一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十',
        普通级联: 'yizhi',
        动态级联: '1-1.0-1.1',
        下拉搜索分页: [],
        远程搜索: '',
        普通下拉: [1, 2],
        普通日期: '2023-07-20',
        日期时间: '2023-07-20T07:36:02.809Z',
        日期区间: ['2023-07-20', '2023-08-26'],
        月份区间: ['2023-07', '2024-05'],
        时间区间: ['2023-07-21 00:00:00', '2023-07-22 23:59:59'],
        选择年份: '2024',
        选择月份: '2023-04',
        无控制数字: 999,
        单选框: 0,
        布尔单选框: false,
        字符串单选框: '0',
        复选框: ['0', '3'],
        上传组件: [
          {
            raw: {},
            lastModified: 1578453789000,
            name: '1.jpeg',
            size: 8560,
            type: 'image/jpeg',
            percent: 100,
            status: 'success',
            uploadTime: '2023-07-20',
            response: {
              url: 'https://tdesign.gtimg.com/site/source/figma-pc.png',
              XMLHttpRequest: {},
            },
            url: 'https://tdesign.gtimg.com/site/source/figma-pc.png',
          },
        ],
        自定义上传: [
          {
            raw: {},
            lastModified: 1578453789000,
            name: '1.jpeg',
            size: 8560,
            type: 'image/jpeg',
            percent: 0,
            status: 'waiting',
          },
        ],
      },
      lazyData: [
        {
          label: '一级 1',
          value: '1',
          isLeaf: false,
        },
        {
          label: '一级 2',
          value: '2',
          isLeaf: false,
        },
        {
          label: '一级 3',
          value: '3',
          isLeaf: true, // 叶子节点
        },
      ],
      // 模拟的子节点数据
      childrenData: {
        1: [
          {
            label: '二级 1-1',
            value: '1-1',
            isLeaf: false,
          },
          {
            label: '二级 1-2',
            value: '1-2',
            isLeaf: true,
          },
        ],
        2: [
          {
            label: '二级 2-1',
            value: '2-1',
            isLeaf: false,
          },
          {
            label: '二级 2-2',
            value: '2-2',
            isLeaf: true,
          },
        ],
        '1-1': [
          {
            label: '三级 1-1-1',
            value: '1-1-1',
            isLeaf: true,
          },
          {
            label: '三级 1-1-2',
            value: '1-1-2',
            isLeaf: true,
          },
        ],
        '2-1': [
          {
            label: '三级 2-1-1',
            value: '2-1-1',
            isLeaf: true,
          },
          {
            label: '三级 2-1-2',
            value: '2-1-2',
            isLeaf: true,
          },
        ],
      },
    };
  },
  computed: {
    formConfig() {
      return [
        {
          label: '开始日期',
          prop: '开始日期',
          component: 'date',
          isRequired: true,
          formInputConfig: {
            placeholder: '开始日期11',
            dateRangeEnd: this.formData.结束日期,
          },
        },
        {
          label: '结束日期',
          prop: '结束日期',
          component: 'date',
          isRequired: true,
          formInputConfig: {
            showCustom: true,
            placeholder: '结束日期22',
            dateRangeStart: this.formData.开始日期,
          },
        },
      ];
    },
    formActions() {
      return [
        {
          isReset: true,
        },
        {
          label: '确定',
          type: 'primary',
          isLoading: true,
          onClick: ({ endLoading }) => {
            this.$refs.form.validate((valid) => {
              if (!valid) return endLoading();
              console.log('%c [ 校验通过 ]-560-「BaseForm」', 'font-size:13px; background:pink; color:#bf2c9f;', 111);
              endLoading();
            });
          },
        },
        {
          label: '自定义上传',
          type: 'primary',
          isLoading: true,
          onClick: ({ endLoading }) => {
            endLoading();
            this.$refs.form.uploadFiles('customUpload');
          },
        },
        {
          label: '部分字段校验',
          type: 'primary',
          onClick: () => {
            this.$refs.form.validateField('aaa1', (errorMessage) => {
              console.log('%c [  ]-1023-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', errorMessage);
            });
          },
        },
      ];
    },
    tableColumns() {
      return [
        {
          prop: '输入框带插槽',
          label: '输入框带插槽',
          component: 'input',
          width: '180px',
          isRequired: 1,
          formInputConfig: {
            slots: {
              suffix: ({ row, rowIndex }) => (
                <i
                  class="el-icon-plus"
                  onClick={() => {
                    console.log(
                      '%c [ 选择行 ]-780-「update-form」',
                      'font-size:13px; background:pink; color:#bf2c9f;',
                      row,
                      rowIndex,
                    );
                  }}
                />
              ),
            },
          },
        },
        {
          prop: '普通输入框',
          label: '普通输入框',
          component: 'input',
          isRequired: 1,
          formInputConfig: {
            maxlength: 100,
          },
        },
        {
          prop: '数字输入框',
          label: '数字输入框',
          component: 'inputNumber',
          isRequired: 1,
        },
        {
          prop: '普通下拉',
          label: '普通下拉',
          component: 'select',
          formInputConfig: {
            options: [
              { value: 1, label: '一' },
              { value: 2, label: '二' },
            ],
            multiple: true,
          },
        },
        {
          label: '普通日期',
          prop: '普通日期',
          component: 'date',
          width: '180px',
          formInputConfig: {
            type: 'date',
          },
        },
      ];
    },
  },
  created() {
    this.selectOptions = [
      { label: '0000', value: 0 },
      { label: '张三', value: 1 },
      { label: '李四', value: 2 },
      { label: '王五', value: 3 },
    ];
  },
};
</script>
