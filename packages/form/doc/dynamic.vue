<template>
  <div class="component-view">
    {{ formData }}
    <x-form ref="formRef" v-model="formData" :form-config="formConfig" :actions="actions" label-width="120px" />
  </div>
</template>

<script>
export default {
  name: 'DynamicForm',
  data() {
    return {
      formData: {},
      formConfig: [
        {
          prop: 'userType',
          label: '用户类型',
          component: 'radio',
          isRequired: true,
          formInputConfig: {
            options: [
              { label: '企业用户', value: 'company' },
              { label: '个人用户', value: 'personal' },
            ],
          },
          events: {
            change: this.handleUserTypeChange,
          },
        },
        {
          prop: 'companyName',
          label: '企业名称',
          component: 'input',
          isRequired: true,
          isShow: false,
        },
        {
          prop: 'businessLicense',
          label: '营业执照',
          component: 'upload',
          isRequired: true,
          isShow: false,
          formInputConfig: {
            action: 'https://api.example.com/upload',
            accept: '.jpg,.png,.pdf',
            limit: 1,
          },
        },
        {
          prop: 'idCard',
          label: '身份证号',
          component: 'input',
          isRequired: true,
          isShow: false,
          rules: [
            {
              pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
              message: '请输入正确的身份证号',
              trigger: 'blur',
            },
          ],
        },
        {
          prop: 'address',
          label: '联系地址',
          component: 'input',
          isRequired: true,
          formInputConfig: {
            type: 'textarea',
          },
        },
        {
          prop: 'contactPhone',
          label: '联系电话',
          component: 'input',
          isRequired: true,
          rules: [
            {
              pattern: /^1[3-9]\d{9}$/,
              message: '请输入正确的手机号格式',
              trigger: 'blur',
            },
          ],
        },
        {
          prop: 'email',
          label: '邮箱',
          component: 'input',
          isRequired: true,
          formInputConfig: {
            type: 'email',
          },
          rules: [
            {
              type: 'email',
              message: '请输入正确的邮箱',
              trigger: 'blur',
            },
          ],
        },
      ],
      actions: [
        {
          label: '提交',
          type: 'primary',
          onClick: this.submitForm,
        },
        {
          label: '重置',
          onClick: this.resetForm,
        },
      ],
    };
  },
  methods: {
    handleUserTypeChange(value) {
      // 重置表单
      this.$refs.formRef.resetFields();
      this.formData.userType = value;

      // 根据用户类型显示不同的表单项
      this.formConfig.forEach((item) => {
        if (value === 'company') {
          if (item.prop === 'companyName' || item.prop === 'businessLicense') {
            item.isShow = true;
          } else if (item.prop === 'idCard') {
            item.isShow = false;
          }
        } else if (value === 'personal') {
          if (item.prop === 'companyName' || item.prop === 'businessLicense') {
            item.isShow = false;
          } else if (item.prop === 'idCard') {
            item.isShow = true;
          }
        }
      });
    },
    submitForm() {
      this.$refs.formRef.validate((valid, invalidFields) => {
        if (valid) {
          this.$message.success('表单验证通过！');
          console.log('表单数据：', this.formData);
        } else {
          console.log('表单验证失败：', invalidFields);
        }
      });
    },
    resetForm() {
      this.$refs.formRef.resetFields();
    },
  },
};
</script>
