<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-18 11:46:13
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-29 10:32:37
 * @FilePath: /vite-element-components/packages/form/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import ViewVue from './view.vue';
  import ValidateVue from './validate.vue';
  import DynamicVue from './dynamic.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      ViewVue,
      ValidateVue,
      DynamicVue,
      Preview
    }
  };
</script>

## Form 组件

使用 x-form-items 做的封装，内部使用栅格布局，可满足大部分表单使用场景。支持表单验证、重置、自定义布局等特性，提供灵活的配置选项来满足不同的业务需求。

### 基础用法

基础的表单提交。支持表单验证、重置功能，可以通过配置 formConfig 来定义表单项。
<basic-vue/>
<preview  comp-name='form' demo-name='basic'/>

### 查看模式用法

仅查看的表单，某些下拉框、单选等需自己处理数据。通过设置 mode 为 'disabled' 来启用查看模式。

<!-- <view-vue/>
<preview  comp-name='form' demo-name='view'/>

### 表单验证

展示了丰富的表单验证功能，包括必填项、长度限制、邮箱格式、手机号格式、密码确认等多种验证规则。支持自定义验证函数，可以实现复杂的验证逻辑。
<validate-vue/>
<preview  comp-name='form' demo-name='validate'/>

### 动态表单

根据用户的选择动态显示或隐藏表单项。本示例展示了如何根据用户类型（企业/个人）动态切换不同的表单项，实现表单的条件渲染。
<dynamic-vue/>
<preview  comp-name='form' demo-name='dynamic'/> -->

### Attributes

| 参数          | 说明                         | 类型          | 可选值             | 默认值 |
| ------------- | ---------------------------- | ------------- | ------------------ | ------ |
| value/v-model | 表单绑定的值对象             | object        | —                  | —      |
| formConfig    | 需要渲染的表单 formItem 配置 | object        | —                  | —      |
| mode          | 编辑模式                     | String        | edit/disabled/view | edit   |
| title         | 表单标题                     | object        | —                  | —      |
| labelWidth    | 表单标题宽度                 | string        | —                  | 130px  |
| css           | 自定义 form 容器样式         | string        | —                  | —      |
| labelPosition | label 显示位置               | string        | top/right          | right  |
| formItemSpan  | 表单项的栅格布局,多少列      | string/number | —                  | 1      |
| actions       | 表单操作栏的按钮组事件       | array         | —                  | —      |

### formConfig 同 x-form-items 组件配置

| 参数            | 说明                                                   | 类型           | 可选值                                                                                                         | 默认值 |
| --------------- | ------------------------------------------------------ | -------------- | -------------------------------------------------------------------------------------------------------------- | ------ |
| prop            | 必需，唯一标识 也是生成的数据的 key                    | String         | —                                                                                                              | —      |
| label           | 必需，表单项名称                                       | String         | —                                                                                                              | —      |
| component       | 必需，组件类型                                         | String         | input/inputNumber/textarea/select/cascader/radio/checkbox/date/selectTree/select/iconSelect/text/upload/custom | —      |
| formItemSpan    | 当前表单项占自定义栅格数                               | String/Number  | —                                                                                                              | —      |
| mode            | 编辑模式                                               | String         | edit/disabled/view                                                                                             | edit   |
| isRequired      | 字段是否必填                                           | Boolean/Number | false                                                                                                          | —      |
| ellipsis        | 回显内容是否溢出隐藏                                   | Boolean        | false                                                                                                          | true   |
| isMultiple      | 是否支持多选，仅对【下拉框】有效                       | Boolean        | true                                                                                                           | false  |
| filterable      | 是否支持静态搜索，仅对【下拉框】有效                   | Boolean        | true                                                                                                           | false  |
| isShow          | 字段是否在表单展示                                     | String         | false                                                                                                          | true   |
| infoText        | 表单域侧边提示文字                                     | String         | —                                                                                                              | —      |
| formInputConfig | 支持 element-eoss <所选表单控件>组件所有属性           | Object         | —                                                                                                              | —      |
| events          | 支持 element-eoss <所选表单控件>组件所有事件           | Object         | —                                                                                                              | —      |
| rules           | 表单验证规则，同 element-eoss Form 组件的 rules        | Object         | —                                                                                                              | —      |
| disabled        | 是否禁用                                               | Boolean        | —                                                                                                              | false  |
| readonly        | 是否只读                                               | Boolean        | —                                                                                                              | false  |
| ...             | FormItem 配置，支持 element-eoss FormItem 组件所有属性 | Object         | —                                                                                                              | —      |

### formInputConfig

| 参数         | 说明                                                              | 类型     | 可选值 | 默认值                                   |
| ------------ | ----------------------------------------------------------------- | -------- | ------ | ---------------------------------------- |
| options      | 选项，仅对 select/selectTree/select/cascader 有效                 | Array    | —      | —                                        |
| maxlength    | 字段数据长度，仅对【input】、【inputNumber】、【textarea】有效    | String   | —      | —                                        |
| clearable    | 是否允许清除该值,默认允许                                         | Boolean  | false  | true                                     |
| digits       | 9_2，9 位整数 2 位小数，仅对【inputNumber】有效                   | String   | —      | 9_2                                      |
| placeholder  | 输入框占位文本                                                    | String   | —      | —                                        |
| modifiers    | 修饰字段，仅对【inputNumber】有效                                 | Object   | —      | {negative:true,short:true,canEmpty:true} |
| remoteMethod | 懒加载方法，仅仅 superProps.lazy 为 true 生效                     | function | —      | —                                        |
| slots        | 支持 element 表单组件全部插槽,例：{append: h => {return '内容';}} | Object   | —      | —                                        |
| ...          | 其他原生表单组件属性                                              | —        | —      | —                                        |

### modifiers 修饰字段，仅对【inputNumber】有效

| 参数     | 说明                                    | 类型    | 可选值 | 默认值 |
| -------- | --------------------------------------- | ------- | ------ | ------ |
| negative | 允许输入负数，否则默认整数（含小数）    | Boolean | false  | true   |
| short    | 小数位不补零                            | Boolean | false  | true   |
| canEmpty | 可以为空 加上后为空字符串时不格式化为 0 | Boolean | false  | true   |

### Events

| 事件名称 | 说明                 | 回调参数                                                  |
| -------- | -------------------- | --------------------------------------------------------- |
| validate | 表单验证触发时的回调 | (prop: string, isValid: boolean, message: string) => void |
| change   | 表单值变化时的回调   | (value: any) => void                                      |
| reset    | 表单重置时的回调     | () => void                                                |

### Slots

| 插槽名称 | 说明                           |
| -------- | ------------------------------ |
| title    | 自定义表单标题区域的内容       |
| footer   | 自定义表单底部操作区域的内容   |
| default  | 自定义表单内容，会覆盖默认内容 |

### Methods

| 方法名        | 说明                                                                                                                                                                 | 参数                                                             |
| ------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------- |
| validate      | 对整个表单进行校验的方法，参数为一个回调函数。该回调函数会在校验结束后被调用，并传入两个参数：是否校验成功和未通过校验的字段。若不传入回调函数，则会返回一个 promise | (callback?: (valid: boolean, object: object) => void) => Promise |
| validateField | 对部分表单字段进行校验的方法                                                                                                                                         | (props: array \| string, callback: Function) => void             |
| resetFields   | 对整个表单进行重置，将所有字段值重置为初始值并移除校验结果                                                                                                           | () => void                                                       |
| clearValidate | 移除表单项的校验结果。传入待移除的表单项的 prop 属性或者 prop 组成的数组，如不传则移除整个表单的校验结果                                                             | (props?: array \| string) => void                                |
