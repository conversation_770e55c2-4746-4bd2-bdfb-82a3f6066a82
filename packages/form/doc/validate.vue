<template>
  <div class="component-view">
    <x-form
      ref="formRef"
      v-model="formData"
      :form-config="formConfig"
      :actions="actions"
      label-width="120px"
      :form-item-span="8"
    />
  </div>
</template>

<script>
export default {
  name: 'ValidateForm',
  data() {
    return {
      formData: {
        name: '',
        age: '',
        email: '',
        phone: '',
        password: '',
        confirmPassword: '',
      },
      formConfig: [
        {
          prop: 'name',
          label: '用户名',
          component: 'input',
          isRequired: true,
          rules: [
            {
              min: 3,
              max: 20,
              message: '长度在 3 到 20 个字符',
              trigger: 'blur',
            },
          ],
        },
        {
          prop: 'age',
          label: '年龄',
          component: 'inputNumber',
          isRequired: true,
          formInputConfig: {
            min: 0,
            max: 120,
          },
        },
        {
          prop: 'email',
          label: '邮箱',
          component: 'input',
          isRequired: true,
          formInputConfig: {
            type: 'email',
          },
          rules: [
            {
              type: 'email',
              message: '请输入正确的邮箱格式',
              trigger: ['blur', 'change'],
            },
          ],
        },
        {
          prop: 'phone',
          label: '手机号',
          component: 'input',
          isRequired: true,
          rules: [
            {
              pattern: /^1[3-9]\d{9}$/,
              message: '请输入正确的手机号格式',
              trigger: 'blur',
            },
          ],
        },
        {
          prop: 'password',
          label: '密码',
          component: 'input',
          isRequired: true,
          formInputConfig: {
            type: 'password',
          },
          rules: [
            {
              min: 6,
              message: '密码长度不能小于6位',
              trigger: 'blur',
            },
          ],
        },
        {
          prop: 'confirmPassword',
          label: '确认密码',
          component: 'input',
          isRequired: true,
          formInputConfig: {
            type: 'password',
          },
          rules: [
            {
              validator: (rule, value, callback) => {
                if (value !== this.formData.password) {
                  callback(new Error('两次输入的密码不一致'));
                } else {
                  callback();
                }
              },
              trigger: 'blur',
            },
          ],
        },
      ],
      actions: [
        {
          label: '提交',
          type: 'primary',
          onClick: this.submitForm,
        },
        {
          label: '重置',
          onClick: this.resetForm,
        },
      ],
    };
  },
  methods: {
    submitForm() {
      this.$refs.formRef.validate((valid, invalidFields) => {
        if (valid) {
          this.$message.success('表单验证通过！');
          console.log('表单数据：', this.formData);
        } else {
          console.log('表单验证失败：', invalidFields);
        }
      });
    },
    resetForm() {
      this.$refs.formRef.resetFields();
    },
  },
};
</script>
