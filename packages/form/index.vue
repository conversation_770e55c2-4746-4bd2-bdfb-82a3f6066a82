<template>
  <div :class="{ 'x-form': true, 'x-form__fixed': actionsPosition === 'fixed' }">
    <el-form
      ref="baseForm"
      :model="formData"
      :label-width="labelWidth"
      :label-position="labelPosition"
      :size="size"
      v-bind="$attrs"
    >
      <div v-if="title" class="x-form-title">
        {{ title }} <span v-if="titleTips">{{ titleTips }}</span>
      </div>
      <x-form-items
        ref="designForm"
        v-model="formData"
        :label-width="labelWidth"
        :form-item-span="formItemSpan"
        :form-config="formItems"
        :hide-label="hideLabel"
        :disabled="mode === 'disabled'"
        :mode="mode"
      />
      <slot class="x-form-footer" name="formAppend"></slot>
    </el-form>
    <div
      v-if="($slots && $slots.footer) || (actions.length > 0 && mode === 'edit')"
      :style="{
        'margin-left': actionsPosition === 'fixed' ? '' : labelWidth,
      }"
      :class="{
        'x-form-footer': true,
        'x-form-footer__fixed': actionsPosition === 'fixed',
      }"
    >
      <slot name="footer"></slot>
      <template v-if="actions.length > 0">
        <x-button
          v-for="(btn, index) in actions"
          :key="`btn-${index}`"
          v-bind="{
            ...btn,
            type: btn.isReset ? 'default' : btn.type || 'primary',
            label: btn.isReset ? '重置' : btn.label,
            onClick: btn.isReset ? () => resetFields() : btn.onClick,
          }"
        />
      </template>
    </div>
  </div>
</template>

<script>
import XFormItems from '../formItems/index.vue';
import XButton from '../button/index.vue';
import { getInitialValueByComponent } from '../shared/utils';

/**
 * @description 表单组件，基于 Element UI 的 Form 组件进行封装，支持表单项的动态配置、表单验证、重置等功能
 * @module XForm
 */
export default {
  name: 'XForm',
  components: { XFormItems, XButton },
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    /**
     * @description 表单标题
     * @default ''
     */
    title: {
      type: String,
      default: '',
    },
    /**
     * @description 表单标题提示
     * @default ''
     */
    titleTips: {
      type: String,
      default: '',
    },
    /**
     * @description 表单项配置数组，用于动态生成表单项
     * @property {Array} formConfig
     * @default []
     */
    formConfig: {
      type: Array,
      default: () => [],
    },
    /**
     * @description 表单数据对象，用于双向绑定
     * @property {Object} value
     * @default {}
     */
    value: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 表单域标签的宽度
     * @property {String} labelWidth
     * @default '130px'
     */
    labelWidth: {
      type: String,
      default: '130px',
    },
    /**
     * @description 表单域标签的位置，可选值：'left'|'right'|'top'
     * @property {String} labelPosition
     * @default 'right'
     */
    labelPosition: {
      type: [String],
      default: 'right',
    },
    /**
     * @description 表单项的栅格布局占比
     * @property {String|Number} formItemSpan
     * @default 24
     */
    formItemSpan: {
      type: [String, Number],
      default: 24,
      validator(value) {
        const num = Number(value);
        return num > 0 && num <= 24;
      },
    },
    /**
     * @description 表单操作栏的按钮组配置
     * @property {Array} actions
     * @default []
     */
    actions: {
      type: Array,
      default: () => [],
    },
    /**
     * @description 操作栏位置，可选值：'relative'|'fixed'
     * @property {String} actionsPosition
     * @default 'relative'
     */
    actionsPosition: {
      type: String,
      default: 'relative',
    },
    /**
     * @description 编辑模式
     * @property {String} mode
     * @default 'edit'
     */
    mode: {
      type: String,
      default: 'edit',
    },
    /**
     * @description 是否禁用整个表单
     * @property {Boolean} disabled
     * @default false
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 是否隐藏表单项的标签
     * @property {Boolean} hideLabel
     * @default false
     */
    hideLabel: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 表单组件的尺寸
     * @property {String} size
     * @default ''
     */
    size: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      initialUserValues: {}, // 记录用户传入的初始默认值
    };
  },
  computed: {
    /**
     * @description 过滤后的表单项配置，根据 isShow 或 isShowForm 属性控制显示
     * @returns {Array}
     */
    formItems: {
      get() {
        return this.formConfig?.filter((item) => {
          if ('isShow' in item || 'isShowForm' in item) {
            return item.isShow || item.isShowForm;
          }
          return item;
        });
      },
      set(val) {
        this.$emit('update:formConfig', val);
      },
    },
    /**
     * @description 表单数据的计算属性，实现双向绑定
     * @returns {Object}
     */
    formData: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('update:value', value);
      },
    },
  },
  created() {
    // 记录用户传入的初始默认值（用于重置时保持用户的默认值）
    if (this.value && typeof this.value === 'object') {
      this.initialUserValues = { ...this.value };
    }
  },
  methods: {
    /**
     * @description 初始化表单数据
     * @param {Object} [initData={}] - 可传入自定义初始数据
     */
    init(initData = {}) {
      this.resetFields(initData);
    },
    /**
     * @description 表单验证方法
     * @param {Function} cb - 验证回调函数
     */
    validate(cb) {
      if (!this.$refs.baseForm) {
        console.warn('表单引用不存在，验证失败');
        // eslint-disable-next-line no-unused-expressions
        cb && cb(false);
        return;
      }
      this.$refs.baseForm.validate((valid) => {
        // eslint-disable-next-line no-unused-expressions
        cb && cb(valid);
      });
    },
    /**
     * @description 重置表单数据
     * @param {Object} [initData={}] - 可传入自定义初始数据
     * 使用统一的初始值逻辑，确保与 formItems 组件保持一致
     */
    resetFields(initData) {
      // 生成重置数据，优先使用用户的初始默认值
      const resetData = {};
      if (!initData) {
        this.formConfig.forEach((item) => {
          const { component, prop, formInputConfig } = item;
          if (prop) {
            // 优先使用用户传入的初始默认值，如果没有则使用组件默认值
            if (Object.prototype.hasOwnProperty.call(this.initialUserValues, prop)) {
              resetData[prop] = this.initialUserValues[prop];
            } else {
              resetData[prop] = getInitialValueByComponent(component, formInputConfig);
            }
          }
        });
        this.$nextTick(() => {
          this.formData = { ...resetData };
          // 清除校验样式
          this.$refs.baseForm.clearValidate();
        });
      } else {
        this.formConfig.forEach((item) => {
          const { component, prop, formInputConfig } = item;
          if (prop) {
            resetData[prop] = getInitialValueByComponent(component, formInputConfig);
          }
        });

        this.$nextTick(() => {
          // 更新表单数据为重置值，initData 可以覆盖重置值
          this.formData = { ...resetData, ...initData };
          // 清除校验样式
          this.$refs.baseForm.clearValidate();
        });
      }
    },

    /**
     * @description 移除表单项的校验结果
     * @param {String|Array} props - 要移除校验结果的表单项的 prop 或 prop 数组
     */
    clearValidate(props) {
      this.$refs?.baseForm?.clearValidate(props);
    },
    /**
     * @description 校验表单项的部分字段
     * @param {String|Array} props - 要移除校验结果的表单项的 prop 或 prop 数组
     * @param {function} errorMessage - 校验失败时的错误回调
     */
    validateField(props, errorMessage) {
      this.$refs?.baseForm?.validateField(props, errorMessage);
    },
    /**
     * @description 获取上传组件的文件列表
     * @param {String} refName - 上传组件的引用名
     * @returns {Array} 文件列表
     */
    uploadFiles(refName) {
      return this.$refs.designForm?.uploadFiles(refName);
    },
  },
};
</script>

<style lang="scss">
.x-form {
  width: 100%;
  border-radius: 3px;

  &-title {
    font-size: 14px;
    line-height: 20px;
    padding-bottom: 20px;
    position: relative;
    &::before {
      display: inline-block;
      content: '';
      width: 4px;
      height: 20px;
      border-radius: 2px;
      background-color: var(--brand-6, #0f45ea);
      margin-right: 6px;
      vertical-align: top;
    }
  }
  &-footer {
    .el-button:not(:first-child) {
      margin-left: 16px;
    }
    &__fixed {
      width: 100%;
      border-top: 1px solid #eee;
      background-color: #fff;
      padding: 12px 0;
      position: sticky;
      bottom: -1px;
      left: 0;
      z-index: 999;
    }
  }
  .el-form {
    overflow: hidden !important;
  }
  &__fixed {
    flex-grow: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    .el-form {
      flex-grow: 1;
    }
  }
}
</style>
