// 全局公共样式
.x-button.el-button,
.el-button {
  border-radius: 6px !important;
}
.el-input.is-disabled .el-input__inner,
.el-range-editor.is-disabled .el-range-separator {
  color: rgba(0, 0, 0, 0.4) !important;
}
.el-range-editor.is-disabled,
.el-range-editor.is-disabled .el-input__inner,
.el-input.is-disabled .el-input__inner,
.el-range-editor.is-disabled input,
.el-textarea.is-disabled .el-textarea__inner {
  background: #ffffff !important;
  color: var(--form-item-label-color) !important;
}
.el-checkbox__input.is-disabled + span.el-checkbox__label,
.el-radio__input.is-disabled + span.el-radio__label {
  color: var(--form-item-label-color) !important;
}
