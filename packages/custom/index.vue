<!--
 * @Description: 自定义内容组件 - 用于渲染自定义内容，支持函数、对象、字符串等多种渲染方式
 * @Version: 1.0.0
 * @Author: Linyer
 * @Date: 2024-05-18 14:52:40
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-18 15:53:29
-->

<script lang="jsx">
/**
 * @component XCustom
 * @description 自定义内容组件，支持多种渲染方式的通用容器组件
 */
export default {
  name: 'XCustom',
  title: '自定义内容组件',
  props: {
    /**
     * @description 渲染内容，支持多种类型
     * @type {Array|String|Object|Number|Function}
     * @default null
     * @example
     * // 函数方式
     * :render="() => <div>自定义内容</div>"
     * // 对象方式（VNode）
     * :render="h('div', '自定义内容')"
     * // 字符串方式
     * :render="'自定义内容'"
     */
    render: {
      type: [Array, String, Object, Number, Function],
      default: null,
    },
    /**
     * 编辑模式
     * @type {String}
     * @default 'edit'
     */
    mode: {
      type: String,
      default: 'edit',
    },
    /**
     * 是否禁用
     * @type {boolean}
     * @default false
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * 值类型
     */
    valueType: {
      type: String,
      default: 'array',
    },
    /**
     * @description 可编辑表格使用，绑定值
     * @type {Object}
     * @default ''
     */
    context: {
      type: Object,
      default: () => ({}),
    },
  },

  /**
   * @description 渲染函数
   * @param {Function} h - createElement 函数
   * @returns {VNode} 渲染的虚拟节点
   */
  render(h) {
    // 处理禁用和只读状态的样式
    const className = {
      'is-disabled': this.mode === 'disabled' || this.disabled,
      'is-readonly': this.mode === 'view',
    };

    // 根据render属性的类型进行不同的渲染处理
    let content;
    if (typeof this.render === 'function') {
      // 如果是函数，执行函数获取渲染内容
      content = this.render(this.context);
    } else if (typeof this.render === 'object') {
      // 如果是对象（VNode），直接使用
      content = this.render;
    } else {
      // 其他类型（字符串、数字、数组等），包装在div中
      content = h('div', this.render);
    }

    // 返回最终的渲染结果
    return h(
      'div',
      {
        class: ['x-custom', className],
        attrs: {
          'data-disabled': this.mode === 'disabled',
          'data-readonly': this.mode === 'view',
        },
      },
      [content],
    );
  },
};
</script>
