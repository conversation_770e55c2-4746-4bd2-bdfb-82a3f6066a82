<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-18 11:46:13
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-25 15:31:52
 * @FilePath: /vite-element-components/packages/custom/doc/basic.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <p>
      普通自定义组件：
      <x-custom :render="renderFunc" />
    </p>
    <p>
      详情自定义组件：
      <x-custom mode="view" :render="renderObject" />
    </p>
    <p>
      禁用自定义组件：
      <x-custom disabled :render="renderFunc" />
    </p>
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      inputValue: '',
      renderObject: (
        <el-button
          onClick={() => {
            console.log(111);
          }}
        >
          对象自定义组件按钮
        </el-button>
      ),
    };
  },
  computed: {},
  methods: {
    renderFunc() {
      return [<el-button>自定义按钮</el-button>, <el-button>1自定义组件按钮</el-button>];
    },
  },
};
</script>
