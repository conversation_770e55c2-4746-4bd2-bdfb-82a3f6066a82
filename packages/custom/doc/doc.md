<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:43:59
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-26 17:54:00
 * @FilePath: /vite-element-components/packages/custom/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,Preview
    }
  };
</script>

## Custom 组件

自定义内容组件，用于渲染自定义内容，支持函数、对象、字符串等多种渲染方式。该组件提供了一个灵活的容器，可以根据需要渲染不同类型的内容。

### 基础用法

组件接收一个 render 函数，内可以传入渲染的 html 或 vNode。支持多种渲染方式：

- 函数方式：通过返回 JSX 或 VNode 渲染内容
- 对象方式：直接传入 VNode 对象
- 字符串方式：直接渲染文本内容

<basic-vue/>
<preview  comp-name='custom' demo-name='basic'/>

### Attributes

| 参数     | 说明                 | 类型                                | 可选值                 | 默认值 |
| -------- | -------------------- | ----------------------------------- | ---------------------- | ------ |
| render   | 自定义组件的渲染内容 | Function / Object / String / Number | —                      | null   |
| mode     | 编辑模式             | String                              | edit / view / disabled | edit   |
| disabled | 是否禁用状态         | Boolean                             | —                      | false  |
| context  | 可编辑表格绑定值     | Object                              | —                      | {}     |

### Events

组件本身不直接触发事件，但可以通过render函数中的内容触发自定义事件。

### Methods

组件本身不提供公共方法，渲染内容的方法可以在render函数中定义和使用。
