<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:07
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-03 17:18:33
 * @FilePath: /vite-element-components/packages/pagination/doc/layout.vue
 * @Description: 不同布局配置的分页示例
-->
<template>
  <div class="component-view">
    <div class="pagination-item">
      <p>完整功能</p>
      <x-pagination
        :total="100"
        :page.sync="page1"
        :page-size="10"
        layout="sizes, prev, pager, next, jumper, ->, total"
        @pagination="handlePagination"
      />
    </div>

    <div class="pagination-item">
      <p>简单模式</p>
      <x-pagination
        :total="100"
        :page.sync="page2"
        :page-size="10"
        layout="prev, pager, next"
        @pagination="handlePagination"
      />
    </div>

    <div class="pagination-item">
      <p>带总数的模式</p>
      <x-pagination
        :total="100"
        :page.sync="page3"
        :page-size="10"
        layout="total, prev, pager, next"
        @pagination="handlePagination"
      />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      page1: 1,
      page2: 1,
      page3: 1,
    };
  },
  methods: {
    handlePagination(pagination) {
      console.log('页码变化：', pagination);
    },
  },
};
</script>

<style scoped>
.pagination-item {
  margin-bottom: 20px;
}
.pagination-item p {
  margin-bottom: 10px;
  color: #666;
}
</style>
