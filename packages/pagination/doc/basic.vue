<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:07
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-03 17:18:33
 * @FilePath: /vite-element-components/packages/pagination/doc/basic.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <x-pagination
      :total="pagination.total"
      :page.sync="pagination.page"
      :page-size="pagination.pageSize"
      :page-sizes="pagination.pageSizes"
      @pagination="getPagination"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      pagination: {
        total: 100, // 总条数
        page: 1, // 当前页
        pageSize: 10, // 每页条数
        pageSizes: [10, 20, 50, 100], // 默认
      },
    };
  },
  methods: {
    getPagination(pagination) {
      console.log(`当前第${pagination.page}页，每页${pagination.pageSize}条。`, pagination);
      // 请求接口
    },
  },
};
</script>
