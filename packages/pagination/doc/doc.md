<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:07
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-03 15:41:34
 * @FilePath: /vite-element-components/packages/pagination/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import LayoutVue from './layout.vue';
  import StyleVue from './style.vue';
  import FeatureVue from './feature.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      LayoutVue,
      StyleVue,
      FeatureVue,
      Preview
    }
  };
</script>

## Pagination 组件

基于 Element UI 的分页组件，提供了简化的分页功能和自动滚动特性。通过简单的配置即可实现分页展示和数据加载。

### 基础用法

简化了原 el-pagination 组件的使用，让开发者只需关注几个必需的参数：total（总条数）、page（当前页）、pageSize（每页显示条数）和pagination（分页的方法）。

<basic-vue/>
<preview comp-name='pagination' demo-name='basic'/>

### 不同布局

通过设置 `layout` 属性，可以实现不同的分页布局。组件支持的布局元素包括：`sizes`（每页显示个数选择器）、`prev`（上一页）、`pager`（页码）、`next`（下一页）、`jumper`（跳页）、`->`（右对齐）、`total`（总条目数）。

<layout-vue/>
<preview comp-name='pagination' demo-name='layout'/>

### 自定义样式

可以通过 `background` 属性设置分页按钮的背景色，也可以通过自定义 CSS 变量来修改分页组件的样式。

<style-vue/>
<preview comp-name='pagination' demo-name='style'/>

### 功能配置

展示了自动滚动、动态隐藏等高级功能的使用方法。

<feature-vue/>
<preview comp-name='pagination' demo-name='feature'/>

### Attributes

| 参数       | 说明                         | 类型     | 可选值                                            | 默认值                                 |
| ---------- | ---------------------------- | -------- | ------------------------------------------------- | -------------------------------------- |
| total      | 总条数                       | number   | —                                                 | —                                      |
| page       | 当前页码                     | number   | —                                                 | 1                                      |
| pageSize   | 每页显示条目个数             | number   | —                                                 | 10                                     |
| pageSizes  | 每页显示个数选择器的选项设置 | array    | —                                                 | [10, 20, 50, 100]                      |
| layout     | 组件布局，子组件名用逗号分隔 | string   | sizes, prev, pager, next, jumper, ->, total, slot | 'prev, pager, next, jumper, ->, total' |
| background | 是否为分页按钮添加背景色     | boolean  | false/true                                        | true                                   |
| autoScroll | 页码改变时是否自动滚动到顶部 | boolean  | false/true                                        | true                                   |
| hidden     | 是否隐藏分页组件             | boolean  | false/true                                        | false                                  |
| onChange   | 页码改变时触发               | function | —                                                 | —                                      |

### Events

| 事件名         | 说明                         | 回调参数                               |
| -------------- | ---------------------------- | -------------------------------------- |
| pagination     | 当前页码或每页条数改变时触发 | { page: 当前页码, pageSize: 每页条数 } |
| size-change    | page-size 改变时触发         | number（新的每页条数）                 |
| current-change | pageNo 改变时触发            | number（新的页码）                     |

### 注意事项

1. 组件使用了 `.sync` 修饰符实现双向绑定，确保父组件能够正确接收到页码变化。
2. `autoScroll` 属性开启时，切换页码会自动滚动到页面顶部，适用于长列表场景。
3. `layout` 属性可以灵活配置分页组件的布局，通过逗号分隔的方式组合不同的功能模块。
4. 当数据总量（total）为 0 时，分页器会自动隐藏。
5. `hidden` 属性可用于手动控制分页组件的显示和隐藏，适用于需要根据业务逻辑动态控制分页器显示状态的场景。
