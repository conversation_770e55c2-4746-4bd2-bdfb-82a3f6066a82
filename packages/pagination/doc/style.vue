<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:07
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-03 17:18:33
 * @FilePath: /vite-element-components/packages/pagination/doc/style.vue
 * @Description: 带背景色和自定义样式的分页示例
-->
<template>
  <div class="component-view">
    <div class="pagination-item">
      <p>带背景色的分页</p>
      <x-pagination :total="100" :page.sync="page1" :page-size="10" background @pagination="handlePagination" />
    </div>

    <div class="pagination-item">
      <p>自定义样式的分页</p>
      <x-pagination
        :total="100"
        :page.sync="page2"
        :page-size="10"
        class="custom-pagination"
        @pagination="handlePagination"
      />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      page1: 1,
      page2: 1,
    };
  },
  methods: {
    handlePagination(pagination) {
      console.log('页码变化：', pagination);
    },
  },
};
</script>

<style scoped>
.pagination-item {
  margin-bottom: 20px;
}
.pagination-item p {
  margin-bottom: 10px;
  color: #666;
}
.custom-pagination {
  --el-pagination-button-color: #0f45ea;
  --el-pagination-hover-color: #0f45ea;
  --el-pagination-active-bg-color: #0f45ea;
  --el-pagination-active-color: #fff;
}
</style>
