<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:07
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-03 17:18:33
 * @FilePath: /vite-element-components/packages/pagination/doc/feature.vue
 * @Description: 隐藏功能和自动滚动的分页示例
-->
<template>
  <div class="component-view">
    <div class="pagination-item">
      <p>自动滚动功能（点击页码会自动滚动到顶部）</p>
      <x-pagination
        :total="100"
        :page.sync="page1"
        :page-size="10"
        :auto-scroll="true"
        @pagination="handlePagination"
      />
    </div>

    <div class="pagination-item">
      <p>动态隐藏分页（total为0时自动隐藏）</p>
      <x-pagination :total="total" :page.sync="page2" :page-size="10" @pagination="handlePagination" />
      <el-button @click="toggleTotal" style="margin-top: 10px">切换数据总数</el-button>
    </div>

    <div class="pagination-item">
      <p>手动控制隐藏</p>
      <x-pagination :total="100" :page.sync="page3" :page-size="10" :hidden="isHidden" @pagination="handlePagination" />
      <el-button @click="toggleHidden" style="margin-top: 10px">{{ isHidden ? '显示' : '隐藏' }}分页</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      page1: 1,
      page2: 1,
      page3: 1,
      total: 0,
      isHidden: false,
    };
  },
  methods: {
    handlePagination(pagination) {
      console.log('页码变化：', pagination);
    },
    toggleTotal() {
      this.total = this.total === 0 ? 100 : 0;
    },
    toggleHidden() {
      this.isHidden = !this.isHidden;
    },
  },
};
</script>

<style scoped>
.pagination-item {
  margin-bottom: 20px;
}
.pagination-item p {
  margin-bottom: 10px;
  color: #666;
}
</style>
