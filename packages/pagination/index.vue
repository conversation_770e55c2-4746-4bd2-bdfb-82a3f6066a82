<script lang="jsx">
import { scrollTo } from '../../src/utils/scroll-to';

/**
 * @component XPagination
 * @description 基于Element UI的分页组件，提供了简化的分页功能和自动滚动特性
 */
export default {
  name: 'XPagination',
  model: {
    prop: 'page',
    event: 'update:page',
  },
  props: {
    /**
     * 总条目数
     * @type {number}
     * @required
     */
    total: {
      required: true,
      type: Number,
    },
    /**
     * 当前页码
     * @type {number}
     * @default 1
     */
    page: {
      type: Number,
      default: 1,
    },
    /**
     * 每页显示条目个数
     * @type {number}
     * @default 10
     */
    pageSize: {
      type: Number,
      default: 10,
    },
    /**
     * 每页显示个数选择器的选项设置
     * @type {number[]}
     * @default [10, 20, 50, 100]
     */
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 50, 100];
      },
    },
    /**
     * 组件布局配置，子组件名用逗号分隔
     * @type {string}
     * @default 'prev, pager, next, jumper, ->, total'
     */
    layout: {
      type: String,
      default: 'total, sizes, ->, prev, pager, next, jumper',
    },
    /**
     * 是否为分页按钮添加背景色
     * @type {boolean}
     * @default true
     */
    background: {
      type: Boolean,
      default: true,
    },
    /**
     * 页码改变时是否自动滚动到顶部
     * @type {boolean}
     * @default true
     */
    autoScroll: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否隐藏分页组件
     * @type {boolean}
     * @default false
     */
    hidden: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    current: {
      get() {
        return this.page;
      },
      set(val) {
        this.$emit('update:page', val);
      },
    },
    pageLimit: {
      get() {
        return this.pageSize;
      },
      set(val) {
        this.$emit('update:pageSize', val);
      },
    },
    setLayout: {
      get() {
        const width = document.body.clientWidth;
        if (width < 500) {
          return 'simple';
        }
        return this.layout;
      },
      set(val) {
        this.$emit('update:layout', val);
      },
    },
  },
  methods: {
    /**
     * 处理每页显示条数变化
     * @param {number} pageSize - 新的每页条数
     * @emits pagination
     */
    handleSizeChange(pageSize) {
      this.current = 1;
      this.$emit('change', { pageNo: this.current, pageSize });
      if (this.autoScroll) {
        scrollTo(0, 500);
      }
    },
    /**
     * 处理当前页码变化
     * @param {number} pageNo - 新的页码
     * @emits pagination
     */
    handleCurrentChange(pageNo) {
      this.$emit('change', { pageNo, pageSize: this.pageLimit });
      if (this.autoScroll) {
        scrollTo(0, 500);
      }
    },
  },
  render() {
    return (
      <div class={['x-pagination', { hidden: this.hidden }]}>
        <el-pagination
          current-page={this.current}
          background={this.background}
          page-size={this.pageLimit}
          page-sizes={this.pageSizes}
          total={this.total}
          layout={this.setLayout}
          {...{
            props: this.$attrs,
            on: {
              ...this.$listeners,
              'size-change': (val) => {
                this.pageLimit = val;
                this.handleSizeChange(val);
              },
              'current-change': (val) => {
                this.current = val;
                this.handleCurrentChange(val);
              },
            },
          }}
        />
      </div>
    );
  },
};
</script>

<style lang="scss" scoped>
@import '../styles/index.scss';
.x-pagination {
  padding: 15px 0;
  &.hidden {
    display: none;
  }
}
</style>
