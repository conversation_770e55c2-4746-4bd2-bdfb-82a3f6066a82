<template>
  <div :class="['x-search-table', $slots.searchTableLeft ? 'x-search-table__flex' : '']">
    <div v-if="$slots.searchTableLeft" class="x-search-table__left">
      <slot name="searchTableLeft"></slot>
    </div>
    <div class="x-search-table__content" :style="`padding:${gutterPx.left} ${gutterPx.right};`">
      <toolbar v-if="title || toolbars.length > 0" :title="title" :toolbars="toolbars" v-bind="titleAttr">
        <div slot="titleLeft"><slot name="titleLeft"></slot></div>
      </toolbar>
      <!--以tabs方式展示---->
      <el-tabs
        class="x-search-table__tabs"
        v-if="tabArray"
        v-model="tabActiveIndex"
        v-bind="tabAttr.tabs"
        @tab-click="handleTabsChange"
      >
        <el-tab-pane
          v-for="tab in tabsMap"
          :key="`searchTabs${tab.label}`"
          :label="tab.label"
          :name="tab.value"
          v-bind="tabAttr.tabPanel"
        >
        </el-tab-pane>
      </el-tabs>
      <x-search
        v-if="searchFormConfig.length > 0"
        ref="search"
        v-model="searchForm"
        :label-width="labelWidth"
        :search-item-span="searchItemSpan"
        :search-config="searchFormConfig"
        v-bind="searchAttrs"
        @updateParams="(params) => handleSearchBtnClick(params)"
        @resetSearch="(formData) => handleResetSearch({ ...formData })"
      />
      <!--表格头部插槽-->
      <slot name="tableTop"></slot>
      <div v-show="showTable" class="x-search-table__wrapper">
        <!--表格左侧插槽-->
        <slot name="tableLeft"></slot>
        <x-table
          ref="baseTable"
          class="table"
          :draggable="draggable"
          :table-data="tableData"
          :table-options="tableOptions"
          :columns="tableColumnsConfig"
          :selection-change="selectionChange"
          :selection.sync="selectionArray"
          v-bind="{
            loading: loading,
            paginationConfig: finallyPaginationConfig,
            ...tableAttrs,
            height: tableAttrs.height ? tableAttrs.height : isSingle ? '100%' : '',
            onChange: (pageInfo) => handlePaginationChange(pageInfo),
          }"
          v-on="$listeners"
        />
        <!--表格右侧插槽-->
        <slot name="tableRight"></slot>
      </div>
    </div>
  </div>
</template>

<script>
import Toolbar from '../toolbar/index.vue';
import XTable from '../table/index.vue';
import XSearch from '../search/index.vue';
import { getInitialValueByComponent } from '../shared/utils';

export default {
  name: 'XSearchTable',
  components: { Toolbar, XTable, XSearch },
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    /**
     * @description 搜索表格配置项，包含搜索表单配置、表格列配置等
     * @property {Array} searchFormConfig - 搜索表单配置项
     * @property {Array} tableColumnsConfig - 表格列配置项
     * @property {Array} toolbars - 工具栏配置项
     * @property {Object} tableOptions - 表格配置项
     */
    searchTableConfig: {
      type: Object,
      default: () => ({
        searchFormConfig: [],
        tableColumnsConfig: [],
      }),
      required: true,
    },
    /**
     * @description 是否为单一表格模式
     */
    isSingle: {
      type: Boolean,
      default: true,
    },
    /**
     * @description 搜索表单的值
     */
    value: {
      type: Object,
      default: undefined,
    },
    /**
     * @description 表格多选事件回调函数
     * @param {Array} selection - 已选择的行数据
     */
    selectionChange: {
      type: Function,
      default: () => {},
    },
    /**
     * @description 表格当前选中的数据
     */
    selection: {
      type: Array,
      default: () => [],
    },
    /**
     * @description 是否在组件创建时自动请求数据
     */
    initializeData: {
      type: Boolean,
      default: true,
    },
    /**
     * @description 获取表格数据的API函数
     * @param {Object} params - 请求参数
     * @returns {Promise} 返回包含list和totalSize的数据对象
     */
    fetchTableApi: {
      type: Function,
      default: null,
    },
    /**
     * @description 请求表格数据时的额外固定参数
     */
    fetchTableParams: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 自定义获取表格数据的函数
     * @param {Object} query - 查询参数
     * @param {Function} callback - 成功回调函数
     */
    fetchTableFunction: {
      type: Function,
      default: null,
    },
    /**
     * @description 是否显示表格
     */
    showTable: {
      type: Boolean,
      default: true,
    },
    /**
     * @description 是否启用表格行拖拽排序
     */
    draggable: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 表单域标签的宽度，例如 '80px'。作为 Form 直接子元素的 form-item 会继承该值
     */
    labelWidth: {
      type: String,
      default: '80px',
    },
    /**
     * @description 每行显示的搜索项数量
     */
    searchItemSpan: {
      type: [String, Number],
      default: 4,
    },
    /**
     * @description 标签页配置，支持数组或对象格式
     */
    tabArray: {
      type: [Object, Array],
      default: () => null,
    },
    /**
     * @description 当前激活的标签页的值
     */
    tabActive: {
      type: [Number, String],
      default: null,
    },
    /**
     * @description 标签页组件的属性配置
     */
    tabAttr: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 内容区域的左右间距，格式为 [左间距, 右间距]
     */
    gutters: {
      type: Array,
      default: () => null,
    },
    /**
     * @description 表格组件的扩展属性
     */
    tableAttrs: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 搜索组件的扩展属性
     */
    searchAttrs: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 标题组件的扩展属性
     */
    titleAttrs: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 分页配置参数
     */
    paginationConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      tableData: [], // 表格数据
      loading: false, // 表格加载状态
      defaultPaginationConfig: {
        total: 0,
        pageSize: 10,
        pageNo: 1,
        pageSizeOptions: [10, 20, 30, 50, 100],
      },
      innerSearchForm: {}, // 新增，支持非受控的搜索表单状态
      initialUserSearchValues: {}, // 记录用户传入的搜索表单初始默认值
    };
  },
  computed: {
    searchForm: {
      get() {
        // 受控优先
        return this.value !== undefined ? this.value : this.innerSearchForm;
      },
      set(val) {
        if (this.value !== undefined) {
          this.$emit('update:value', val);
        } else {
          this.innerSearchForm = val;
        }
      },
    },
    tabsMap() {
      let tabsMap = null;
      if (this.tabArray) {
        if (Array.isArray(this.tabArray)) {
          tabsMap = this.tabArray.map((tab) => ({
            ...tab,
          }));
        } else {
          tabsMap = Object.keys(this.tabArray).map((tab) => ({
            ...this.tabArray[tab],
          }));
        }
      }
      return tabsMap;
    },
    // 当前激活的tabIndex
    tabActiveIndex: {
      get() {
        if (this.tabsMap?.length > 0) {
          return this.tabActive || this.tabsMap[0].value;
        }
        return '';
      },
      set(val) {
        this.$emit('update:tabActive', val);
      },
    },
    // 标题
    title() {
      return this.searchTableConfig?.title || '';
    },
    // 搜索栏表单配置
    searchFormConfig() {
      return this.searchTableConfig?.searchFormConfig || [];
    },
    // 表格列配置
    tableColumnsConfig() {
      return this.searchTableConfig.tableColumnsConfig;
    },
    // 页控制栏配置
    toolbars() {
      return this.searchTableConfig.toolbars || [];
    },
    // 表格自身的参数配置
    tableOptions() {
      return this.searchTableConfig.tableOptions;
    },
    // 请求表格数据方法
    fetchTableFunc() {
      if (this.fetchTableApi) {
        return async (query, succeed) => {
          const {
            data: { list, totalSize },
          } = await this.fetchTableApi({
            ...query,
            ...this.fetchTableParams,
          });
          succeed({ total: Number(totalSize), records: list });
        };
      }
      return this.fetchTableFunction;
    },
    // 多选数据的变更
    selectionArray: {
      get() {
        return this.selection;
      },
      set(val) {
        this.$emit('update:selection', val);
      },
    },
    // 处理间距样式
    gutterPx() {
      if (this.gutters) {
        const [left, right] = this.gutters;
        const isPixel = (value) => typeof value === 'string' && value.includes('px');
        return {
          left: isPixel(left) ? left : `${left}px`,
          right: isPixel(right) ? right : `${right}px`,
        };
      }
      return {
        left: null,
        right: null,
      };
    },
    // 最终分页参数
    finallyPaginationConfig() {
      return {
        ...this.defaultPaginationConfig,
        ...this.paginationConfig,
      };
    },
  },
  watch: {
    // 处理删除表格数据场景，监听表格，若当前显示为最后一页，并最后一页只有一条，删除之后，自动返回上一页数据
    'finallyPaginationConfig.total': {
      handler(newTotal, oldTotal) {
        if (newTotal !== oldTotal && newTotal > 0) {
          const { pageNo, pageSize } = this.finallyPaginationConfig;
          const maxPage = Math.ceil(newTotal / pageSize);

          if (pageNo > maxPage) {
            // 使用 Vue.set 来触发响应式更新
            this.$set(this.finallyPaginationConfig, 'pageNo', maxPage);
            this.$nextTick(() => {
              this.search();
            });
          }
        }
      },
      immediate: false,
    },
  },
  created() {
    // 记录用户传入的搜索表单初始默认值（用于重置时保持用户的默认值）
    if (this.value !== undefined && typeof this.value === 'object') {
      this.initialUserSearchValues = { ...this.value };
    }

    // 初始化搜索表单数据
    if (this.value === undefined) {
      const initSearchForm = {};
      this.searchFormConfig.forEach((item) => {
        const { component, prop, formInputConfig } = item;
        if (prop) {
          initSearchForm[prop] = getInitialValueByComponent(component, formInputConfig);
        }
      });
      this.innerSearchForm = initSearchForm;
    }
    // eslint-disable-next-line no-unused-expressions
    this.initializeData && this.search();
  },
  mounted() {
    // 往该组件上绑定vxe-table的方法
    this.forwardMethods.call(this, this.$refs.baseTable.$refs.table);
  },
  methods: {
    /**
     * @description 处理分页变化事件
     * @param {Object} tab - 当前标签页数据
     * @param {Object} config - 分页配置信息
     * @param {Number} config.pageNo - 当前页码
     * @param {Number} config.pageSize - 每页显示条数
     * @fires paginationChange
     */
    handlePaginationChange(config) {
      const { pageNo, pageSize } = config;
      this.finallyPaginationConfig.pageNo = pageNo;
      this.finallyPaginationConfig.pageSize = pageSize;
      this.finallyPaginationConfig.type = 'pagination';
      this.search();
      this.$nextTick(() => {
        this.$emit('paginationChange', this.finallyPaginationConfig);
      });
    },
    /**
     * @description 获取表格数据
     * @param {Object} [params] - 搜索参数
     * @param {Boolean} [params.reset=false] - 是否重置页码
     */
    async search(params = {}) {
      // 当需要重置页数时，传入reset为true
      if (params.reset) {
        this.finallyPaginationConfig.pageNo = 1;
      }
      this.loading = true;
      try {
        // 清理搜索参数，但保留有意义的值
        const cleanedSearchForm = {};
        // eslint-disable-next-line guard-for-in
        for (const key in this.searchForm) {
          const value = this.searchForm[key];
          if (value !== undefined && value !== null && value !== '') {
            cleanedSearchForm[key] = value;
          }
        }

        await this.fetchTableFunc(
          {
            ...cleanedSearchForm,
            pageNo: this.finallyPaginationConfig.pageNo,
            pageSize: this.finallyPaginationConfig.pageSize,
          },
          ({ records, total }) => {
            this.finallyPaginationConfig.total = total || 0;
            this.tableData = records || [];
          },
        );
      } catch (error) {
        console.error('表格数据请求失败:', error);
        // 触发错误事件，让父组件处理
        this.$emit('error', error);
        // 重置表格数据
        this.tableData = [];
        this.finallyPaginationConfig.total = 0;
      } finally {
        setTimeout(() => {
          this.loading = false;
        }, 100);
      }
    },
    /**
     * @description 处理搜索按钮点击事件
     */
    handleSearchBtnClick() {
      this.finallyPaginationConfig.pageNo = 1;
      this.finallyPaginationConfig.type = 'search';
      this.search();
    },
    /**
     * @description 处理标签页切换事件
     */
    handleTabsChange(tab) {
      if (tab.name !== this.tabActive) {
        this.finallyPaginationConfig.pageNo = 1;
        this.finallyPaginationConfig.type = 'search';
        this.finallyPaginationConfig.total = 0;

        // 重置搜索表单但保留用户的默认值
        if (this.$refs.search) {
          this.$refs.search.handleSearchReset();
        }
      }
    },
    /**
     * @description 处理搜索表单重置事件
     * @param {Object} params - 重置后的表单数据
     * @fires resetSearch
     */
    handleResetSearch(params) {
      this.$emit('resetSearch', params);
      this.search();
    },
    /**
     * @description 将子组件的方法绑定到当前组件实例上
     * @param {Object} childComponent - 子组件实例
     * @private
     */
    forwardMethods(childComponent) {
      if (!childComponent?.$options?.methods) return;

      const { methods } = childComponent.$options;
      const reservedMethods = [
        'forwardMethods',
        'search',
        'handleSearchBtnClick',
        'handleTabsChange',
        'handlePaginationChange',
      ];

      // 遍历子组件的方法
      Object.keys(methods).forEach((methodName) => {
        // 避免覆盖当前组件的关键方法
        if (!reservedMethods.includes(methodName) && typeof childComponent[methodName] === 'function') {
          this[methodName] = (...args) => childComponent[methodName](...args);
        }
      });
    },
  },
};
</script>

<style lang="scss">
@import '../styles/index.scss';
.x-search-table {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  border-radius: 3px;
  &__left {
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    flex-shrink: 0;
    & > div {
      height: 100%;
      position: relative;
    }
  }
  &__flex {
    height: 100%;
  }
  &__content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    @include scrollbar();
  }
  &__wrapper {
    width: 100%;
    display: flex;
    overflow: hidden;
    flex-grow: 1;
    .table {
      width: 100%;
      flex-grow: 1;
      overflow-x: auto;
      display: flex;
      @include scrollbar();
      .vxe-grid {
        width: 100%;
        height: 100%;
      }
    }
  }
  .x-search,
  .x-toolbar {
    border-radius: 3px 3px 0 0;
    flex-shrink: 0;
  }
  .el-tab-pane > .x-search {
    margin-top: 0;
  }

  .x-toolbar + .x-search {
    border-radius: 0;
  }
  .x-search + .x-search-table__wrapper {
    .x-table {
      padding: 0 15px;
    }
  }
}
</style>
<style lang="scss" scoped>
::v-deep {
  .el-tabs__header {
    padding: 12px 15px 0;
    margin: 0;
  }
  .vxe-grid--layout-body-content-wrapper {
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  .vxe-grid .vxe-grid--table-container {
    flex-grow: 1;
  }
  .vxe-toolbar {
    padding: 8px 0 0;
  }
  .vxe-grid--toolbar-wrapper + .vxe-grid--table-container {
    margin-top: 8px;
  }
}
</style>
