<template>
  <div class="component-view" style="height: 600px">
    <x-search-table
      ref="searchTable111"
      v-model="searchForm"
      :search-table-config="searchTableConfig"
      :fetch-table-function="fetchTableFunction"
      :search-attrs="{
        minShowRowNumber: 2,
      }"
      :table-attrs="{
        pagination: false,
        id: 'deptId',
        'keep-source': true,
        treeConfig: {
          transform: true,
          rowField: 'deptId',
          parentField: 'parentId',
          expandAll: true, // 首次默认全展开
        },
        editConfig: { trigger: 'manual', mode: 'row', showStatus: true, autoClear: false },
      }"
      :sort-change="sortChange"
    />
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      saveLoading: true,
      searchForm: {
        b: 0,
      },
    };
  },
  computed: {
    // 搜索表格配置项
    searchTableConfig() {
      return {
        title: '商机管理13',
        // 表格自定义参数
        tableOptions: {
          props: {
            border: true,
          },
        },
        searchFormConfig: [
          {
            prop: 'name',
            label: '关键词搜索',
            component: 'input',
            formInputConfig: {
              placeholder: '搜索手机号码/url链接/来源',
            },
          },
          {
            prop: 'b',
            label: '普通下拉组件',
            component: 'select',
            formInputConfig: {
              options: [
                { label: '1111', value: 0 },
                { label: '张三', value: 1 },
                { label: '李四', value: 2 },
                { label: '王五', value: 3 },
              ],
            },
          },
          {
            prop: 'name3',
            label: '关键词搜索',
            component: 'input',
            formInputConfig: {
              placeholder: '搜索手机号码/url链接/来源',
            },
          },
          {
            label: '日期区间',
            prop: 'opportunityTime',
            component: 'date',
            formInputConfig: {
              type: 'datetimerange',
              'start-placeholder': '开始时间',
              'end-placeholder': '结束时间',
            },
          },
          {
            label: '普通时间',
            prop: 'date',
            component: 'date',
            formInputConfig: {
              type: 'date',
            },
          },
        ],
        tableColumnsConfig: [
          {
            title: '部门名称',
            field: 'deptName',
            treeNode: true, // 以当前单元格开启树
            editRender: {},
            slots: {
              edit: ({ row }, h) => <x-input vModel={row.deptName} type="text"></x-input>,
            },
          },
          {
            title: '排序字段1',
            field: 'a',
            editRender: {},
            slots: {
              edit: ({ row }, h) => <x-input-number vModel={row.排序字段1}></x-input-number>,
            },
            sortable: true,
          },
          {
            title: '排序字段2',
            field: 'b',
            sortable: true,
          },
          {
            type: 'actions',
            minShowActions: 2,
            width: 150,
            fixed: 'right',
            title: '操作',
            actions: [
              // {
              //   label: '查看',
              //   onClick: (row, index, e) => {},
              // },
              {
                label: '编辑',
                show: (row) => !this.hasEditStatus(row),
                onClick: (row, index, e) => {
                  this.$refs.searchTable111.setEditRow(row);
                },
              },
              {
                label: '保存',
                isLoading: true,
                show: (row) => this.hasEditStatus(row),
                onClick: (row, index, { $event, startLoading, endLoading }) => {
                  const $table = this.$refs.searchTable111;
                  if ($table) {
                    $table.clearEdit().then(() => {
                      startLoading();
                      setTimeout(() => {
                        endLoading();
                        this.$message.success('保存成功！');
                      }, 2000);
                    });
                  }
                },
              },
              {
                label: '取消',
                show: (row) => this.hasEditStatus(row),
                onClick: (row, index, e) => {
                  const $table = this.$refs.searchTable111;
                  if ($table) {
                    $table.clearEdit();
                  }
                },
              },
              {
                label: '删除',
                theme: 'danger',
                onClick: (row, index, e) => {},
              },
            ],
          },
        ],
      };
    },
  },
  methods: {
    /**
     * 渲染表格数据
     * @param {*} query 查询数据
     * @param {*} succeed 成功回调
     */
    async fetchTableFunction(query, succeed) {
      // const {
      // 	data: { rows, total }
      // } = await list({
      // 	...query
      // });
      const res = {
        msg: '操作成功',
        code: 200,
        data: {
          total: 4,
          rows: [
            {
              deptId: 100,
              parentId: 0,
              deptName: '应急园区',
              a: 1,
              b: 2,
            },
            {
              deptId: 112,
              parentId: 6,
              deptName: '值班A',
              a: 2,
              b: 3,
            },
            {
              deptId: 104,
              parentId: 6,
              deptName: '值班1',
              a: 3,
              b: 4,
            },
            {
              deptId: 6,
              parentId: 100,
              deptName: '值班人员',
              a: 4,
              b: 5,
            },
          ],
        },
      };
      // 如果需要处理接口返回的表格数据，可以在这里处理数据
      succeed({ total: Number(res.data.total), records: res.data.rows });
    },
    /**
     * 排序事件
     * @param {*} sortEvent
     * @param {*} callback  回调必须，去更新排序选中状态
     */
    sortChange(sortEvent, callback) {
      // 一般情况下排序是与服务器交互 根据不同条件传不同参数调用接口
      switch (sortEvent?.property) {
        case 'a':
          // a列的排序 设置传参
          this.fetchTableFunction(() => {
            callback();
          });
          break;
        case 'b':
          // b列的排序 设置传参
          this.fetchTableFunction(() => {
            callback();
          });
          break;
        default:
      }
    },
    hasEditStatus(row) {
      const $table = this.$refs.searchTable111;
      if ($table) {
        return $table.isEditByRow(row);
      }
    },
  },
};
</script>
