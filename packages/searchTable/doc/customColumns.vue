<template>
  <div class="component-view" style="height: 600px">
    <x-search-table
      ref="searchTable"
      v-model="searchForm"
      :search-table-config="searchTableConfig"
      :fetch-table-function="fetchTableFunction"
      :table-attrs="{
        id: 'id',
        'column-config': { resizable: true },
        'custom-config': { storage: true },
      }"
    />
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      searchForm: {},
      mockData: [
        { id: 1, name: '产品A', price: 199, stock: 100, category: '电子产品', createTime: '2024-03-15', status: 1 },
        { id: 2, name: '产品B', price: 299, stock: 50, category: '家居用品', createTime: '2024-03-14', status: 0 },
        { id: 3, name: '产品C', price: 99, stock: 200, category: '办公用品', createTime: '2024-03-13', status: 1 },
      ],
    };
  },
  computed: {
    searchTableConfig() {
      return {
        title: '自定义列配置示例',
        searchFormConfig: [
          {
            prop: 'name',
            label: '产品名称',
            component: 'input',
          },
          {
            prop: 'category',
            label: '产品类别',
            component: 'select',
            formInputConfig: {
              options: [
                { label: '电子产品', value: '电子产品' },
                { label: '家居用品', value: '家居用品' },
                { label: '办公用品', value: '办公用品' },
              ],
            },
          },
        ],
        tableColumnsConfig: [
          { type: 'checkbox', width: 60, fixed: 'left' },
          { title: '产品名称', field: 'name' },
          { title: '价格', field: 'price', align: 'right', formatter: ({ cellValue }) => `¥${cellValue}` },
          { title: '库存', field: 'stock', align: 'right' },
          { title: '类别', field: 'category' },
          { title: '创建时间', field: 'createTime' },
          {
            title: '状态',
            field: 'status',
            width: 100,
            formatter: ({ cellValue }) => (cellValue === 1 ? '上架' : '下架'),
          },
          {
            type: 'actions',
            title: '操作',
            width: 100,
            fixed: 'right',
            actions: [
              {
                label: '查看',
                onClick: (row, index, e) => {},
              },
              {
                label: '编辑',
                onClick: (row, index, e) => {},
              },
            ],
          },
        ],
      };
    },
  },
  methods: {
    fetchTableFunction(query, succeed) {
      // 模拟异步请求
      const filteredData = this.mockData;

      // 使用succeed回调返回结果
      succeed({
        records: filteredData,
        total: filteredData.length,
      });
    },
    handleEdit(row) {
      console.log('编辑行数据', row);
    },
    handleDelete(row) {
      console.log('删除行数据', row);
    },
  },
};
</script>
