<template>
  <div class="component-view" style="height: 600px">
    <x-search-table
      ref="searchTable"
      v-model="searchForm"
      :search-table-config="searchTableConfig"
      :fetch-table-function="fetchTableFunction"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchForm: {},
      // 模拟数据
      tableData: [
        {
          date: '2024-01-01',
          name: '张三',
          province: '浙江',
          city: '杭州',
          address: '西湖区',
          zip: 310000,
          score: 98,
        },
        {
          date: '2024-01-02',
          name: '李四',
          province: '江苏',
          city: '南京',
          address: '鼓楼区',
          zip: 210000,
          score: 87,
        },
        {
          date: '2024-01-03',
          name: '王五',
          province: '广东',
          city: '深圳',
          address: '南山区',
          zip: 518000,
          score: 92,
        },
      ],
    };
  },
  computed: {
    searchTableConfig() {
      return {
        title: '表头合并示例',
        tableOptions: {
          border: true,
        },
        // 搜索表单配置
        searchFormConfig: [
          {
            prop: 'name',
            label: '姓名',
            component: 'input',
          },
          {
            prop: 'date',
            label: '日期',
            component: 'date',
          },
        ],
        // 表格列配置
        tableColumnsConfig: [
          {
            title: '日期',
            field: 'date',
          },
          {
            title: '用户信息',
            children: [
              {
                title: '姓名',
                field: 'name',
                width: 100,
              },
              {
                title: '得分',
                field: 'score',
                width: 80,
              },
            ],
          },
          {
            title: '地址信息',
            children: [
              {
                title: '省份/城市',
                children: [
                  {
                    title: '省份',
                    field: 'province',
                  },
                  {
                    title: '城市',
                    field: 'city',
                  },
                ],
              },
              {
                title: '详细地址',
                field: 'address',
              },
              {
                title: '邮编',
                field: 'zip',
              },
            ],
          },
        ],
      };
    },
  },
  methods: {
    // 模拟获取表格数据的函数
    fetchTableFunction(query, succeed) {
      return new Promise((resolve) => {
        setTimeout(() => {
          succeed({
            records: this.tableData,
            total: this.tableData.length,
          });
          resolve();
        }, 300);
      });
    },
  },
};
</script>
