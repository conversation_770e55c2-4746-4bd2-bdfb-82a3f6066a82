<template>
  <div class="component-view" style="height: 600px">
    <x-search-table
      ref="searchTable"
      v-model="searchForm"
      :search-table-config="searchTableConfig"
      :fetch-table-function="fetchTableFunction"
    >
      <div class="search-left" slot="searchTableLeft">左侧插槽</div>
    </x-search-table>
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      searchForm: {},
      mockData: [
        {
          id: 1,
          orderNo: 'ORD20240315001',
          customerName: '张三',
          amount: 1999.99,
          status: 'pending',
          paymentMethod: 'alipay',
          createTime: '2024-03-15 10:30:00',
          region: '华东',
          category: '电子产品',
        },
        {
          id: 2,
          orderNo: 'ORD20240315002',
          customerName: '李四',
          amount: 2599.0,
          status: 'completed',
          paymentMethod: 'wechat',
          createTime: '2024-03-15 11:15:00',
          region: '华南',
          category: '家居用品',
        },
        {
          id: 3,
          orderNo: 'ORD20240315003',
          customerName: '王五',
          amount: 3299.5,
          status: 'processing',
          paymentMethod: 'card',
          createTime: '2024-03-15 14:20:00',
          region: '华北',
          category: '服装',
        },
      ],
    };
  },
  computed: {
    searchTableConfig() {
      return {
        title: '高级查询示例',
        searchFormConfig: [
          {
            prop: 'keyword',
            label: '关键词搜索',
            component: 'input',
            formInputConfig: {
              placeholder: '订单号/客户名称',
            },
          },
          {
            prop: 'dateRange',
            label: '创建时间',
            component: 'date',
            formInputConfig: {
              type: 'datetimerange',
              'start-placeholder': '开始时间',
              'end-placeholder': '结束时间',
            },
          },
          {
            prop: 'status',
            label: '订单状态',
            component: 'select',
            formInputConfig: {
              options: [
                { label: '待处理', value: 'pending' },
                { label: '处理中', value: 'processing' },
                { label: '已完成', value: 'completed' },
              ],
            },
          },
          {
            prop: 'region',
            label: '所属区域',
            component: 'select',
            formInputConfig: {
              options: [
                { label: '华东', value: '华东' },
                { label: '华南', value: '华南' },
                { label: '华北', value: '华北' },
              ],
            },
          },
          {
            prop: 'category',
            label: '商品类别',
            component: 'select',
            formInputConfig: {
              options: [
                { label: '电子产品', value: '电子产品' },
                { label: '家居用品', value: '家居用品' },
                { label: '服装', value: '服装' },
              ],
            },
          },
          {
            prop: 'minAmount',
            label: '订单金额',
            component: 'inputNumber',
            formItemSpan: 6,
            formInputConfig: {
              'controls-position': 'right',
              placeholder: '最小金额',
              min: 0,
            },
          },
          {
            prop: 'maxAmount',
            label: '订单最大金额',
            formItemSpan: 6,
            component: 'inputNumber',
            formInputConfig: {
              'controls-position': 'right',
              placeholder: '最大金额',
              min: 0,
            },
          },
        ],
        tableColumnsConfig: [
          { type: 'checkbox' },
          { title: '订单号', field: 'orderNo' },
          { title: '客户名称', field: 'customerName' },
          {
            title: '订单金额',
            field: 'amount',
            sortable: true,
            formatter: ({ cellValue }) => `¥${cellValue.toFixed(2)}`,
          },
          {
            title: '支付方式',
            field: 'paymentMethod',
            formatter: ({ cellValue }) =>
              ({
                alipay: '支付宝',
                wechat: '微信支付',
                card: '银行卡',
              })[cellValue],
          },
          {
            title: '订单状态',
            field: 'status',
            width: 100,
            slots: {
              default: ({ row }) => {
                const statusMap = {
                  pending: { type: 'warning', label: '待处理' },
                  processing: { type: 'primary', label: '处理中' },
                  completed: { type: 'success', label: '已完成' },
                };
                const status = statusMap[row.status];
                return <el-tag type={status.type}>{status.label}</el-tag>;
              },
            },
          },
          { title: '所属区域', field: 'region', width: 100 },
          { title: '商品类别', field: 'category', width: 100 },
          { title: '创建时间', field: 'createTime', width: 160, sortable: true },
          {
            type: 'actions',
            title: '操作',
            width: 150,
            fixed: 'right',
            actions: [
              {
                label: '查看',
                onClick: (row, index, e) => {
                  console.log(
                    '%c [ 查看 ]-185-「advancedSearch」',
                    'font-size:13px; background:pink; color:#bf2c9f;',
                    row,
                  );
                },
              },
              {
                label: '编辑',
                onClick: (row, index, e) => {
                  console.log(
                    '%c [ 编辑 ]-185-「advancedSearch」',
                    'font-size:13px; background:pink; color:#bf2c9f;',
                    row,
                  );
                },
              },
              {
                label: '删除',
                theme: 'danger',
                show: (row) => row.status !== 'completed',
                onClick: (row, index, e) => {
                  console.log(
                    '%c [ 编辑 ]-185-「advancedSearch」',
                    'font-size:13px; background:pink; color:#bf2c9f;',
                    row,
                  );
                },
              },
            ],
          },
        ],
      };
    },
  },
  methods: {
    fetchTableFunction(query, succeed) {
      // 模拟异步请求
      const filteredData = this.mockData.filter(
        (item) =>
          (!query.orderNo || item.orderNo.includes(query.orderNo)) &&
          (!query.customerName || item.customerName.includes(query.customerName)) &&
          (!query.status || item.status === query.status) &&
          (!query.paymentMethod || item.paymentMethod === query.paymentMethod) &&
          (!query.region || item.region === query.region) &&
          (!query.category || item.category === query.category),
      );

      // 使用succeed回调返回结果
      succeed({
        records: filteredData,
        total: filteredData.length,
      });
    },
  },
};
</script>
<style lang="scss">
.search-left {
  width: 260px;
  border-right: 1px solid #ebeef5;
}
</style>
