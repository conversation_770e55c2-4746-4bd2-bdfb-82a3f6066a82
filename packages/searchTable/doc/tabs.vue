<template>
  <div class="component-view" style="height: 600px">
    <x-search-table
      ref="searchTable"
      v-model="searchForm"
      :is-single="false"
      :search-table-config="searchTableConfig"
      :fetch-table-function="fetchTableFunction"
      :tab-array="tabArray"
      :tab-active.sync="tabActive"
    />
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      searchForm: {},
      tabActive: 'pending',
      tabArray: [
        { label: '待处理', value: 'pending' },
        { label: '已处理', value: 'processed' },
        { label: '已拒绝', value: 'rejected' },
      ],
    };
  },
  computed: {
    // 搜索表格配置项
    searchTableConfig() {
      return {
        title: '工单管理',
        searchFormConfig: [
          {
            prop: 'keyword',
            label: '关键词搜索',
            component: 'input',
            formInputConfig: {
              placeholder: '请输入工单号/客户名称',
            },
          },
          {
            label: '提交时间',
            prop: 'submitTime',
            component: 'date',
            formInputConfig: {
              type: 'daterange',
              'start-placeholder': '开始日期',
              'end-placeholder': '结束日期',
            },
          },
          {
            prop: 'priority',
            label: '优先级',
            component: 'select',
            isShow: this.tabActive === 'processed',
            formInputConfig: {
              options: [
                { label: '高', value: 'high' },
                { label: '中', value: 'medium' },
                { label: '低', value: 'low' },
              ],
            },
          },
        ],
        tableColumnsConfig: [
          {
            title: '工单号',
            field: 'ticketId',
          },
          {
            title: '客户名称',
            field: 'customerName',
          },
          {
            title: '问题类型',
            field: 'issueType',
          },
          {
            title: '优先级',
            field: 'priority',
            slots: {
              default: ({ row }, h) => {
                const typeMap = {
                  high: 'danger',
                  medium: 'warning',
                  low: 'info',
                };
                return <el-tag type={typeMap[row.priority]}>{row.priority}</el-tag>;
              },
            },
          },
          {
            title: '提交时间',
            field: 'submitTime',
            sortable: true,
          },
          {
            type: 'actions',
            title: '操作',
            width: 100,
            actions: [
              {
                label: '查看',
                onClick: (row, index, e) => {},
              },
              {
                label: '编辑',
                onClick: (row, index, e) => {},
              },
            ],
          },
        ],
      };
    },
  },
  methods: {
    // 获取表格数据
    async fetchTableFunction(query, succeed) {
      console.log('%c [  ]-118-「tabs」', 'font-size:13px; background:pink; color:#bf2c9f;', query);
      // 模拟接口请求
      setTimeout(() => {
        succeed({
          records: [
            {
              ticketId: 'TK2024001',
              customerName: '张三',
              issueType: '产品咨询',
              priority: 'high',
              submitTime: '2024-03-10 10:00:00',
            },
            {
              ticketId: 'TK2024002',
              customerName: '李四',
              issueType: '技术支持',
              priority: 'medium',
              submitTime: '2024-03-10 11:30:00',
            },
          ],
          total: 22,
        });
      }, 500);
    },
  },
};
</script>
