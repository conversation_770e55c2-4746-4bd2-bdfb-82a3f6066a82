<script>
  import BasicVue from './basic.vue';
  import TabsVue from './tabs.vue';
  import TreeEditVue from './treeEdit.vue';
  import CustomColumnsVue from './customColumns.vue';
  import MergeHeaderVue from './mergeHeader.vue';
  import AdvancedSearchVue from './advancedSearch.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      TabsVue,
      TreeEditVue,
      CustomColumnsVue,MergeHeaderVue,
      AdvancedSearchVue,
      Preview
    }
  };
</script>

## SearchTable 组件

一个功能强大的搜索表格组件，集成了搜索表单和表格功能，支持分页、排序、多选、标签页等特性。适用于需要搜索筛选功能的数据表格展示场景。

### 组件特点

- 集成搜索表单：支持动态配置表单项，灵活的布局调整
- 强大的表格功能：支持分页、排序、多选、拖拽排序等特性
- 标签页模式：支持在一个组件中展示多个相关联的表格
- 自定义扩展：提供丰富的插槽和工具栏配置
- 数据管理：支持异步数据加载、自动刷新、数据缓存等
- 灵活配置：提供完整的配置项，支持自定义样式和行为

### 使用场景

- 数据列表展示：适用于需要展示大量数据的列表页面
- 数据筛选：支持复杂的搜索条件组合和筛选
- 数据操作：支持表格数据的选择、排序、编辑等操作
- 多维度数据：通过标签页模式展示多个相关数据表格

### 基础用法

基础的 SearchTable 用法，展示了如何配置搜索表单和表格列。

<basic-vue/>
<preview comp-name='searchTable' demo-name='basic'/>

### 标签页模式

使用标签页模式展示多个相关联的表格，适用于需要在同一页面管理多个相关数据列表的场景。

<tabs-vue/>
<preview comp-name='searchTable' demo-name='tabs'/>

### 树形表格

展示如何配置树形结构的表格，适用于需要展示层级关系数据的场景。

<tree-edit-vue/>
<preview comp-name='searchTable' demo-name='treeEdit'/>

### 自定义列配置

展示如何配置可调整列宽、自定义列显示的表格。支持列宽拖拽调整和列显示配置的本地存储。

<custom-columns-vue/>
<preview comp-name='searchTable' demo-name='customColumns'/>

### 合并表头示例

展示如何配置合并表头的表格，适用于需要展示复杂表头结构的场景。
<merge-header-vue/>
<preview comp-name='searchTable' demo-name='mergeHeader'/>

### 高级查询示例

展示如何配置复杂的查询条件和高级筛选功能，包括多种表单组件的组合使用。

<advanced-search-vue/>
<preview comp-name='searchTable' demo-name='advancedSearch'/>

### Attributes

| 参数               | 说明                                                                     | 类型          | 可选值            | 默认值  |
| ------------------ | ------------------------------------------------------------------------ | ------------- | ----------------- | ------- |
| searchTableConfig  | 搜索表格配置项，包含搜索表单配置、表格列配置等                           | object        | —                 | —       |
| isSingle           | 是否为单一表格模式，是：使用flex布局，占用父容器剩余高度；否：自动撑高。 | boolean       | —                 | true    |
| value / v-model    | 搜索表单的值                                                             | object        | —                 | {}      |
| selection          | 表格当前选中的数据                                                       | array         | —                 | []      |
| initializeData     | 是否在组件创建时自动请求数据                                             | boolean       | —                 | true    |
| fetchTableApi      | 获取表格数据的API函数                                                    | function      | —                 | null    |
| fetchTableParams   | 请求表格数据时的额外固定参数                                             | object        | —                 | {}      |
| fetchTableFunction | 自定义获取表格数据的函数                                                 | function      | —                 | null    |
| showTable          | 是否显示表格                                                             | boolean       | —                 | true    |
| sortChange         | 表格排序事件的回调函数                                                   | function      | —                 | null    |
| draggable          | 是否启用表格行拖拽排序                                                   | boolean       | —                 | false   |
| labelWidth         | 表单域标签的宽度                                                         | string        | —                 | '80px'  |
| labelPosition      | 表单域标签的对齐方式                                                     | string        | left/right/center | 'right' |
| searchItemSpan     | 每行显示的搜索项数量                                                     | number/string | —                 | 4       |
| tabArray           | 标签页配置，支持数组或对象格式                                           | array/object  | —                 | null    |
| tabActive          | 当前激活的标签页的值                                                     | number/string | —                 | null    |
| tabAttr            | 标签页组件的属性配置                                                     | object        | —                 | {}      |
| gutters            | 内容区域的左右间距，格式为 [左间距, 右间距]                              | array         | —                 | null    |
| tableAttrs         | 表格组件的扩展属性，支持树形表格配置(treeConfig)等                       | object        | —                 | {}      |
| titleAttrs         | 表格标题的扩展属性                                                       | object        | —                 | {}      |

### Events

| 事件名称         | 说明             | 回调参数                                            |
| ---------------- | ---------------- | --------------------------------------------------- |
| selectionChange  | 表格多选事件     | selection: 已选择的行数据数组                       |
| paginationChange | 分页变化事件     | paginationConfig: 分页配置信息                      |
| resetSearch      | 搜索表单重置事件 | formData: 重置后的表单数据                          |
| sort-change      | 表格排序变化事件 | column: 列配置信息, prop: 排序字段, order: 排序方式 |

### Methods

| 方法名称 | 说明                             | 参数 |
| -------- | -------------------------------- | ---- |
| search   | 表格使用当前搜索条件重新加载数据 | —    |

### 注意事项

#### 树形表格配置

使用树形表格时，需要在tableAttrs中配置treeConfig：

```javascript
treeConfig: {
  transform: true, // 是否转换数据结构
  rowField: 'id', // 行数据的唯一标识字段
  parentField: 'parentId', // 父节点标识字段
  expandAll: true, // 是否默认展开所有节点
}
```

### Slots

| 插槽名称        | 说明             |
| --------------- | ---------------- |
| searchTableLeft | 搜索表格左侧内容 |
| tableTop        | 表格顶部内容     |
| tableLeft       | 表格左侧内容     |
| tableRight      | 表格右侧内容     |

### searchTableConfig 配置项

| 参数               | 说明           | 类型   | 可选值 | 默认值 |
| ------------------ | -------------- | ------ | ------ | ------ |
| title              | 表格标题       | string | —      | —      |
| searchFormConfig   | 搜索表单配置项 | array  | —      | []     |
| tableColumnsConfig | 表格列配置项   | array  | —      | []     |
| toolbars           | 工具栏配置项   | array  | —      | []     |
| tableOptions       | 表格配置项     | object | —      | —      |

### tableColumnsConfig 配置项

| 参数      | 说明                                                              | 类型          | 可选值                 | 默认值 |
| --------- | ----------------------------------------------------------------- | ------------- | ---------------------- | ------ |
| title     | 列标题                                                            | string        | —                      | —      |
| field     | 列字段名                                                          | string        | —                      | —      |
| width     | 列宽度                                                            | string/number | —                      | —      |
| fixed     | 列固定方向                                                        | string        | left/right             | —      |
| visible   | 是否显示该列                                                      | boolean       | —                      | true   |
| sortable  | 是否可排序                                                        | boolean       | —                      | false  |
| type      | 列类型                                                            | string        | selection/index/expand | —      |
| align     | 对齐方式                                                          | string        | left/center/right      | left   |
| className | 列样式类名                                                        | string        | —                      | —      |
| formatter | 格式化函数                                                        | function      | —                      | —      |
| slots     | 插槽，参照[vxe-table](https://vxetable.cn/v3/#/grid/api)下的slots | object        | —                      | —      |

### tableControls 表格操作栏配置

| 参数     | 说明                                               | 类型     | 可选值 | 默认值 |
| -------- | -------------------------------------------------- | -------- | ------ | ------ |
| label    | 操作按钮名称                                       | string   | —      | —      |
| show     | 操作按钮是否显示,show:(row)=>{return 条件处理结果} | function | —      | —      |
| theme    | 操作按钮样式，一般为删除按钮设置为danger           | string   | —      | —      |
| icon     | 操作按钮图标名称                                   | string   | —      | —      |
| onClick  | 操作按钮绑定的事件                                 | function | —      | —      |
| disabled | 操作按钮是否禁用                                   | boolean  | —      | —      |
| ...      | XButton 按钮其他参数                               | —        | —      | —      |

### 表格的Methods

使用this.$refs.searchTable.方法名调用

| 方法名            | 说明                   | 参数                                    |
| ----------------- | ---------------------- | --------------------------------------- |
| toggleCheckboxRow | 切换指定行的选中状态   | row: 行数据对象                         |
| setCheckboxRow    | 设置指定行的选中状态   | rows: 行数据对象数组, checked: 是否选中 |
| setAllCheckboxRow | 设置所有行的选中状态   | checked: 是否选中                       |
| clearCheckboxRow  | 清除所有行的选中状态   | —                                       |
| ...               | 兼容所有vxe-table的API | —                                       |
