<template>
  <div class="component-view" style="height: 600px">
    <x-search-table
      ref="searchTable"
      :table-attrs="{
        toolbarConfig: { custom: true },
      }"
      :search-table-config="searchTableConfig"
      :fetch-table-function="fetchTableFunction"
      @resetSearch="handleResetSearch"
    />
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      mockData: [
        { id: 1, name: '产品A', price: 199, category: '电子产品', status: '在售' },
        { id: 2, name: '产品B', price: 299, category: '家居用品', status: '已下架' },
        { id: 3, name: '产品C', price: 99, category: '办公用品', status: '在售' },
        { id: 4, name: '产品D', price: 399, category: '电子产品', status: '待上架' },
        { id: 5, name: '产品E', price: 599, category: '家居用品', status: '在售' },
        { id: 6, name: '产品B', price: 299, category: '家居用品', status: '已下架' },
        { id: 7, name: '产品C', price: 99, category: '办公用品', status: '在售' },
        { id: 48, name: '产品D', price: 399, category: '电子产品', status: '待上架' },
        { id: 59, name: '产品E', price: 599, category: '家居用品', status: '在售' },
      ],
      ruleForm: {},
    };
  },
  computed: {
    // 搜索表格配置项
    searchTableConfig() {
      return {
        title: '基础示例',
        searchFormConfig: [
          {
            prop: 'name',
            label: '关键词',
            component: 'input',
            formInputConfig: {
              placeholder: '请输入产品名称或产品编号',
            },
          },
          {
            prop: 'category',
            label: '产品类别',
            component: 'select',
            formInputConfig: {
              options: [
                { label: '电子产品', value: '电子产品' },
                { label: '家居用品', value: '家居用品' },
                { label: '办公用品', value: '办公用品' },
              ],
            },
          },
          {
            prop: 'status',
            label: '状态',
            component: 'select',
            formInputConfig: {
              options: [
                { label: '在售', value: '在售' },
                { label: '已下架', value: '已下架' },
                { label: '待上架', value: '待上架' },
              ],
            },
          },
        ],
        tableColumnsConfig: [
          { title: '产品名称', field: 'name' },
          { title: '价格', field: 'price', formatter: ({ cellValue }) => `¥${cellValue}` },
          { title: '类别', field: 'category' },
          {
            title: '状态',
            field: 'status',
            width: 100,
            slots: {
              default: ({ row }) => {
                const typeMap = {
                  在售: 'success',
                  已下架: 'info',
                  待上架: 'warning',
                };
                return <el-tag type={typeMap[row.status]}>{row.status}</el-tag>;
              },
            },
          },
          {
            type: 'actions',
            title: '操作',
            width: 150,
            actions: [
              {
                label: '查看',
                onClick: (row, index, e) => {
                  console.log(
                    '%c [  ]-125-「basic」',
                    'font-size:13px; background:pink; color:#bf2c9f;',
                    row,
                    index,
                    e,
                  );
                },
              },
              {
                label: '编辑',
                onClick: (row, index, e) => {},
              },
              {
                label: '删除',
                theme: 'danger',
                onClick: (row, index, e) => {},
              },
            ],
          },
        ],
        toolbars: [
          {
            label: '新增',
            type: 'primary',
            icon: 'el-icon-plus',
            onClick: () => {
              const selected = this.$refs.searchTable.getRadioRecord();
              console.log(
                '%c [ getRadioRecord ]-133-「basic」',
                'font-size:13px; background:pink; color:#bf2c9f;',
                selected,
              );
            },
          },
        ],
      };
    },
  },
  methods: {
    fetchTableFunction(query, succeed) {
      // 使用succeed回调返回结果
      console.log('%c [  ]-145-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', query);
      return new Promise((resolve) => {
        setTimeout(() => {
          succeed({
            records: this.mockData,
            total: this.mockData.length,
          });
          resolve();
        }, 400);
      });
    },
    handleResetSearch() {
      console.log('%c [ 重置 ]-154-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', 111);
    },
  },
};
</script>
