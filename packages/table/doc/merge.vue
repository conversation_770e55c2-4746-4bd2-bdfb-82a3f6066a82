<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-11 17:55:42
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-11 18:01:10
 * @FilePath: /vite-element-components/packages/table/doc/merge.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <x-table :table-data="tableDataSource" :columns="columns" :merge-cells="mergedCells" border />
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      tableDataSource: [
        { department: '技术部', name: '张三', position: '前端工程师' },
        { department: '技术部', name: '李四', position: '后端工程师' },
        { department: '市场部', name: '王五', position: '市场经理' },
        { department: '市场部', name: '赵六', position: '市场专员' },
        { department: '财务部', name: '陈七', position: '会计' },
      ],
      columns: [
        { title: '部门', field: 'department' },
        { title: '姓名', field: 'name' },
        { title: '职位', field: 'position' },
      ],
      // 自动合并配置
      mergeCells: [],
    };
  },
  computed: {
    // 根据数据自动合并单元格
    mergedCells() {
      const mergeCells = [];
      let prevDept = '';
      let startRow = 0;

      // 自动合并部门列
      this.tableDataSource.forEach((row, index) => {
        if (row.department !== prevDept && index !== 0) {
          if (index - startRow > 1) {
            mergeCells.push({
              row: startRow,
              col: 0,
              rowspan: index - startRow,
              colspan: 1,
            });
          }
          startRow = index;
        }
        prevDept = row.department;
      });

      // 添加最后一个部门的合并
      if (this.tableDataSource.length - startRow > 1) {
        mergeCells.push({
          row: startRow,
          col: 0,
          rowspan: this.tableDataSource.length - startRow,
          colspan: 1,
        });
      }

      // 保留职位列的特殊合并逻辑
      mergeCells.push({ row: 1, col: 2, rowspan: 2, colspan: 1 });
      return mergeCells;
    },
  },
  methods: {},
};
</script>
