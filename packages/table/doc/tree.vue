<template>
  <div class="component-view">
    <x-table
      ref="treeTable"
      title="树形表格示例"
      :table-data="tableDataSource"
      :columns="tableColumns"
      :tree-config="{
        rowField: 'id',
        childrenField: 'children',
        expandAll: true,
      }"
    />
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      tableDataSource: [
        {
          id: 1,
          name: '研发部',
          manager: '张三',
          employeeCount: 20,
          budget: 500000,
          children: [
            {
              id: 11,
              name: '前端组',
              manager: '李四',
              employeeCount: 8,
              budget: 200000,
              children: [
                {
                  id: 111,
                  name: 'Web组',
                  manager: '王五',
                  employeeCount: 5,
                  budget: 120000,
                },
                {
                  id: 112,
                  name: '移动组',
                  manager: '赵六',
                  employeeCount: 3,
                  budget: 80000,
                },
              ],
            },
            {
              id: 12,
              name: '后端组',
              manager: '钱七',
              employeeCount: 12,
              budget: 300000,
              children: [
                {
                  id: 121,
                  name: 'Java组',
                  manager: '孙八',
                  employeeCount: 7,
                  budget: 180000,
                },
                {
                  id: 122,
                  name: 'Python组',
                  manager: '周九',
                  employeeCount: 5,
                  budget: 120000,
                },
              ],
            },
          ],
        },
      ],
      paginationConfig: {
        total: 7,
        pageSize: 10,
        pageNo: 1,
      },
    };
  },
  computed: {
    tableColumns() {
      return [
        {
          title: '部门名称',
          field: 'name',
          treeNode: true,
        },
        {
          title: '负责人',
          field: 'manager',
        },
        {
          title: '人数',
          field: 'employeeCount',
          align: 'right',
        },
        {
          title: '预算(元)',
          field: 'budget',
          align: 'right',
          slots: {
            default: ({ row }) => `¥${row.budget.toLocaleString()}`,
          },
        },
      ];
    },
  },
};
</script>
