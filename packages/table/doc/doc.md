<script>
  import BasicVue from './basic.vue';
  import SortVue from './sort.vue';
  import TreeVue from './tree.vue';
  import CustomVue from './custom.vue';
  import MergeVue from './merge.vue';
  import MergeHeaderVue from './mergeHeader.vue';
  import EditVue from './edit.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      SortVue,
      TreeVue,
      CustomVue,
      MergeVue,MergeHeaderVue,
      EditVue,
      Preview
    }
  };
</script>

## Table 组件

基于 vxe-table 封装的表格组件，提供了丰富的功能和灵活的配置选项。支持分页、排序、多选、拖拽排序等功能，适用于展示大量结构化数据。

### 基础用法

基础的 Table 用法，展示了最基本的表格数据展示、分页、标题等功能。

<basic-vue/>
<preview  comp-name='table' demo-name='basic'/>

### 复杂场景

复杂 Table 用法。注意：selectionChange 和 selection 两者互斥，只能使用其中一个。selectionChange 常用于当前页数据的操作，selection 常用于全部数据的操作。

<sort-vue/>
<preview  comp-name='table' demo-name='sort'/>

### 树形表格

通过配置 tableOptions 的 treeConfig 属性，可以展示具有层级关系的数据结构。支持展开/收起、自定义图标等功能。

<tree-vue/>
<preview  comp-name='table' demo-name='tree'/>

### 自定义列配置

通过 slots 配置，可以自定义列的渲染内容，实现复杂的数据展示效果。支持使用 JSX 语法，可以灵活地组合各种 UI 组件。

<custom-vue/>
<preview  comp-name='table' demo-name='custom'/>

### 合并单元格示例

通过配置 merge-cells 方法实现行合并功能，适用于展示具有相同属性的数据行。

<merge-vue/>
<preview  comp-name='table' demo-name='merge'/>

### 合并表头示例

通过配置 tableColumns实现表头合并。

<merge-header-vue/>
<preview  comp-name='table' demo-name='mergeHeader'/>

### 编辑表格示例

通过配置 columns 的 slots 属性，可以实现单元格的编辑功能。支持输入框、下拉框等多种编辑形式，适用于需要直接在表格中修改数据的场景。

<edit-vue/>
<preview  comp-name='table' demo-name='edit'/>

### Attributes

| 参数              | 说明                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | 类型          | 可选值 | 默认值                                |
| ----------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------- | ------ | ------------------------------------- |
| tableDataSource   | 表格数据                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | array         | —      | —                                     |
| columns           | 表格列配置                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | array         | —      | —                                     |
| tableTitle        | 表格标题                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | string        | —      | —                                     |
| tableOptions      | 穿透表格属性，具体可查看[vxe-table](https://vxetable.cn/v3/#/grid/api)。常用属性包括：<br>- stripe: 是否带有斑马纹<br>- border: 是否带有边框<br>- showOverflow: 当内容过长时显示为省略号<br>- showHeaderOverflow: 表头内容过长显示为省略号<br>- highlightCurrentRow: 是否高亮当前行<br>- highlightHoverRow: 鼠标移到行是否要高亮显示<br>- size: 表格尺寸(medium / small / mini)<br>- align: 所有单元格对齐方式(left / center / right)<br>- headerAlign: 表头对齐方式(left / center / right) | object        | —      | —                                     |
| tableControls     | 表格操作栏的按钮                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | object        | —      | —                                     |
| selectionChange   | 监听多选事件变更                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | function      | —      | —                                     |
| sortConfig        | 排序配置                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | object        | —      | —                                     |
| sortChange        | 监听排序变更                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | function      | —      | —                                     |
| pagination        | 是否启用分页                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | boolean       | —      | true                                  |
| paginationOptions | 穿透分页属性                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | object        | —      | —                                     |
| paginationConfig  | 分页配置                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | object        | —      | —                                     |
| paginationConfig  | 分页参数                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | object        | —      | { total: 0, pageSize: 10, pageNo: 1 } |
| onChange          | 监听当前页数和 pageSize 变更                                                                                                                                                                                                                                                                                                                                                                                                                                                                | function      | —      | —                                     |
| loading           | 是否处于 loading 状态                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | boolean       | —      | false                                 |
| loadingSize       | loading 图标尺寸                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | string        | —      | medium                                |
| draggable         | 是否启用拖拽排序功能                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | boolean       | —      | false                                 |
| maxHeight         | 表格最大高度                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | string/number | —      | —                                     |
| rowKey            | 行数据的唯一标识字段                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | string        | —      | —                                     |
| toolbarsInTitle   | 是否将工具栏显示在标题栏中                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | boolean       | —      | false                                 |

### Events

| 事件名          | 说明                                       | 参数                                              |
| --------------- | ------------------------------------------ | ------------------------------------------------- |
| selectionChange | 当选择项发生变化时触发                     | selection: 已选择的行数据数组                     |
| sortChange      | 当表格排序条件发生变化时触发               | { field, order }: 排序字段和顺序                  |
| onChange        | 当分页参数（页码、每页条数）发生变化时触发 | { pageNo, pageSize }: 当前页码和每页条数          |
| cell-click      | 当单击单元格时触发                         | { row, column, cell }: 行数据、列配置、单元格元素 |
| cell-dblclick   | 当双击单元格时触发                         | { row, column, cell }: 行数据、列配置、单元格元素 |

### Slots

| 插槽名     | 说明                   |
| ---------- | ---------------------- |
| titleRight | 标题栏右侧的自定义内容 |

### columns 表格列配置

| 参数           | 说明                                                                                | 类型    | 可选值                                                                                      | 默认值 |
| -------------- | ----------------------------------------------------------------------------------- | ------- | ------------------------------------------------------------------------------------------- | ------ |
| title          | 表格列标题                                                                          | string  | —                                                                                           | —      |
| field          | 表格列字段名                                                                        | string  | —                                                                                           | —      |
| width          | 表格宽度                                                                            | string  | —                                                                                           | —      |
| slots          | 表格列插槽                                                                          | object  | {<br> default: ({ row }, h) => {<br> return row.xxx<br>}<br>}                               | —      |
| type           | 表格列类型                                                                          | string  | checkbox(复选框)/seq(序号)/actions(操作列)                                                  | —      |
| visible        | 是否显示表格列                                                                      | boolean | true/false                                                                                  | true   |
| fixed          | 表格列固定方向                                                                      | string  | left/right                                                                                  | —      |
| minShowActions | 表格操作列最低显示几个操作项                                                        | string  | —                                                                                           | 1      |
| actions        | 表格操作列配置                                                                      | array   | [{label:"查看",show:(row)=>1,onClick:(row,index,e)=>{}}]，如果是删除按钮添加theme: 'danger' | —      |
| ...            | vxe-grid 列 columns 参数 ，具体可查看[vxe-table](https://vxetable.cn/v3/#/grid/api) | —       | —                                                                                           | —      |

### tableControls 表格操作栏配置

| 参数     | 说明                                               | 类型     | 可选值 | 默认值 |
| -------- | -------------------------------------------------- | -------- | ------ | ------ |
| label    | 操作按钮名称                                       | string   | —      | —      |
| show     | 操作按钮是否显示,show:(row)=>{return 条件处理结果} | function | —      | —      |
| theme    | 操作按钮样式，一般为删除按钮设置为danger           | string   | —      | —      |
| icon     | 操作按钮图标名称                                   | string   | —      | —      |
| onClick  | 操作按钮绑定的事件                                 | function | —      | —      |
| disabled | 操作按钮是否禁用                                   | boolean  | —      | —      |
| ...      | XButton 按钮其他参数                               | —        | —      | —      |

### Methods

| 方法名            | 说明                   | 参数                                    |
| ----------------- | ---------------------- | --------------------------------------- |
| toggleCheckboxRow | 切换指定行的选中状态   | row: 行数据对象                         |
| setCheckboxRow    | 设置指定行的选中状态   | rows: 行数据对象数组, checked: 是否选中 |
| setAllCheckboxRow | 设置所有行的选中状态   | checked: 是否选中                       |
| clearCheckboxRow  | 清除所有行的选中状态   | —                                       |
| ...               | 兼容所有vxe-table的API | —                                       |

### 使用注意事项

1. **性能优化建议**

   - 当数据量较大时，建议开启虚拟滚动功能
   - 合理使用 showOverflow 配置，避免内容过多时的性能问题
   - 设置合适的 maxHeight，避免表格高度过大

2. **功能限制**

   - selectionChange 和 selection 属性互斥，只能使用其中一个
   - 启用拖拽排序功能时，需确保表格数据有唯一标识字段
   - 表格高度自适应时，需要考虑父容器的高度限制

3. **最佳实践**
   - 建议使用 rowKey 指定行数据的唯一标识
   - 大数据量场景下，建议使用服务端分页
   - 自定义列时，优先使用 slots 配置，而不是直接修改源码
