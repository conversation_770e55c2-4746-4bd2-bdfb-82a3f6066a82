<template>
  <div class="component-view">
    <x-table
      ref="customTable"
      title="自定义列配置示例"
      row-key="id"
      :table-data="tableDataSource"
      :columns="tableColumns"
      :pagination-config="paginationConfig"
    />
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      tableDataSource: [
        {
          id: 1,
          name: '项目A',
          status: 'processing',
          progress: 75,
          priority: 'high',
          startDate: '2024-01-01',
          endDate: '2024-06-30',
          leader: { name: '张三', avatar: 'https://avatars.githubusercontent.com/u/1?v=4' },
          tags: ['前端', 'Vue', 'Element'],
        },
        {
          id: 2,
          name: '项目B',
          status: 'pending',
          progress: 30,
          priority: 'medium',
          startDate: '2024-02-15',
          endDate: '2024-08-15',
          leader: { name: '李四', avatar: 'https://avatars.githubusercontent.com/u/2?v=4' },
          tags: ['后端', 'Java', 'Spring'],
        },
        {
          id: 3,
          name: '项目C',
          status: 'completed',
          progress: 100,
          priority: 'low',
          startDate: '2024-03-01',
          endDate: '2024-05-31',
          leader: { name: '王五', avatar: 'https://avatars.githubusercontent.com/u/3?v=4' },
          tags: ['移动端', 'React Native'],
        },
      ],
      paginationConfig: {
        total: 3,
        pageSize: 10,
        pageNo: 1,
      },
    };
  },
  computed: {
    tableColumns() {
      return [
        {
          title: '项目名称',
          field: 'name',
        },
        {
          title: '状态',
          field: 'status',
          width: 100,
          slots: {
            default: ({ row }) => {
              const statusMap = {
                processing: { text: '进行中', color: '#1890ff' },
                pending: { text: '待开始', color: '#faad14' },
                completed: { text: '已完成', color: '#52c41a' },
              };
              const status = statusMap[row.status];
              return <span style={`color: ${status.color}; font-weight: 500;`}>{status.text}</span>;
            },
          },
        },
        {
          title: '进度',
          field: 'progress',
          width: 200,
          slots: {
            default: ({ row }) => (
              <el-progress percentage={row.progress} status={row.progress === 100 ? 'success' : undefined} />
            ),
          },
        },
        {
          title: '优先级',
          field: 'priority',
          slots: {
            default: ({ row }) => {
              const priorityMap = {
                high: { text: '高', color: '#f5222d' },
                medium: { text: '中', color: '#faad14' },
                low: { text: '低', color: '#52c41a' },
              };
              const priority = priorityMap[row.priority];
              return (
                <el-tag size="small" color={priority.color} effect="dark">
                  {priority.text}
                </el-tag>
              );
            },
          },
        },
        {
          title: '项目周期',
          field: 'date',
          width: 220,
          slots: {
            default: ({ row }) => `${row.startDate} ~ ${row.endDate}`,
          },
        },
        {
          title: '负责人',
          field: 'leader',
          width: 120,
          slots: {
            default: ({ row }) => (
              <div style="display: flex; align-items: center;">
                <el-avatar size="small" src={row.leader.avatar} style="margin-right: 8px" />
                <span>{row.leader.name}</span>
              </div>
            ),
          },
        },
        {
          title: '技术标签',
          field: 'tags',
          width: 200,
          slots: {
            default: ({ row }) =>
              row.tags.map((tag) => (
                <el-tag size="small" style="margin-right: 4px" key={tag}>
                  {tag}
                </el-tag>
              )),
          },
        },
      ];
    },
  },
};
</script>
