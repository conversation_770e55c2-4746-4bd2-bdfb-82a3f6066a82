<template>
  <div class="component-view">
    <x-table
      ref="table"
      :table-data="tableData"
      :columns="columns"
      title="编辑表格示例"
      keep-source
      :table-options="tableOptions"
      :edit-config="{ trigger: 'manual', mode: 'row', showStatus: true, autoClear: false }"
    />
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      editingRow: null,
      tableData: [
        { id: 1, name: '张三', age: 25, status: '1', address: '北京市朝阳区' },
        { id: 2, name: '李四', age: 30, status: '0', address: '上海市浦东新区' },
        { id: 3, name: '王五', age: 28, status: '1', address: '广州市天河区' },
      ],
      tableOptions: {
        border: true,
        stripe: true,
        highlightHoverRow: true,
      },
      columns: [
        { type: 'seq', width: '60', title: '序号' },
        {
          field: 'name',
          title: '姓名',
          width: '120',
          editRender: {},
          slots: {
            edit: ({ row }, h) => <x-input vModel={row.name} type="text"></x-input>,
          },
        },
        {
          field: 'age',
          title: '年龄',
          width: '100',
          editRender: {},
          slots: {
            edit: ({ row }, h) => <x-input-number vModel={row.age} type="text"></x-input-number>,
          },
        },
        {
          field: 'status',
          title: '状态',
          width: '120',
        },
        {
          field: 'address',
          title: '地址',
        },
        {
          title: '操作',
          width: '150',
          type: 'actions',
          actions: [
            {
              label: '编辑',
              show: (row) => !this.hasEditStatus(row),
              onClick: (row) => {
                this.$refs.table.setEditRow(row);
              },
            },
            {
              label: '保存',
              isLoading: true,
              show: (row) => this.hasEditStatus(row),
              onClick: (row, index, { $event, startLoading, endLoading }) => {
                const $table = this.$refs.table;
                if ($table) {
                  $table.clearEdit().then(() => {
                    startLoading();
                    setTimeout(() => {
                      endLoading();
                      this.$message.success('保存成功！');
                    }, 2000);
                  });
                }
              },
            },
            {
              label: '取消',
              show: (row) => this.hasEditStatus(row),
              onClick: (row, index, e) => {
                const $table = this.$refs.table;
                if ($table) {
                  $table.clearEdit();
                }
              },
            },
          ],
        },
      ],
    };
  },
  methods: {
    hasEditStatus(row) {
      const $table = this.$refs.table;
      if ($table) {
        return $table.isEditByRow(row);
      }
    },
  },
};
</script>
