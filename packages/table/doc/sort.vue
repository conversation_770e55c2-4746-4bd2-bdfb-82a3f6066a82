<template>
  <div class="component-view">
    <div class="operation-buttons">
      <el-button @click="$refs.advancedTable.toggleCheckboxRow(tableDataSource[1])">切换第二行选中</el-button>
      <el-button @click="$refs.advancedTable.setAllCheckboxRow(true)">全选</el-button>
      <el-button @click="$refs.advancedTable.clearCheckboxRow()">清空选择</el-button>
    </div>
    <div class="selected-info" v-if="selection.length">已选择 {{ selection.length }} 条数据</div>
    <x-table
      ref="advancedTable"
      title="高级表格示例"
      row-key="id"
      draggable
      border
      :table-data="tableDataSource"
      :columns="tableColumns"
      :selection-change="handleSelectionChange"
      :selection.sync="selection"
      :sort-config="{ trigger: 'cell', defaultSort: { field: 'age', order: 'desc' } }"
      :sort-change="handleSortChange"
      v-bind="tablePaginationConfig"
    />
  </div>
</template>
<script lang="jsx">
export default {
  data() {
    return {
      tableDataSource: [
        { id: 1, name: '张三', age: 25, city: '北京', status: '在职', salary: 12000 },
        { id: 2, name: '李四', age: 30, city: '上海', status: '在职', salary: 15000 },
        { id: 3, name: '王五', age: 28, city: '广州', status: '离职', salary: 13500 },
        { id: 4, name: '赵六', age: 35, city: '深圳', status: '在职', salary: 20000 },
        { id: 5, name: '钱七', age: 22, city: '杭州', status: '实习', salary: 8000 },
        { id: 6, name: '孙八', age: 27, city: '南京', status: '在职', salary: 14000 },
        { id: 7, name: '周九', age: 32, city: '武汉', status: '离职', salary: 16000 },
        { id: 8, name: '吴十', age: 29, city: '成都', status: '在职', salary: 17000 },
      ],
      paginationConfig: {
        total: 8,
        pageSize: 10,
        pageNo: 1,
      },
      selection: [],
    };
  },
  computed: {
    tableColumns() {
      return [
        { type: 'checkbox', width: 50 },
        { title: '序号', type: 'seq' },
        { title: '姓名', field: 'name', sortable: true },
        { title: '年龄', field: 'age', sortable: true },
        { title: '城市', field: 'city' },
        {
          title: '状态',
          field: 'status',
          width: 100,
          slots: {
            default: ({ row }) => (
              <el-tag
                type={
                  {
                    在职: 'success',
                    离职: 'danger',
                    实习: 'warning',
                  }[row.status]
                }
              >
                {row.status}
              </el-tag>
            ),
          },
        },
        { title: '薪资', field: 'salary', sortable: true },
        {
          title: '操作',
          type: 'actions',
          width: 150,
          actions: [
            {
              label: '查看',
              onClick: (row, index, e) => {},
            },
            {
              label: '编辑',
              onClick: (row, index, e) => {},
            },
            {
              label: '撤回',
              onClick: (row, index, e) => {},
            },
            {
              label: '发布',
              onClick: (row, index, e) => {},
            },
            {
              label: '删除',
              theme: 'danger',
              onClick: (row, index, e) => {},
            },
          ],
        },
      ];
    },
    tablePaginationConfig() {
      return {
        paginationConfig: this.paginationConfig,
        onChange: (pageInfo) => this.handleSettingChange(pageInfo),
      };
    },
    sortConfig() {
      return {
        remote: true,
        trigger: 'cell',
      };
    },
  },
  created() {
    this.search();
  },
  methods: {
    // 翻页和修改每页的条数
    handleSettingChange(pageInfo) {
      const { current, pageSize } = pageInfo;
      this.paginationConfig.pageNo = current;
      this.paginationConfig.pageSize = pageSize;
      this.search();
    },
    // search组件调用列表接口
    search(cb) {
      // 调用接口获取表格数据
      const records = [];
      for (let i = 0; i < this.paginationConfig.pageSize; i++) {
        records.push({
          id: i + 1,
          name: `姓名${i}`,
          age: i + 21,
          city: `111${i}`,
          salary: 2131 * i,
          status: ['在职', '离职', '实习'][Math.floor(Math.random() * 3)],
        });
      }

      this.paginationConfig.total = records.length;
      this.tableDataSource = records || [];
      // eslint-disable-next-line no-unused-expressions
      cb && cb();
    },
    // 全选事件
    handleSelectionChange(val) {
      console.log('%c [ 全选事件 ]-200-「BaseTable」', 'font-size:13px; background:pink; color:#bf2c9f;', val);
    },
    /**
     * 排序事件
     * @param {*} sortEvent
     * @param {*} callback  回调必须，去更新排序选中状态
     */
    handleSortChange(sortEvent, callback) {
      console.log('%c [  ]-340-「BaseTable」', 'font-size:13px; background:pink; color:#bf2c9f;', sortEvent);
      // 一般情况下排序是与服务器交互 根据不同条件传不同参数调用接口
      switch (sortEvent?.property) {
        case 'a':
          // a列的排序 设置传参
          this.search(() => {
            callback();
          });
          break;
        case 'b':
          // b列的排序 设置传参
          this.search(() => {
            callback();
          });
          break;
        default:
      }
    },
  },
};
</script>
