<template>
  <div class="component-view">
    <x-table
      ref="baseTable"
      title="我是标题"
      toolbars-in-title
      minHeight="600px"
      :toolbarConfig="{ custom: true }"
      :toolbars="toolbars"
      :table-data.sync="tableDataSource"
      :columns="searchTableConfig.tableColumnsConfig"
      v-bind="tablePaginationConfig"
    >
      <div slot="titleRight">我是标题右边插槽</div>
    </x-table>
  </div>
</template>
<script lang="jsx">
export default {
  data() {
    return {
      loading: false,
      tableDataSource: [], // 渲染表格的数据源
      paginationConfig: {
        total: 0,
        pageSize: 10,
        pageNo: 1,
        pageSizeOptions: [10, 20, 30, 40, 50],
      },
    };
  },
  computed: {
    // 分页控件的配置
    tablePaginationConfig() {
      return {
        loading: this.loading,
        paginationConfig: this.paginationConfig,
        onChange: (pageInfo) => this.handleSettingChange(pageInfo),
      };
    },
    toolbars() {
      return [
        {
          label: '添加',
          type: 'primary',
          onClick: () => {
            const selected = this.$refs.baseTable.getRadioRecord();
            console.log(
              '%c [  ]-56-「basic」',
              'font-size:13px; background:pink; color:#bf2c9f;',
              this.$refs.baseTable.$refs.table.getRadioRecord,
            );
          },
        },
      ];
    },
    searchTableConfig() {
      return {
        // 表格列配置
        tableColumnsConfig: [
          { type: 'radio' },
          {
            title: '序号',
            type: 'seq',
            width: 50,
          },
          {
            title: '预警信息标题',
            field: 'title',
            width: 150,
            filters: [{ data: '' }],
            filterRender: {
              name: 'VxeInput',
            },
          },
          {
            title: '审核状态',
            field: 'status',
            slots: {
              default: ({ row }) => (
                <el-tag type={{ 0: 'default', 1: 'success' }[row.status]}>{['未审核', '审核通过'][row.status]}</el-tag>
              ),
            },
            filters: [
              { label: '未审核', value: 0 },
              { label: '审核通过', value: 1 },
            ],
          },
          {
            title: 'abcdefghij',
            field: 'type',
            // visible: false
          },
          {
            title: '联系方式',
            field: 'telephone',
          },
          {
            title: '联系方式',
            field: 'title1',
          },
          {
            title: '联系方式',
            field: 'telephone1',
            width: 200,
          },
          {
            title: '联系方式',
            field: 'title2',
          },

          {
            type: 'actions',
            title: '操作',
            fixed: 'right',
            actions: [
              {
                label: '查看',
                isLoading: true,
                onClick: (row, index, e) => {
                  console.log(
                    '%c [  ]-91-「BaseTable」',
                    'font-size:13px; background:pink; color:#bf2c9f;',
                    row,
                    index,
                    e,
                  );
                  setTimeout(() => {
                    e.endLoading();
                  }, 2000);
                },
              },
              {
                label: '编辑',
                show: (row, index) => index % 2 === 0,
                onClick: (row, index, e) => {
                  console.log(
                    '%c [  ]-91-「BaseTable」',
                    'font-size:13px; background:pink; color:#bf2c9f;',
                    row,
                    index,
                    e,
                  );
                },
              },
              {
                label: '撤回',
                onClick: (row, index, e) => {
                  console.log(
                    '%c [  ]-91-「BaseTable」',
                    'font-size:13px; background:pink; color:#bf2c9f;',
                    row,
                    index,
                    e,
                  );
                },
              },
              {
                label: '发布',
                onClick: (row, index, e) => {
                  console.log(
                    '%c [  ]-91-「BaseTable」',
                    'font-size:13px; background:pink; color:#bf2c9f;',
                    row,
                    index,
                    e,
                  );
                },
              },
              {
                label: '删除删除删除',
                theme: 'danger',
                onClick: (row, index, e) => {
                  console.log(
                    '%c [  ]-91-「BaseTable」',
                    'font-size:13px; background:pink; color:#bf2c9f;',
                    row,
                    index,
                    e,
                  );
                  this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                  })
                    .then(() => {
                      this.$message({
                        type: 'success',
                        message: '删除成功!',
                      });
                    })
                    .catch(() => {
                      this.$message({
                        type: 'info',
                        message: '已取消删除',
                      });
                    });
                },
              },
            ],
          },
        ],
      };
    },
  },
  created() {
    this.search();
  },
  methods: {
    // 翻页和修改每页的条数
    async handleSettingChange(pageInfo) {
      const { current, pageSize } = pageInfo;
      this.loading = true;
      this.paginationConfig.pageNo = current;
      this.paginationConfig.pageSize = pageSize;
      this.search();
      setTimeout(() => {
        this.loading = false;
      }, 1000);
    },
    // search组件调用列表接口
    search() {
      const records = [];
      for (let i = 0; i < 10; i++) {
        records.push({
          title: `标题${i}`,
          type: i,
          telephone: 13331212322 + i,
          status: i % 2,
        });
      }

      this.paginationConfig.total = records.length;
      this.tableDataSource = records || [];
    },
  },
};
</script>
