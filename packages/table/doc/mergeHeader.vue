<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-28 11:50:53
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-28 11:55:09
 * @FilePath: /vite-element-components/packages/table/doc/mergeHeader.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <x-table
      ref="table"
      :data="tableData"
      :columns="tableColumns"
      :table-options="{
        border: true,
      }"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        {
          date: '2024-01-01',
          name: '张三',
          province: '浙江',
          city: '杭州',
          address: '西湖区',
          zip: 310000,
          score: 98,
        },
        {
          date: '2024-01-02',
          name: '李四',
          province: '江苏',
          city: '南京',
          address: '鼓楼区',
          zip: 210000,
          score: 87,
        },
        {
          date: '2024-01-03',
          name: '王五',
          province: '广东',
          city: '深圳',
          address: '南山区',
          zip: 518000,
          score: 92,
        },
      ],
      tableColumns: [
        {
          title: '日期',
          field: 'date',
          width: 120,
        },
        {
          title: '用户信息',
          children: [
            {
              title: '姓名',
              field: 'name',
              width: 100,
            },
            {
              title: '得分',
              field: 'score',
              width: 80,
            },
          ],
        },
        {
          title: '地址信息',
          children: [
            {
              title: '省份/城市',
              children: [
                {
                  title: '省份',
                  field: 'province',
                },
                {
                  title: '城市',
                  field: 'city',
                },
              ],
            },
            {
              title: '详细地址',
              field: 'address',
            },
            {
              title: '邮编',
              field: 'zip',
            },
          ],
        },
      ],
    };
  },
};
</script>
