<!--
 * @Description: 表格组件，基于vxe-table封装，支持分页、排序、多选、拖拽排序等功能
 * @Version: 1.0.0
 * @Author: <PERSON>yer
 * @Date: 2024-06-8 16:37:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-07 17:59:31
-->
<script lang="jsx">
import 'xe-utils';
import sortable from 'sortablejs';
import cloneDeep from 'lodash/cloneDeep';
import debounce from 'lodash/debounce';
import throttle from 'lodash/throttle';
import { generateRandomString } from '../../src/utils';
import XButton from '../button/index.vue';
import XEmpty from '../empty/index.vue';
import 'vxe-pc-ui/lib/style.css';
import './index.scss';

// VXE 组件注册已移至组件库入口文件 packages/index.js
// 这样可以确保在库模式下正确注册到使用者的 Vue 实例中

/**
 * @name XTable
 * @description 基于vxe-table封装的表格组件，提供了丰富的功能和灵活的配置选项
 */
export default {
  name: 'XTable',
  components: { XButton, XEmpty },
  props: {
    /**
     * @description 表格标题
     * @type {String}
     * @default ''
     */
    title: {
      type: String,
      default: '',
    },
    /**
     * @description 穿透表格属性，支持vxe-table的所有配置项
     * @type {Object}
     * @default {}
     */
    tableOptions: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 多选事件变更回调函数
     * @type {Function}
     * @default null
     */
    selectionChange: {
      type: Function,
      default: null,
    },
    /**
     * @description 多选数据的rowKey对应的值数组
     * @type {Array}
     * @default []
     */
    selection: {
      type: Array,
      default: () => [],
    },
    /**
     * @description 排序配置项
     * @type {Object}
     * @default {}
     */
    sortConfig: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 排序变更回调函数
     * @type {Function}
     * @default null
     */
    sortChange: {
      type: Function,
      default: null,
    },
    /**
     * @description 表格列配置
     * @type {Array}
     * @default []
     */
    columns: {
      type: Array,
      default: () => [],
    },
    /**
     * @description 表格数据源
     * @type {Array}
     * @default []
     */
    tableData: {
      type: Array,
      default: () => [],
    },
    /**
     * @description 是否启用分页
     * @type {Boolean}
     * @default true
     */
    pagination: {
      type: Boolean,
      default: true,
    },
    /**
     * @description 分页配置参数
     * @type {Object}
     * @default null
     */
    paginationConfig: {
      type: Object,
      default: null,
    },
    /**
     * @description 分页变更回调函数
     * @type {Function}
     * @default null
     */
    onChange: {
      type: Function,
      default: null,
    },
    /**
     * @description 是否显示加载状态
     * @type {Boolean}
     * @default false
     */
    loading: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 加载提示文字
     * @type {String}
     * @default '加载中...'
     */
    loadingText: {
      type: String,
      default: '加载中...',
    },
    /**
     * @description 是否必须有数据
     * @type {Boolean}
     * @default false
     */
    isRequired: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 必填校验提示文字
     * @type {String}
     * @default '至少需要添加一条数据'
     */
    requiredMessage: {
      type: String,
      default: '至少需要添加一条数据',
    },
    /**
     * @description 工具栏按钮配置
     * @type {Array|Function}
     * @default null
     */
    toolbars: {
      type: [Array, Function],
      default: null,
    },
    /**
     * @description 工具栏是否显示在标题内部
     * @type {Boolean}
     * @default false
     */
    toolbarsInTitle: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 是否允许拖动排序
     * @type {Boolean}
     * @default false
     */
    draggable: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 行数据的唯一标识字段
     * @type {String}
     * @default 'id'
     */
    rowKey: {
      type: String,
      default: 'id',
    },
    /**
     * @description 是否允许编辑表格
     * @type {Boolean}
     * @default false
     */
    isEditTable: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 表格边框类型
     * @type {Boolean|String}
     * @default 'none'
     */
    border: {
      type: [Boolean, String],
      default: 'none',
    },
    /**
     * @description 表格内容是否换行展示
     * @type {Boolean}
     * @default true
     */
    showOverflow: {
      type: [Boolean, String],
      default: 'tooltip',
    },
    /**
     * @description 表格高度
     * @type {String|Number}
     * @default ''
     */
    height: {
      type: [String, Number],
      default: '',
    },
    /**
     * @description 是否显示斑马纹
     * @type {Boolean}
     * @default true
     */
    stripe: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      tooltipArr: {},
      isMultiLevelHeader: false,
      isValidated: false,
      validateResult: false,
      xTableId: generateRandomString(16),
      defaultPaginationConfig: {
        total: 0,
        pageSize: 10,
        pageNo: 1,
        pageSizeOptions: [10, 20, 30, 50, 100],
      },
    };
  },
  computed: {
    tooltipArrKeys: {
      cache: true, // 显式启用缓存
      get() {
        return Object.keys(this.tooltipArr);
      },
    },
    // 表格配置
    _tableOptions() {
      const { rowConfig, columnConfig, ...otherAttrs } = this.$attrs;
      return {
        ...this.tableOptions,
        props: {
          ...(this.tableOptions.props || {}),
          data: this.renderTableData,
          rowConfig: {
            useKey: true,
            keyField: this.rowKey,
            ...(this.tableOptions?.props?.rowConfig || {}),
            ...rowConfig,
          },
          columns: this.renderColumnsOptions(this.columns),
          columnConfig: {
            resizable: true,
            ...columnConfig,
            ...(this.tableOptions?.props?.columnConfig || {}),
          },
          ...otherAttrs,
        },
      };
    },
    // 自定义排序配置
    customSortConfig() {
      return {
        remote: true,
        trigger: 'cell',
        ...this.sortConfig,
      };
    },
    // 渲染的表格数据
    renderTableData: {
      cache: true,
      get() {
        return this.tableData;
      },
      set(val) {
        this.$emit('update:tableData', val);
      },
    },
    // 多选数据的变更
    selectionArray: {
      get() {
        return this.selection;
      },
      set(val) {
        this.$emit('update:selection', val);
      },
    },
  },
  watch: {
    tableData: {
      handler: debounce(function () {
        if (this.selection?.length) {
          // 只有存在选中数据时才执行
          this.handleCheckBoxRow();
        }
      }, 200),
      deep: false, // 明确不需要深度监听
    },
  },
  mounted() {
    // 初始化多选数据
    this.handleCheckBoxRow();
    // 往该组件上绑定vxe-table的方法
    this.forwardMethods.call(this, this.$refs.table);
    // 将拖拽初始化移到这里，确保 DOM 已经渲染
    if (this.draggable) {
      this.handleDraggable();
    }
  },
  beforeDestroy() {
    // 清理所有可能的内存占用
    if (this.sortable) {
      this.sortable.destroy();
    }
    this._columnsCache = null;
    this._columnsCacheKey = null;
    this.tooltipArr = {};
  },
  methods: {
    /**
     * @description 初始化多选数据，根据selection属性设置表格的选中状态
     */
    handleCheckBoxRow() {
      this.$nextTick(() => {
        if (this.selectionArray.length > 0) {
          this.selectionArray.forEach((selectId) => {
            const selectRow = this.renderTableData.find((_) => _[this.rowKey] === selectId);

            if (selectRow) {
              this.$refs.table.setCheckboxRow(selectRow, true);
            }
          });
        }
      });
    },
    /**
     * @description 初始化拖拽排序功能
     */
    handleDraggable() {
      this.$nextTick(() => {
        const xTable = this.$refs.table;
        this.sortable = sortable.create(
          xTable.$el.querySelector('.body--wrapper>.vxe-table--body-inner-wrapper tbody'),
          {
            handle: '.x-table__drag-btn',
            onEnd: (context) => {
              const { oldIndex, newIndex } = context;

              const originTableData = cloneDeep(this.renderTableData); // 当前页原始数据
              // 排序的行
              const sortedRow = this.renderTableData.splice(oldIndex, 1)[0];
              // 被插入的行
              const beSortedRow = originTableData[newIndex];

              this.renderTableData.splice(newIndex, 0, sortedRow);
              this.$emit('onDragEnd', {
                ...context,
                sortedRow,
                beSortedRow,
                tableData: this.renderTableData,
              });
            },
          },
        );
      });
    },
    /**
     * @description 计算列宽，根据标题文字长度自动计算
     * @param {string} title 列标题
     * @returns {string} 计算后的列宽
     */
    calculateColumnWidth: (function () {
      const cache = new Map();
      return function (title) {
        if (!title) return '';
        if (cache.has(title)) return cache.get(title);

        const charCount = [...title].reduce((count, char) => count + (char.match(/[\u4e00-\u9fa5]/) ? 2 : 1), 0);
        const width = charCount * 10 + 20;
        cache.set(title, width);
        return width;
      };
    })(),
    /**
     * @description 渲染表格列配置
     * @param {Array} data 列配置数组
     * @returns {Array} 处理后的列配置数组
     */
    renderColumnsOptions(data) {
      // 添加缓存机制
      if (this._columnsCache && this._columnsCacheKey === JSON.stringify(data)) {
        return this._columnsCache;
      }

      const columns = [...data];
      if (this.draggable) {
        columns.unshift({
          title: '排序',
          width: 50,
          type: 'move',
          align: 'center',
          slots: {
            default: () => <i class="x-table__drag-btn el-icon-rank" style="cursor:move" />,
          },
        });
      }
      const result = columns.map((column) => {
        let m = {};
        let minWidth = '';

        switch (column.type) {
          case 'move':
            // 拖拽排序
            m = {
              width: 50,
              ...column,
            };
            break;
          case 'seq':
            // 序号列
            m = {
              width: 50,
              title: column.label,
              ...column,
            };
            break;
          case 'checkbox':
            // 复选框
            m = {
              width: 43,
              ...column,
            };
            break;
          case 'actions':
            // 操作栏
            minWidth = column.actions ? ((column.minShowActions || 2) + 1) * 52 : '';
            m = {
              field: '操作',
              minWidth,
              ...column,
              type: undefined,
              slots: {
                default: ({ row, rowIndex }) =>
                  this.renderTooltipWrap(rowIndex, this.renderButtons(column, row, rowIndex)),
              },
            };
            break;
          default:
            // 普通列
            // eslint-disable-next-line no-case-declarations

            m = {
              minWidth: this.calculateColumnWidth(column.title),
              ...column,
              formatter: (options) => {
                const { cellValue } = options;
                if (column.formatter) return column.formatter(options);
                if (column.isMoney) return Number(cellValue).toFixed(2);
                if (cellValue === '0' || cellValue === 0) return '0';
                if (!cellValue) return '-';
                return cellValue;
              },
            };
            break;
        }

        if (column?.children && Array.isArray(column.children)) {
          this.isMultiLevelHeader = true;
          m.children = this.renderColumnsOptions(column.children);
          m.minWidth = null;
        }
        return m;
      });
      // 缓存结果
      this._columnsCache = result;
      this._columnsCacheKey = JSON.stringify(data);
      return result;
    },
    /**
     * @description 获取可见的操作按钮列表
     * @param {Object} config 按钮配置
     * @param {Object} row 行数据
     * @param {Number} index 行索引
     * @returns {Object} 处理后的按钮列表
     */
    getVisibleButtons(config, row, index) {
      const visibleActions = config.actions.filter((action) => (action.show ? action.show(row, index) : true));
      const minShowActions = config.minShowActions || 2;
      const moreButtons = visibleActions.slice(minShowActions, visibleActions.length);
      const normalButtonLength = moreButtons.length === 1 ? minShowActions + 1 : minShowActions;

      return {
        normalButtons: visibleActions.slice(0, Math.min(visibleActions.length, normalButtonLength)),
        moreButtons: moreButtons.length > 1 ? moreButtons : [],
      };
    },

    /**
     * @description 渲染普通按钮
     * @param {Array} buttons 按钮配置数组
     * @param {Object} row 行数据
     * @param {Number} index 行索引
     * @returns {Array} 按钮节点数组
     */
    renderNormalButtons(buttons, row, index) {
      return buttons.map((action) => {
        const buttonKey = `${action.label}_${row[this.rowKey]}`;
        const { show, ...otherPrams } = action;
        return (
          <x-button
            key={buttonKey}
            type="text"
            css="max-width:100%;"
            size="medium"
            {...{
              attrs: {
                ...otherPrams,
              },
              scopedSlots: {
                rightSlot: () => (action.rightIcon ? action.rightIcon(row) : ''),
              },
              on: {
                ...action,
              },
            }}
            params={[row, index]}
          />
        );
      });
    },

    /**
     * @description 渲染更多按钮下拉菜单
     * @param {Array} buttons 按钮配置数组
     * @param {Object} row 行数据
     * @param {Number} index 行索引
     * @returns {VNode} 下拉菜单节点
     */
    renderMoreButtonDropdown(buttons, row, index) {
      if (!buttons.length) return '';
      // trigger="click"

      return (
        <el-dropdown
          maxColumnWidth="120px"
          style="margin-left:8px;"
          oncommand={(command) => {
            const button = buttons.find((btn) => btn.label === command);
            if (button) {
              button.onClick(row, index);
            }
          }}
        >
          <el-button type="text" size="medium">
            <span>
              更多
              <i class="el-icon-arrow-down" />
            </span>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            {buttons.map((action) => {
              const { show, ...otherPrams } = action;
              return (
                <el-dropdown-item class="x-table__action-btn-wrapper">
                  <x-button
                    type="text"
                    class="x-table__action-btn"
                    params={[row, index]}
                    {...{
                      attrs: { ...otherPrams },
                    }}
                  />
                </el-dropdown-item>
              );
            })}
          </el-dropdown-menu>
        </el-dropdown>
      );
    },

    /**
     * @description 渲染操作按钮组
     * @param {Object} config 按钮配置
     * @param {Object} row 行数据
     * @param {Number} index 行索引
     * @returns {Array} 按钮组节点数组
     */
    renderButtons(config, row, index) {
      const { normalButtons, moreButtons } = this.getVisibleButtons(config, row, index);
      return [
        this.renderNormalButtons(normalButtons, row, index),
        this.renderMoreButtonDropdown(moreButtons, row, index),
      ];
    },
    // 渲染超长文字按钮
    renderTooltipWrap(rowIndex, vNode) {
      // 使用节流处理鼠标移入事件
      const handleMouseEnter = throttle((e) => {
        const target = e.target?.childNodes[0];
        if (!target) return;
        const text = vNode?.map((_) => _?.elm?.textContent).join();

        if (target.scrollWidth > target.offsetWidth && text) {
          this.$set(this.tooltipArr, rowIndex, text);
        } else {
          this.$delete(this.tooltipArr, rowIndex);
        }
      }, 200);

      return [
        <el-tooltip
          content={this.tooltipArr[rowIndex]}
          placement="top"
          disabled={!this.tooltipArrKeys.includes(String(rowIndex))}
        >
          <div class="x-table__tooltip-wrap" style="overflow:hide;" on-mouseenter={handleMouseEnter}>
            {vNode}
          </div>
        </el-tooltip>,
      ];
    },
    // 获取数组的差集
    getArrayDifference(array1, array2) {
      const difference = array1.filter((item) => !array2.includes(item));
      return difference;
    },
    // 暴露多选方法
    handleSelectionChange({ records }) {
      const selects = records.map((item) => item[this.rowKey] || item);
      // eslint-disable-next-line no-unused-expressions
      this.selectionChange && this.selectionChange(records, selects);

      // 当存在表格复选编辑时
      const pageNoIdArray = this.renderTableData.map((row) => row[this.rowKey]); // 当前页的id数组
      const otherPageSelected = this.getArrayDifference(this.selectionArray, pageNoIdArray); // 通过id去寻找选中数据中，不在当前页的数据
      this.selectionArray = Array.from(new Set([...selects, ...otherPageSelected])).sort((a, b) => b - a);
    },
    // 暴露排序方法
    handleSortChange(val, cb) {
      // eslint-disable-next-line no-unused-expressions
      this.sortChange && this.sortChange(val, cb);
    },
    // 使用防抖处理频繁触发的事件
    handlePaginationChange(pageInfo) {
      if (this.onChange) {
        this.onChange(pageInfo);
      }
    },
    // 绑定子组件的方法
    forwardMethods(childComponent) {
      const { methods } = childComponent.$options; // 获取子组件的方法
      // 遍历子组件的方法
      // eslint-disable-next-line guard-for-in
      for (const methodName in methods) {
        // 使用闭包创建一个新的函数，用于转发调用子组件的方法
        this[methodName] = (...args) => childComponent[methodName](...args);
      }
    },
    // 渲染表头
    renderTitle() {
      const tableHeaderClass = {
        'x-table__header': true,
        'x-table__header-toolbars': this.toolbarsInTitle,
      };
      return (
        <div class={tableHeaderClass}>
          <div class="x-table__header-title">
            {this.title}
            {this.isRequired && [
              <label class="x-table__required">(*必填)</label>,
              this.isValidated && !this.validateResult && (
                <label class="x-table__required-message">{this.requiredMessage}</label>
              ),
            ]}
          </div>
          {this.$slots?.titleRight ? <div class="x-table__header-right">{this.$slots.titleRight}</div> : ''}
          {this.toolbarsInTitle && this.renderToolbars()}
        </div>
      );
    },
    /**
     * @description 获取工具栏按钮的默认配置
     * @param {Object} action 按钮配置
     * @returns {Object} 处理后的按钮配置
     */
    getToolbarConfig(action) {
      return {
        css: 'max-width:100%;',
        type: 'primary',
        ...action,
      };
    },

    /**
     * @description 渲染工具栏
     * @returns {VNode|null} 工具栏节点
     */
    renderToolbars() {
      if (!this.toolbars?.length) return null;

      return (
        <div class="x-table__toolbars">
          {this.toolbars.map((action) => (
            <x-button key={action.label || action.text} {...{ attrs: this.getToolbarConfig(action) }} />
          ))}
        </div>
      );
    },

    /**
     * @description 校验表格内容
     * @returns {Boolean} 校验结果
     */
    validateTable() {
      this.isValidated = true;
      this.validateResult = Boolean(this.renderTableData.length);
      return this.validateResult;
    },

    /**
     * @description 处理表格排序变化
     * @param {Object} sortEvent 排序事件对象
     */
    handleTableSort(sortEvent) {
      this.handleSortChange(sortEvent, () => {
        setTimeout(() => {
          if (!sortEvent?.property || !sortEvent?.order) {
            return this.$refs.table.clearSort();
          }
          this.$refs.table.sort(sortEvent?.property, sortEvent?.order);
        }, 10);
      });
    },

    /**
     * @description 渲染分页器
     * @returns {VNode|String} 分页器节点
     */
    renderPagination() {
      if (!this.pagination) return '';
      const defaultTotal = this.paginationConfig ? this.paginationConfig.total : this.tableData.length;
      const finalPaginationConfig = {
        ...this.defaultPaginationConfig,
        total: defaultTotal,
        ...this.paginationConfig,
      };
      return (
        <x-pagination
          pageSizes={finalPaginationConfig.pageSizeOptions}
          show-sizer
          background
          onChange={this.handlePaginationChange}
          vModel={finalPaginationConfig.pageNo}
          pageSize={finalPaginationConfig.pageSize}
          total={finalPaginationConfig.total}
        />
      );
    },
  },
  render() {
    return (
      <div class={['x-table', !this.pagination ? 'x-table__no-pagination' : '']} id={this.xTableId}>
        {this.title && this.renderTitle()}
        {this.toolbars && !this.toolbarsInTitle && this.renderToolbars()}
        <vxe-grid
          ref="table"
          class="x-table__grid"
          show-overflow={this.showOverflow}
          scroll-y={{ mode: 'virtual', gt: 100 }}
          scroll-x={{ mode: 'virtual' }}
          empty-render={{ name: 'NotData' }}
          sort-config={this.customSortConfig}
          tooltip-config={{ enterable: true }}
          border={this.isMultiLevelHeader ? true : this.border}
          height={this.height}
          loading={this.loading}
          loading-config={{
            text: this.loadingText,
          }}
          stripe={this.stripe}
          auto-resize
          on-sort-change={this.handleTableSort}
          on-checkbox-all={this.handleSelectionChange}
          on-checkbox-change={this.handleSelectionChange}
          {...{
            scopedSlots: {
              empty: () => <x-empty size={160} />,
              pager: () => this.renderPagination(),
            },
            ...this._tableOptions,
            on: {
              ...this.$listeners,
            },
          }}
        />
      </div>
    );
  },
};
</script>

<style lang="scss">
@import '../styles/index.scss';

.x-table {
  background-color: #fff;
  border-radius: 6px;
  padding: 12px 15px 0;
  &__header {
    background: inherit;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    border-radius: 3px;
    &-title {
      font-size: 16px;
      font-weight: 600;
      flex-grow: 1;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    &-toolbars {
      .x-table__toolbars {
        padding-bottom: 0;
      }
    }
    &-right {
      display: flex;
      align-items: center;
    }
  }

  &__required {
    font: 12px;
    margin-left: 4px;
    color: #ff4d4f;
    &-message {
      font: 12px;
      margin-left: 4px;
      color: #ff4d4f;
    }
  }
  &__toolbars {
    background: inherit;
    padding-bottom: 12px;
    display: flex;
    justify-content: flex-end;
    .x-button:not(:first-child) {
      margin-left: 12px;
    }
  }
  &__action {
    .el-dropdown__item.el-dropdown__item--theme-default {
      color: var(--color-primary);
    }
    &-btn {
      width: 100%;
      text-align: left !important;
      &-wrapper {
        padding: 0 15px !important;
        &:hover,
        &:active {
          background-color: rgba(0, 0, 0, 0.04);
        }
      }
    }
  }
  .el-button {
    font-weight: 400;
  }
  .vxe-header--column {
    .vxe-resizable {
      &::before,
      &::after {
        content: '';
        display: inline-block;
        vertical-align: middle;
      }
      &::before {
        width: 1px;
        height: 50%;
        background-color: #d9dddf;
      }
      &::after {
        width: 0;
        height: 100%;
      }
    }
  }
  .vxe-body--row.sortable-ghost,
  .vxe-body--row.sortable-chosen {
    box-shadow:
      0 1px 10px rgba(0, 0, 0, 0.05),
      0 4px 5px rgba(0, 0, 0, 8%),
      0 2px 4px -1px rgba(0, 0, 0, 12%);
  }
  .vxe-table {
    min-height: 200px;
    &:not(.border--full) {
      thead th,
      tbody td {
        vertical-align: middle;
      }
      .vxe-table--header-wrapper,
      .vxe-body--row.row--stripe {
        border-radius: 6px;
      }
      .vxe-table--fixed-wrapper {
        .fixed-left--wrapper.vxe-table--header-wrapper {
          border-radius: 6px 0 0 6px;
        }

        .fixed-right--wrapper.vxe-table--header-wrapper {
          border-radius: 0 6px 6px 0;
        }
      }
      tbody td {
        &:first-child {
          border-radius: 6px 0 0 6px;
        }
        &:last-child {
          border-radius: 0 6px 6px 0;
        }
      }
    }
  }
  &.x-table__no-pagination {
    padding-bottom: 16px;
  }
  .vxe-body--column.col--ellipsis > .vxe-cell {
    max-height: initial;
  }
  &__tooltip-wrap {
    > button {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-left: 0;
      padding-right: 0;
    }
    .el-button:not(:first-child) {
      margin-left: 8px;
    }
  }
  .vxe-table--body-wrapper {
    @include scrollbar();
  }
}
</style>
