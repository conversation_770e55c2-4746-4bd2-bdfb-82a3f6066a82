@import 'vxe-table/styles/cssvar.scss';
@import 'vxe-pc-ui/styles/cssvar.scss';
.x-table__grid {
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  ::-webkit-scrollbar-thumb {
    border: 0px solid transparent;
    background-clip: content-box;
    border-radius: 10px;
  }
  .vxe-table .vxe-table--scroll-y-top-corner {
    border-radius: 0 3px 3px 0;
  }
}
// 默认主题
html[data-vxe-ui-theme='light'] {
  --x-pagination-text-color: #096dd9;
  --x-pagination-bg-color: #e6f7ff;
  --x-gray-1: #f5f5f5;
  --vxe-ui-table-header-font-color: #15224c;
  --vxe-ui-font-primary-color: var(--brand-6, #0f45ea);
  --vxe-ui-table-header-font-weight: 500;
  --vxe-ui-table-header-background-color: #edf2f6;
  --vxe-ui-font-color: #2f446b;
  --vxe-ui-table-cell-negative-color: #ff4d4f;
  --vxe-ui-table-validate-error-color: #ff4d4f;
  --vxe-ui-table-cell-dirty-update-color: #ff4d4f;
  .x-table__grid {
    ::-webkit-scrollbar-track,
    ::-webkit-scrollbar-corner {
      background-color: #fff;
    }
    ::-webkit-scrollbar-thumb {
      background-color: #bfbfbf;
    }
    ::-webkit-scrollbar-thumb:hover,
    ::-webkit-scrollbar-thumb:active {
      background-color: #787878;
    }
  }
}
// 暗黑主题
html[data-vxe-ui-theme='dark'] {
  --x-pagination-text-color: var(--color-primary);
  --x-pagination-bg-color: #fff;
  --x-gray-1: #fff;
  --vxe-ui-font-color: #15224c;
  --vxe-ui-font-primary-color: var(--brand-6, #0f45ea);
  --vxe-ui-table-header-font-weight: 500;
  --vxe-ui-table-cell-negative-color: #ff4d4f;
  --vxe-ui-table-validate-error-color: #ff4d4f;
  --vxe-ui-table-cell-dirty-update-color: #ff4d4f;

  .x-table__grid {
    ::-webkit-scrollbar-track,
    ::-webkit-scrollbar-corner {
      background-color: #151518;
    }
    ::-webkit-scrollbar-thumb {
      background-color: #bfbfbf;
    }
    ::-webkit-scrollbar-thumb:hover,
    ::-webkit-scrollbar-thumb:active {
      background-color: #a3a6ad;
    }
  }
}
