/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-18 11:46:13
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 13:51:54
 * @FilePath: /vite-element-components/packages/index.js
 * @Description: 组件库入口文件
 */
// 导入版本号
import { version } from '../package.json';
// 公共指令
import directives from './shared/directives';

// VXE 组件
import { VxeUI, VxeTable, VxeColumn, VxeColgroup, VxeGrid, VxeToolbar } from 'vxe-table';
import {
  VxeTooltip,
  VxeLoading,
  VxeInput,
  VxeSelect,
  VxeNumberInput,
  VxeDatePicker,
  VxeButton,
  VxeRadioGroup,
} from 'vxe-pc-ui';
import zhCN from 'vxe-pc-ui/lib/language/zh-CN';
// 组件
import xButton from './button/index.js';
import xCascader from './cascader/index.js';
import xCheckbox from './checkbox/index.js';
import xCustom from './custom/index.js';
import xDatePicker from './datePicker/index.js';
import xDialog from './dialog/index.js';
import xEditTable from './editTable/index.js';
import xEmpty from './empty/index.js';
import xForm from './form/index.js';
import xFormItems from './formItems/index.js';
import xTitle from './title/index';
import xInput from './input/index';
import xInputNumber from './inputNumber/index';
import xInputNumberRange from './inputNumberRange/index.js';
import xPagination from './pagination/index.js';
import xRadio from './radio/index.js';
import xSearch from './search/index.js';
import xSearchTable from './searchTable/index.js';
import xSelect from './select/index.js';
import xSelectTree from './selectTree/index.js';
import xSwitch from './switch/index.js';
import xTable from './table/index.js';
import xText from './text/index';
import xToolbar from './toolbar/index.js';
import xTooltip from './tooltip/index.js';
import xTransfer from './transfer/index';
import xUpload from './upload/index.js';

const components = [
  xButton,
  xCascader,
  xCheckbox,
  xCustom,
  xDatePicker,
  xDialog,
  xEditTable,
  xEmpty,
  xForm,
  xFormItems,
  xTitle,
  xInput,
  xInputNumber,
  xInputNumberRange,
  xPagination,
  xRadio,
  xSearch,
  xSearchTable,
  xSelect,
  xSelectTree,
  xSwitch,
  xTable,
  xText,
  xToolbar,
  xTooltip,
  xTransfer,
  xUpload,
];
const install = (vue, options = {}) => {
  // 注册 VXE 组件
  vue
    .use(VxeTable)
    .use(VxeColumn)
    .use(VxeColgroup)
    .use(VxeGrid)
    .use(VxeTooltip)
    .use(VxeLoading)
    .use(VxeInput)
    .use(VxeSelect)
    .use(VxeNumberInput)
    .use(VxeDatePicker)
    .use(VxeToolbar)
    .use(VxeButton)
    .use(VxeRadioGroup);

  // 配置 VXE 语言和全局设置
  VxeUI.setI18n('zh-CN', zhCN);
  VxeUI.setLanguage('zh-CN');
  VxeUI.setConfig({
    zIndex: 4000, // 全局 zIndex 起始值，如果项目的的 z-index 样式值过大时就需要跟随设置更大，避免被遮挡
  });

  // 处理配置参数
  const config = {
    serveUrl: options.serveUrl || '',
    ...options,
  };
  // 将配置挂载到 Vue 原型上，供组件使用
  vue.prototype.$appConfig = config;
  components.forEach((component) => {
    vue.use(component);
  });
  directives.forEach(({ key, value }) => {
    vue.directive(key, value);
  });
};

export default {
  // 组件库版本号
  version,
  // 安装函数
  install,
  // 所有组件
  xButton,
  xCascader,
  xCheckbox,
  xCustom,
  xDatePicker,
  xDialog,
  xEditTable,
  xEmpty,
  xForm,
  xFormItems,
  xTitle,
  xInput,
  xInputNumber,
  xInputNumberRange,
  xPagination,
  xRadio,
  xSearch,
  xSearchTable,
  xSelect,
  xSelectTree,
  xSwitch,
  xTable,
  xText,
  xToolbar,
  xTooltip,
  xTransfer,
  xUpload,
};
