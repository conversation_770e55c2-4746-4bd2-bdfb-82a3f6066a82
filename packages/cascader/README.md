# XCascader 组件

基于 Element UI 的级联选择器组件进行二次封装，提供更便捷的配置方式和更丰富的功能。

## 快速开始

```vue
<template>
  <div>
    <!-- 基础用法 -->
    <x-cascader v-model="value" :options="options" />

    <!-- 只读模式 -->
    <x-cascader mode="view" v-model="value" :options="options" />

    <!-- 多选模式 -->
    <x-cascader v-model="multipleValue" :options="options" :props="{ multiple: true }" />

    <!-- 可搜索 -->
    <x-cascader v-model="value" :options="options" filterable placeholder="支持搜索" />

    <!-- 动态加载 -->
    <x-cascader v-model="lazyValue" :props="{ lazy: true, lazyLoad: loadData }" />

    <!-- 带参数的事件处理 -->
    <x-cascader v-model="value" :options="options" :params="{ id: 1, name: 'test' }" :onChange="handleChange" />

    <!-- 动态显示/禁用 -->
    <x-cascader
      v-model="value"
      :options="options"
      :params="userData"
      :show="(params) => params.role === 'admin'"
      :disabled="(params) => params.status === 'inactive'"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      value: [],
      multipleValue: [],
      lazyValue: [],
      userData: { role: 'admin', status: 'active' },
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                { value: 'yizhi', label: '一致' },
                { value: 'fankui', label: '反馈' },
              ],
            },
          ],
        },
      ],
    };
  },
  methods: {
    handleChange(value, params, context) {
      console.log('值改变:', value, params);
      // context 包含: $event, getCheckedNodes, clearCheckedNodes, formatValue
    },

    loadData(node, resolve) {
      setTimeout(() => {
        const nodes = [
          { value: 'leaf1', label: '叶子节点1', leaf: true },
          { value: 'leaf2', label: '叶子节点2', leaf: true },
        ];
        resolve(nodes);
      }, 1000);
    },
  },
};
</script>
```

## 特性

- 🎨 **丰富的模式** - 支持编辑和只读两种模式
- 🔧 **灵活配置** - 支持函数式的动态显示和禁用控制
- 📊 **多选支持** - 内置多选模式，支持标签折叠
- 🔍 **搜索功能** - 支持选项搜索和过滤
- ⚡ **动态加载** - 支持懒加载，适用于大数据量场景
- 📦 **参数传递** - 便捷的参数传递机制，适用于表格操作等场景
- 🔄 **事件处理** - 灵活的事件处理机制，支持自定义上下文

## 使用场景

- 需要通过配置对象统一管理级联选择器属性的场景
- 需要动态控制组件显示/隐藏、禁用状态的场景
- 需要处理多级分类数据选择的场景
- 需要传递额外参数到事件处理函数的场景
- 表格操作列中的动态级联选择配置
- 表单中的地区、分类等层级数据选择

## API

### Props

| 参数            | 说明                        | 类型                    | 可选值                        | 默认值 |
| --------------- | --------------------------- | ----------------------- | ----------------------------- | ------ |
| value / v-model | 绑定值                      | string / number / array | —                             | —      |
| options         | 可选项数据源                | array                   | —                             | []     |
| props           | 配置选项                    | object                  | —                             | {}     |
| size            | 尺寸                        | string                  | large / medium / small / mini | —      |
| mode            | 模式                        | string                  | edit / view                   | edit   |
| placeholder     | 输入框占位文本              | string                  | —                             | 请选择 |
| disabled        | 是否禁用                    | boolean / function      | —                             | false  |
| show            | 是否显示                    | boolean / function      | —                             | true   |
| clearable       | 是否可清空                  | boolean                 | —                             | false  |
| filterable      | 是否可搜索                  | boolean                 | —                             | false  |
| separator       | 选项分隔符                  | string                  | —                             | /      |
| showAllLevels   | 是否显示完整路径            | boolean                 | —                             | true   |
| collapseTags    | 多选时是否折叠标签          | boolean                 | —                             | false  |
| debounce        | 搜索防抖延迟(ms)            | number                  | —                             | 300    |
| beforeFilter    | 筛选前的钩子函数            | function                | —                             | —      |
| popperClass     | 自定义浮层类名              | string                  | —                             | —      |
| params          | 传递给事件的参数            | array / object          | —                             | []     |
| onChange        | 值改变事件处理函数          | function                | —                             | —      |
| onExpandChange  | 展开节点变化事件处理函数    | function                | —                             | —      |
| onBlur          | 失去焦点事件处理函数        | function                | —                             | —      |
| onFocus         | 获得焦点事件处理函数        | function                | —                             | —      |
| onVisibleChange | 下拉框显示/隐藏事件处理函数 | function                | —                             | —      |
| onRemoveTag     | 移除标签事件处理函数        | function                | —                             | —      |

### Events

| 事件名         | 说明                    | 回调参数           |
| -------------- | ----------------------- | ------------------ |
| change         | 值改变时触发            | (value, context)   |
| expand-change  | 展开节点发生变化时触发  | (value, context)   |
| blur           | 失去焦点时触发          | (event, context)   |
| focus          | 获得焦点时触发          | (event, context)   |
| visible-change | 下拉框出现/隐藏时触发   | (visible, context) |
| remove-tag     | 多选模式下移除Tag时触发 | (value, context)   |

### Methods

| 方法名            | 说明               | 参数                          |
| ----------------- | ------------------ | ----------------------------- |
| getCheckedNodes   | 获取选中的节点数组 | (leafOnly) 是否只返回叶子节点 |
| clearCheckedNodes | 清空选中的节点     | —                             |
| formatValue       | 格式化显示值       | —                             |
| getCascaderRef    | 获取级联选择器引用 | —                             |

### Slots

| 插槽名  | 说明                   | 参数           |
| ------- | ---------------------- | -------------- |
| default | 自定义备选项的节点内容 | { node, data } |
| empty   | 无数据时的内容         | —              |

## 注意事项

1. 在使用动态加载（lazy）模式时，必须配合 lazyLoad 方法使用
2. 多选模式下，建议配置 collapse-tags 属性以优化展示效果
3. 自定义节点内容时，通过 slot-scope 可以获取 node 和 data 两个参数
4. 在只读模式（mode="view"）下，级联选择器将以文本形式展示选中的值
5. 函数式的 show 和 disabled 属性会接收 params 作为参数，便于动态控制
