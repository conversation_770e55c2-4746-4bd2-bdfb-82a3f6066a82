<template>
  <div class="component-view">
    <p>
      仅输出选中节点{{ value0 }}
      <x-cascader
        v-model="value0"
        :options="cascaderOptions"
        :emit-path="false"
        :props="{
          label: 'name',
          checkStrictly: true,
        }"
      />
    </p>
    <p>
      普通级联输入框：{{ value }}
      <x-cascader v-model="value" :options="cascaderOptions" :props="props" />
    </p>
    <p>
      阅读状态级联：
      <x-cascader mode="view" v-model="value" :options="cascaderOptions" :props="props" />
    </p>
    <p>
      禁用级联输入框：
      <x-cascader disabled v-model="value" :options="cascaderOptions" :props="props" />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value0: 'shejiyuanze',
      value: [],
      props: {
        label: 'name',
      },
      cascaderOptions: [
        {
          value: 'zhinan',
          name: '设计',
          children: [
            {
              value: 'shejiyuan<PERSON>',
              name: '设计原则设计原则设计原则设计原则设计原则设计原则设计原则设计原则设计原则设计原则',
              children: [
                {
                  value: 'yizhi',
                  name: '一致',
                },
                {
                  value: 'fankui',
                  name: '反馈',
                },
              ],
            },
            {
              value: 'daohang',
              name: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  name: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  name: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          name: '组件',
          children: [
            {
              value: 'basic',
              name: 'Basic',
              children: [
                {
                  value: 'layout',
                  name: 'Layout 布局',
                },
                {
                  value: 'color',
                  name: 'Color 色彩',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          name: '资源',
          children: [
            {
              value: 'axure',
              name: 'Axure Components',
            },
            {
              value: 'sketch',
              name: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              name: '组件交互文档',
            },
          ],
        },
      ],
    };
  },
};
</script>
