<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:43:59
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-13 14:53:15
 * @FilePath: /vite-element-components/packages/cascader/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import MultipleVue from './multiple.vue';
  import SearchableVue from './searchable.vue';
  import LazyVue from './lazy.vue';
  import CustomVue from './custom.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      MultipleVue,
      SearchableVue,
      LazyVue,
      CustomVue,
      Preview
    }
  };
</script>

## Cascader 组件

级联选择器，用于处理多层级数据结构的选择场景。

### 基础用法

组件兼容 element-eoss 组件库 cascader 级联的所有属性。

<basic-vue/>
<preview  comp-name='cascader' demo-name='basic'/>

### 高级用法

#### 多选级联

支持多个选项的选择，通过设置 multiple 属性来启用。

<multiple-vue/>
<preview  comp-name='cascader' demo-name='multiple'/>

#### 可搜索

可以快速查找选项，通过设置 filterable 属性来启用。

<searchable-vue/>
<preview  comp-name='cascader' demo-name='searchable'/>

#### 动态加载

当选中某一级时，动态加载该级下的选项。

<lazy-vue/>
<preview  comp-name='cascader' demo-name='lazy'/>

#### 自定义节点内容

可以自定义备选项的节点内容，通过 slot-scope 可以获取到节点的数据。

<custom-vue/>
<preview  comp-name='cascader' demo-name='custom'/>

### Attributes

| 参数            | 说明                                                                                            | 类型                | 可选值                | 默认值  |
| --------------- | ----------------------------------------------------------------------------------------------- | ------------------- | --------------------- | ------- |
| value/v-model   | 选中项绑定值                                                                                    | string/number/array | —                     | —       |
| options         | 可选项数据源                                                                                    | array               | —                     | []      |
| props           | 配置选项，详见下表                                                                              | object              | —                     | —       |
| size            | 尺寸                                                                                            | string              | medium / small / mini | —       |
| mode            | 编辑模式，支持只读和编辑两种模式                                                                | string              | edit / readonly       | edit    |
| placeholder     | 输入框占位文本                                                                                  | string              | —                     | 请选择  |
| disabled        | 是否禁用                                                                                        | boolean             | —                     | false   |
| clearable       | 是否支持清空选项                                                                                | boolean             | —                     | false   |
| show-all-levels | 输入框中是否显示选中值的完整路径                                                                | boolean             | —                     | true    |
| collapse-tags   | 多选模式下是否折叠Tag                                                                           | boolean             | —                     | false   |
| separator       | 选项分隔符                                                                                      | string              | —                     | 斜杠'/' |
| emitPath        | 是否返回完整路径，为false时只返回最后一级的值                                                   | boolean             | —                     | true    |
| filterable      | 是否可搜索选项                                                                                  | boolean             | —                     | —       |
| filter-method   | 自定义搜索逻辑，第一个参数是节点node，第二个参数是搜索关键词keyword，通过返回布尔值表示是否命中 | function            | —                     | —       |
| debounce        | 搜索关键词输入的去抖延迟，毫秒                                                                  | number              | —                     | 300     |
| before-filter   | 筛选之前的钩子，参数为输入的值，若返回 false 或者返回 Promise 且被 reject，则停止筛选           | function            | —                     | —       |
| popper-class    | 自定义浮层类名                                                                                  | string              | —                     | —       |

### Props

| 参数          | 说明                                                                                               | 类型                                                                                     | 可选值        | 默认值     |
| ------------- | -------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------- | ------------- | ---------- |
| expandTrigger | 次级菜单的展开方式                                                                                 | string                                                                                   | click / hover | click      |
| multiple      | 是否多选                                                                                           | boolean                                                                                  | —             | false      |
| checkStrictly | 是否严格的遵守父子节点不互相关联                                                                   | boolean                                                                                  | —             | false      |
| emitPath      | 在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值 | boolean                                                                                  | —             | true       |
| lazy          | 是否动态加载子节点，需与 lazyLoad 方法结合使用                                                     | boolean                                                                                  | —             | false      |
| lazyLoad      | 加载动态数据的方法，仅在 lazy 为 true 时有效                                                       | function(node, resolve)，`node`为当前点击的节点，`resolve`为数据加载完成的回调(必须调用) | —             | —          |
| value         | 指定选项的值为选项对象的某个属性值                                                                 | string                                                                                   | —             | 'value'    |
| label         | 指定选项标签为选项对象的某个属性值                                                                 | string                                                                                   | —             | 'label'    |
| children      | 指定选项的子选项为选项对象的某个属性值                                                             | string                                                                                   | —             | 'children' |
| disabled      | 指定选项的禁用为选项对象的某个属性值                                                               | string                                                                                   | —             | 'disabled' |
| leaf          | 指定选项的叶子节点的标志位为选项对象的某个属性值                                                   | string                                                                                   | —             | 'leaf'     |

### Events

| 事件名称       | 说明                        | 回调参数                                                            |
| -------------- | --------------------------- | ------------------------------------------------------------------- |
| change         | 当选中节点变化时触发        | (value, option) value为选中节点的值，option为选中节点的完整数据对象 |
| expand-change  | 当展开节点发生变化时触发    | 各父级选项值组成的数组                                              |
| blur           | 当失去焦点时触发            | (event: Event)                                                      |
| focus          | 当获得焦点时触发            | (event: Event)                                                      |
| visible-change | 下拉框出现/隐藏时触发       | 出现则为 true，隐藏则为 false                                       |
| remove-tag     | 在多选模式下，移除Tag时触发 | 移除的Tag对应的节点的值                                             |

### Slots

| 名称  | 说明                                                                            |
| ----- | ------------------------------------------------------------------------------- |
| -     | 自定义备选项的节点内容，参数为 { node, data }，分别为当前节点的 Node 对象和数据 |
| empty | 无匹配选项时的内容                                                              |

### Methods

| 方法名            | 说明               | 参数                                          |
| ----------------- | ------------------ | --------------------------------------------- |
| getCheckedNodes   | 获取选中的节点数组 | (leafOnly) 是否只是叶子节点，默认值为 `false` |
| clearCheckedNodes | 清空选中的节点     | -                                             |

### 注意事项

1. 在使用动态加载（lazy）模式时，必须配合lazyLoad方法使用，且lazyLoad方法必须调用resolve回调函数
2. 多选模式下，建议配置collapse-tags属性以优化展示效果
3. 自定义节点内容时，通过slot-scope可以获取node和data两个参数，分别表示当前节点的Node对象和原始数据
4. 在只读模式（mode="view"）下，级联选择器将以文本形式展示选中的值，不可进行选择操作
