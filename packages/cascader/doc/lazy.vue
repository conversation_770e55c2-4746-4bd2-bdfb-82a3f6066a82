<template>
  <div class="component-view">
    <p>
      动态加载：
      <x-cascader
        v-model="lazyValue"
        :props="{
          lazy: true,
          lazyLoad: lazyLoadData,
          label: 'name',
        }"
        placeholder="动态加载子级"
      />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      lazyValue: '',
    };
  },
  methods: {
    lazyLoadData(node, resolve) {
      const { level } = node;
      setTimeout(() => {
        const nodes = [
          {
            value: `category${level}`,
            name: `分类${level}`,
            leaf: level >= 2,
          },
          {
            value: `category${level + 1}`,
            name: `分类${level + 1}`,
            leaf: level >= 2,
          },
        ];
        resolve(nodes);
      }, 1000);
    },
  },
};
</script>
