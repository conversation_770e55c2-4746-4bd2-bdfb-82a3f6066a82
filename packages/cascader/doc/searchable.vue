<template>
  <div class="component-view">
    <p>
      可搜索级联：
      <x-cascader
        v-model="searchValue"
        :options="options"
        filterable
        :props="{ label: 'name' }"
        placeholder="支持搜索"
      />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchValue: '',
      options: [
        {
          value: 'fruits',
          name: '水果',
          children: [
            {
              value: 'apple',
              name: '苹果',
            },
            {
              value: 'banana',
              name: '香蕉',
            },
            {
              value: 'orange',
              name: '橙子',
            },
          ],
        },
        {
          value: 'vegetables',
          name: '蔬菜',
          children: [
            {
              value: 'tomato',
              name: '番茄',
            },
            {
              value: 'cucumber',
              name: '黄瓜',
            },
            {
              value: 'carrot',
              name: '胡萝卜',
            },
          ],
        },
      ],
    };
  },
};
</script>
