<template>
  <div class="component-view">
    <p>
      多选级联：
      <x-cascader
        v-model="multipleValue"
        :options="options"
        :props="{ multiple: true, label: 'name' }"
        placeholder="请选择多个选项"
        clearable
      />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      multipleValue: [],
      options: [
        {
          value: 'fruits',
          name: '水果',
          children: [
            {
              value: 'apple',
              name: '苹果',
            },
            {
              value: 'banana',
              name: '香蕉',
            },
            {
              value: 'orange',
              name: '橙子',
            },
          ],
        },
        {
          value: 'vegetables',
          name: '蔬菜',
          children: [
            {
              value: 'tomato',
              name: '番茄',
            },
            {
              value: 'cucumber',
              name: '黄瓜',
            },
            {
              value: 'carrot',
              name: '胡萝卜',
            },
          ],
        },
      ],
    };
  },
};
</script>
