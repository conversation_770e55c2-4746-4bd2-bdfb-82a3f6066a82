<template>
  <div class="component-view">
    <p>
      自定义节点内容：
      <x-cascader v-model="customValue" :options="customOptions" :props="{ label: 'name' }">
        <template slot-scope="{ data }">
          <span>{{ data.name }}</span>
          <span v-if="data.type"> ({{ data.type }}) </span>
        </template>
      </x-cascader>
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      customValue: '',
      customOptions: [
        {
          value: 'frontend',
          name: '前端开发',
          type: '开发方向',
          children: [
            {
              value: 'vue',
              name: 'Vue',
              type: '框架',
            },
            {
              value: 'react',
              name: 'React',
              type: '框架',
            },
          ],
        },
        {
          value: 'backend',
          name: '后端开发',
          type: '开发方向',
          children: [
            {
              value: 'java',
              name: 'Java',
              type: '语言',
            },
            {
              value: 'python',
              name: 'Python',
              type: '语言',
            },
          ],
        },
      ],
    };
  },
};
</script>
