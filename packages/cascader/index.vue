<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2023-05-30 14:23:10
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 10:44:57
 * @FilePath: /vite-element-components/packages/cascader/index.vue
 * @Description: XCascader 组件 - 基于 Element UI 的级联选择器组件封装
-->

<script lang="jsx">
import Tooltip from '../tooltip/index.vue';
import { DEFAULT_CONFIG, DEFAULT_PROPS, EVENTS, CSS_PREFIX } from './constants';
import { formatCascaderValue, isValidCascaderValue } from './utils';
import {
  isValidSize,
  isValidMode,
  computeVisibility,
  computeDisabled,
  safeExecute,
  generateComponentKey,
} from '../shared';

/**
 * XCascader 组件
 * @component XCascader
 * @description 基于 Element UI 的级联选择器组件封装，提供更便捷的配置方式和更丰富的功能
 *
 * @features
 * - 支持只读和编辑两种模式
 * - 支持多种尺寸和样式配置
 * - 支持单选和多选模式
 * - 支持动态加载和搜索功能
 * - 支持函数式的动态显示和禁用控制
 * - 便捷的参数传递机制
 *
 * @example
 * // 基础用法
 * <x-cascader v-model="value" :options="options" />
 *
 * // 只读模式
 * <x-cascader mode="view" v-model="value" :options="options" />
 *
 * // 多选模式
 * <x-cascader v-model="value" :options="options" :props="{ multiple: true }" />
 *
 * // 动态控制
 * <x-cascader :show="(params) => params.visible" :disabled="(params) => params.disabled" />
 */
export default {
  name: 'XCascader',
  title: '级联选择器组件',
  components: { Tooltip },

  props: {
    /**
     * 绑定值，可以是字符串、数字或数组
     */
    value: {
      type: [String, Number, Array],
      default: () => null,
      validator: (value) => isValidCascaderValue(value),
    },

    /**
     * 可选项数据源，数组格式
     */
    options: {
      type: Array,
      default: () => [],
    },

    /**
     * 配置选项，包括 value、label、children 等字段的自定义配置
     */
    props: {
      type: Object,
      default: () => ({}),
    },

    /**
     * 组件尺寸
     */
    size: {
      type: String,
      default: DEFAULT_CONFIG.size,
      validator: isValidSize,
    },

    /**
     * 编辑模式
     */
    mode: {
      type: String,
      default: DEFAULT_CONFIG.mode,
      validator: isValidMode,
    },

    /**
     * 输入框占位文本
     */
    placeholder: {
      type: String,
      default: DEFAULT_CONFIG.placeholder,
    },

    /**
     * 是否可清空
     */
    clearable: {
      type: Boolean,
      default: DEFAULT_CONFIG.clearable,
    },

    /**
     * 是否可搜索选项
     */
    filterable: {
      type: Boolean,
      default: DEFAULT_CONFIG.filterable,
    },

    /**
     * 选项分隔符
     */
    separator: {
      type: String,
      default: DEFAULT_CONFIG.separator,
    },

    /**
     * 是否显示完整路径
     */
    showAllLevels: {
      type: Boolean,
      default: DEFAULT_CONFIG.showAllLevels,
    },

    /**
     * 多选时是否将选中值按文字的形式展示
     */
    collapseTags: {
      type: Boolean,
      default: DEFAULT_CONFIG.collapseTags,
    },

    /**
     * 是否返回完整路径，为false时只返回最后一级的值
     */
    emitPath: {
      type: Boolean,
      default: true,
    },

    /**
     * 搜索关键词输入的去抖延迟，毫秒
     */
    debounce: {
      type: Number,
      default: DEFAULT_CONFIG.debounce,
    },

    /**
     * 筛选之前的钩子
     */
    beforeFilter: {
      type: Function,
      default: DEFAULT_CONFIG.beforeFilter,
    },

    /**
     * 自定义浮层类名
     */
    popperClass: {
      type: String,
      default: DEFAULT_CONFIG.popperClass,
    },

    /**
     * 是否禁用组件
     * 支持函数形式，接收 params 参数
     */
    disabled: {
      type: [Boolean, Function],
      default: DEFAULT_CONFIG.disabled,
    },

    /**
     * 是否显示组件
     * 支持函数形式，接收 params 参数
     */
    show: {
      type: [Boolean, Function],
      default: DEFAULT_CONFIG.show,
    },

    /**
     * 传递给事件处理函数的参数数组或对象
     */
    params: {
      type: [Array, Object],
      default: () => [],
    },

    /**
     * 值改变事件处理函数
     */
    onChange: {
      type: Function,
      default: null,
    },

    /**
     * 展开节点发生变化时触发
     */
    onExpandChange: {
      type: Function,
      default: null,
    },

    /**
     * 失去焦点事件处理函数
     */
    onBlur: {
      type: Function,
      default: null,
    },
    /**
     * 获得焦点事件处理函数
     */
    onFocus: {
      type: Function,
      default: null,
    },
    /**
     * 下拉框出现/隐藏时触发
     */
    onVisibleChange: {
      type: Function,
      default: null,
    },
    /**
     * 多选模式下移除Tag时触发
     */
    onRemoveTag: {
      type: Function,
      default: null,
    },
  },

  data() {
    return {
      /**
       * 内部状态对象
       */
      internalState: {
        loading: false,
        error: null,
      },
      uniqueKey: generateComponentKey('cascader'),
    };
  },

  computed: {
    /**
     * 是否显示组件
     * @returns {boolean} 是否显示
     */
    isVisible() {
      return computeVisibility(this.show, this.params);
    },

    /**
     * 是否禁用组件
     * @returns {boolean} 是否禁用
     */
    isDisabled() {
      return computeDisabled(this.disabled, this.params, this.mode);
    },

    /**
     * 输入值的计算属性
     * @returns {*} 当前选中的值
     */
    inputValue: {
      get() {
        // 如果emitPath为false且传入的是单个值，需要转换为完整路径供cascader使用
        if (!this.emitPath && this.value && !Array.isArray(this.value)) {
          const path = this.findPathByValue(this.options, this.value);
          return path.length > 0 ? path : this.value;
        }
        return this.value;
      },
      set(value) {
        // 根据emitPath决定输出格式
        let emitValue = value;
        if (!this.emitPath && Array.isArray(value) && value.length > 0) {
          // 如果emitPath为false，只返回最后一级的值
          emitValue = value[value.length - 1];
        }
        this.$emit(EVENTS.UPDATE_VALUE, emitValue);
        this.$emit(EVENTS.INPUT, emitValue);
      },
    },

    /**
     * 合并后的配置选项
     * @returns {Object} 合并后的props配置
     */
    mergedProps() {
      const props = {
        ...DEFAULT_PROPS,
        ...this.props,
      };

      // 当emitPath为false时，建议使用hover展开来改善用户体验
      // if (!this.emitPath && props.checkStrictly) {
      //   props.expandTrigger = 'hover';
      // }

      return props;
    },

    /**
     * 组件样式类名
     * @returns {Object} 样式类名对象
     */
    cascaderClasses() {
      return {
        [CSS_PREFIX]: true,
        [`${CSS_PREFIX}-disabled`]: this.isDisabled,
        [`${CSS_PREFIX}-${this.size}`]: this.size,
      };
    },
  },

  methods: {
    // 递归查找节点的路径
    findPathByValue(nodes, targetValue, path = []) {
      for (const node of nodes) {
        const currentPath = [...path, node[this.mergedProps.value]];
        if (node[this.mergedProps.value] === targetValue) {
          return currentPath; // 找到目标节点，返回路径
        }
        if (node[this.mergedProps.children]) {
          const found = this.findPathByValue(node[this.mergedProps.children], targetValue, currentPath);
          if (found.length > 0) return found;
        }
      }
      return []; // 未找到
    },

    /**
     * 获取级联选择器当前选中值的显示文本
     * @returns {string} 选中值的文本表示
     */
    formatValue() {
      return formatCascaderValue(this.inputValue, this.options, this.mergedProps, this.separator);
    },

    /**
     * 获取级联选择器引用
     * @returns {Object} 级联选择器引用
     */
    getCascaderRef() {
      return this.$refs.cascader;
    },

    /**
     * 获取选中的节点数组
     * @param {boolean} leafOnly 是否只返回叶子节点
     * @returns {Array} 选中的节点数组
     */
    getCheckedNodes(leafOnly = false) {
      const cascaderRef = this.getCascaderRef();
      if (cascaderRef && typeof cascaderRef.getCheckedNodes === 'function') {
        return cascaderRef.getCheckedNodes(leafOnly);
      }
      return [];
    },

    /**
     * 清空选中的节点
     */
    clearCheckedNodes() {
      const cascaderRef = this.getCascaderRef();
      if (cascaderRef && typeof cascaderRef.clearCheckedNodes === 'function') {
        cascaderRef.clearCheckedNodes();
      }
    },

    /**
     * 创建事件上下文对象
     * @param {Event} event 原生事件对象
     * @returns {Object} 事件上下文
     */
    createEventContext(event = null) {
      return {
        $event: event,
        getCheckedNodes: this.getCheckedNodes,
        clearCheckedNodes: this.clearCheckedNodes,
        formatValue: this.formatValue,
      };
    },

    /**
     * 处理值改变事件
     * @param {*} value 新值
     */
    handleChange(value) {
      // 根据emitPath决定输出格式
      let emitValue = value;
      if (!this.emitPath && Array.isArray(value) && value.length > 0) {
        // 如果emitPath为false，只返回最后一级的值
        emitValue = value[value.length - 1];
      }

      // 更新value
      this.$emit(EVENTS.UPDATE_VALUE, emitValue);
      this.$emit(EVENTS.INPUT, emitValue);

      const context = this.createEventContext();

      if (this.onChange) {
        safeExecute(this.onChange, [emitValue, this.params, context]);
      } else {
        this.$emit(EVENTS.CHANGE, emitValue, context);
      }
    },

    /**
     * 处理展开节点变化事件
     * @param {*} value 展开的节点值
     */
    handleExpandChange(value) {
      const context = this.createEventContext();

      if (this.onExpandChange) {
        safeExecute(this.onExpandChange, [value, this.params, context]);
      } else {
        this.$emit(EVENTS.EXPAND_CHANGE, value, context);
      }
    },

    /**
     * 处理失去焦点事件
     * @param {Event} event 原生事件对象
     */
    handleBlur(event) {
      const context = this.createEventContext(event);

      if (this.onBlur) {
        safeExecute(this.onBlur, [event, this.params, context]);
      } else {
        this.$emit(EVENTS.BLUR, event, context);
      }
    },

    /**
     * 处理获得焦点事件
     * @param {Event} event 原生事件对象
     */
    handleFocus(event) {
      const context = this.createEventContext(event);

      if (this.onFocus) {
        safeExecute(this.onFocus, [event, this.params, context]);
      } else {
        this.$emit(EVENTS.FOCUS, event, context);
      }
    },

    /**
     * 处理下拉框显示/隐藏事件
     * @param {boolean} visible 是否可见
     */
    handleVisibleChange(visible) {
      const context = this.createEventContext();

      if (this.onVisibleChange) {
        safeExecute(this.onVisibleChange, [visible, this.params, context]);
      } else {
        this.$emit(EVENTS.VISIBLE_CHANGE, visible, context);
      }
    },

    /**
     * 处理移除标签事件
     * @param {*} value 移除的值
     */
    handleRemoveTag(value) {
      const context = this.createEventContext();

      if (this.onRemoveTag) {
        safeExecute(this.onRemoveTag, [value, this.params, context]);
      } else {
        this.$emit(EVENTS.REMOVE_TAG, value, context);
      }
    },
  },

  /**
   * 渲染函数
   * @returns {VNode|undefined} 渲染结果
   */
  render() {
    // 如果不显示，直接返回
    if (!this.isVisible) {
      return undefined;
    }

    // 只读模式渲染
    if (this.mode === 'view') {
      return (
        <div class={this.cascaderClasses}>
          <tooltip content={this.formatValue()} />
        </div>
      );
    }

    // 编辑模式渲染
    const cascaderProps = {
      ref: 'cascader',
      class: this.cascaderClasses,
      style: { width: '100%' },
      props: {
        value: this.inputValue,
        options: Array.isArray(this.options) ? this.options : [],
        props: this.mergedProps,
        size: this.size,
        placeholder: this.placeholder,
        disabled: this.isDisabled,
        clearable: this.clearable,
        filterable: this.filterable,
        separator: this.separator,
        showAllLevels: this.showAllLevels,
        collapseTags: this.collapseTags,
        debounce: this.debounce,
        ...(typeof this.beforeFilter === 'function' ? { beforeFilter: this.beforeFilter } : {}),
        ...(typeof this.filterMethod === 'function' ? { filterMethod: this.filterMethod } : {}),
        popperClass: this.popperClass || 'x-cascader-popper',
        ...this.$attrs,
      },
      on: {
        change: this.handleChange,
        'expand-change': this.handleExpandChange,
        blur: this.handleBlur,
        focus: this.handleFocus,
        'visible-change': this.handleVisibleChange,
        'remove-tag': this.handleRemoveTag,
        ...this.$listeners,
      },
      key: this.uniqueKey,
    };

    return (
      <div class={this.cascaderClasses}>
        <el-cascader {...cascaderProps}>
          {this.$scopedSlots.default && this.$scopedSlots.default}
          {this.$scopedSlots.empty && this.$scopedSlots.empty}
        </el-cascader>
      </div>
    );
  },
};
</script>

<style lang="scss" scoped>
@import '../styles/index.scss';

/**
 * XCascader 组件样式
 * 基于 Element UI Cascader 组件的样式扩展
 */
.x-cascader {
  // 基础样式继承 Element UI
  width: 100%;

  // 只读模式样式
  &.x-cascader-view {
    .tooltip {
      width: 100%;
      display: block;
    }
  }

  // 编辑模式样式
  &.x-cascader-edit {
    .el-cascader {
      width: 100%;
    }
  }

  // 禁用状态样式
  &.x-cascader-disabled {
    .el-cascader {
      cursor: not-allowed;

      .el-input__inner {
        cursor: not-allowed;
        background-color: #f5f7fa;
        border-color: #e4e7ed;
        color: #c0c4cc;
      }
    }
  }

  // 尺寸样式
  &.x-cascader-large {
    .el-cascader {
      .el-input__inner {
        height: 40px;
        line-height: 40px;
        font-size: 14px;
      }
    }
  }

  &.x-cascader-medium {
    .el-cascader {
      .el-input__inner {
        height: 36px;
        line-height: 36px;
        font-size: 14px;
      }
    }
  }

  &.x-cascader-small {
    .el-cascader {
      .el-input__inner {
        height: 32px;
        line-height: 32px;
        font-size: 13px;
      }
    }
  }

  &.x-cascader-mini {
    .el-cascader {
      .el-input__inner {
        height: 28px;
        line-height: 28px;
        font-size: 12px;
      }
    }
  }
}

// 全局级联选择器弹出层样式
.x-cascader-popper {
  .el-cascader-menu {
    min-width: 180px;
  }

  .el-cascader-node {
    &:hover {
      background-color: #f5f7fa;
    }

    &.is-selectable.in-active-path {
      color: #409eff;
      font-weight: 600;
    }

    &.is-active {
      color: #409eff;
      font-weight: 600;
    }
  }

  .el-cascader-menu__empty-text {
    margin: 10px 0;
    color: #c0c4cc;
    font-size: 14px;
    text-align: center;
  }
}

// 兼容旧版本样式
.el-cascader.x-cascader {
  width: 100%;
}
</style>
