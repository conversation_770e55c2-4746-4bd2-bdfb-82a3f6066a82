/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-14 16:30:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-16 09:15:08
 * @FilePath: /vite-element-components/packages/cascader/constants.js
 * @Description: XCascader 组件常量定义
 */

/**
 * XCascader 组件常量定义
 * <AUTHOR>
 * @description 定义级联选择器组件相关的常量和枚举值
 */

import {
  COMMON_EVENTS,
  CSS_PREFIXES,
  DEFAULT_SEPARATOR,
  DEFAULT_PLACEHOLDER,
  DEFAULT_EMPTY_TEXT,
  COMPONENT_STATES,
  VALIDATION_RULES,
} from '../shared';

/**
 * 级联选择器触发方式枚举
 * @type {string[]}
 */
export const CASCADER_TRIGGERS = ['click', 'hover'];

/**
 * 级联选择器展开方式枚举
 * @type {string[]}
 */
export const CASCADER_EXPAND_TRIGGERS = ['click', 'hover'];

/**
 * 级联选择器默认Props配置
 */
export const DEFAULT_PROPS = {
  expandTrigger: 'click',
  multiple: false,
  checkStrictly: false,
  emitPath: true,
  lazy: false,
  lazyLoad: null,
  value: 'value',
  label: 'label',
  children: 'children',
  disabled: 'disabled',
  leaf: 'leaf',
};

/**
 * 级联选择器组件默认配置
 */
export const DEFAULT_CONFIG = {
  size: '',
  mode: 'edit',
  disabled: false,
  show: true,
  params: [],
  clearable: false,
  filterable: false,
  placeholder: '请选择',
  separator: '/',
  showAllLevels: true,
  collapseTags: false,
  debounce: 300,
  beforeFilter: null,
  filterMethod: null,
  popperClass: '',
  props: DEFAULT_PROPS,
};

/**
 * 事件名称常量 (从公共模块导入)
 */
export const EVENTS = {
  CHANGE: COMMON_EVENTS.CHANGE,
  EXPAND_CHANGE: COMMON_EVENTS.EXPAND_CHANGE,
  BLUR: COMMON_EVENTS.BLUR,
  FOCUS: COMMON_EVENTS.FOCUS,
  VISIBLE_CHANGE: COMMON_EVENTS.VISIBLE_CHANGE,
  REMOVE_TAG: COMMON_EVENTS.REMOVE_TAG,
  UPDATE_VALUE: COMMON_EVENTS.UPDATE_VALUE,
  INPUT: COMMON_EVENTS.INPUT,
};

/**
 * CSS 类名前缀 (从公共模块导入)
 */
export const CSS_PREFIX = CSS_PREFIXES.CASCADER;

/**
 * 模式样式映射
 */
export const MODE_CLASS_MAP = {
  edit: `${CSS_PREFIX}-edit`,
  view: `${CSS_PREFIX}-view`,
};

/**
 * 级联选择器状态常量 (从公共模块导入)
 */
export const CASCADER_STATES = COMPONENT_STATES;

/**
 * 验证规则常量 (从公共模块导入)
 */
export { VALIDATION_RULES };

/**
 * 默认分隔符 (从公共模块导入)
 */
export { DEFAULT_SEPARATOR };

/**
 * 默认占位符 (从公共模块导入)
 */
export { DEFAULT_PLACEHOLDER };

/**
 * 默认空值显示 (从公共模块导入)
 */
export { DEFAULT_EMPTY_TEXT };

/**
 * 搜索防抖时间
 */
export const DEFAULT_DEBOUNCE = 300;
