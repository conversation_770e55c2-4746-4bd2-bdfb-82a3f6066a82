/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-14 16:57:29
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-16 09:24:44
 * @FilePath: /vite-element-components/packages/cascader/utils.js
 * @Description:
 */
/**
 * XCascader 组件工具函数
 * <AUTHOR>
 * @description 提供级联选择器组件相关的工具函数和辅助方法
 */

import { DEFAULT_EMPTY_TEXT, DEFAULT_SEPARATOR } from '../shared';
import { DEFAULT_PROPS } from './constants';

// ==================== 业务相关方法 ====================

/**
 * 递归查找选中值对应的标签文本
 */
export function findLabelByValue(options, targetValue, props = DEFAULT_PROPS) {
  const { value: valueKey, label: labelKey, children: childrenKey } = props;
  if (!Array.isArray(options)) return null;
  for (const option of options) {
    if (option[valueKey] === targetValue) return option[labelKey];
    if (option[childrenKey] && Array.isArray(option[childrenKey])) {
      const found = findLabelByValue(option[childrenKey], targetValue, props);
      if (found) return found;
    }
  }
  return null;
}

/**
 * 递归查找选中路径对应的标签文本数组
 */
export function findLabelsByPath(options, valuePath, props = DEFAULT_PROPS) {
  if (!Array.isArray(valuePath) || valuePath.length === 0) return [];
  const { value: valueKey, label: labelKey, children: childrenKey } = props;
  const labels = [];
  let currentOptions = options;
  for (const value of valuePath) {
    if (!Array.isArray(currentOptions)) break;
    const option = currentOptions.find((item) => item[valueKey] === value);
    if (option) {
      labels.push(option[labelKey]);
      currentOptions = option[childrenKey];
    } else {
      break;
    }
  }
  return labels;
}

/**
 * 格式化级联选择器显示值
 */
export function formatCascaderValue(value, options, props = DEFAULT_PROPS, separator = DEFAULT_SEPARATOR) {
  if (!value) return DEFAULT_EMPTY_TEXT;
  if (!Array.isArray(options) || options.length === 0) return DEFAULT_EMPTY_TEXT;
  // 多选
  if (Array.isArray(value) && value.length > 0 && Array.isArray(value[0])) {
    const labels = value
      .map((path) => {
        const pathLabels = findLabelsByPath(options, path, props);
        return pathLabels.length > 0 ? pathLabels.join(separator) : DEFAULT_EMPTY_TEXT;
      })
      .filter((label) => label !== DEFAULT_EMPTY_TEXT);
    return labels.length > 0 ? labels.join(', ') : DEFAULT_EMPTY_TEXT;
  }
  // 单选路径
  if (Array.isArray(value)) {
    const labels = findLabelsByPath(options, value, props);
    return labels.length > 0 ? labels.join(separator) : DEFAULT_EMPTY_TEXT;
  }
  // 单个值
  const label = findLabelByValue(options, value, props);
  return label || DEFAULT_EMPTY_TEXT;
}
/**
 * 验证级联选择器值是否有效
 * @param {*} value 要验证的值
 * @returns {boolean} 是否有效
 */
export function isValidCascaderValue(value) {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string' || typeof value === 'number') return true;
  if (Array.isArray(value)) return true;
  return false;
}
