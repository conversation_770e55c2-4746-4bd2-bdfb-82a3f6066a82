<script lang="jsx">
import { v4 as uuidv4 } from 'uuid';

/**
 * 文字提示组件
 * @component XTooltip
 * @description 基于 Element UI 的文字提示组件，当文本内容超出显示范围时自动显示提示框。支持自定义主题、位置和样式。
 */
export default {
  name: 'XTooltip',
  props: {
    /**
     * 显示的文字内容
     * @type {string|number|array}
     * @default ''
     */
    content: {
      type: [String, Number, Array],
      default: '',
    },
    /**
     * 自定义样式类名，用于设置父元素的样式（如宽度、字体等）
     * @type {string}
     * @default ''
     */
    className: {
      type: String,
      default: '',
    },
    /**
     * 子元素引用标识，在同一页面多次使用时需确保唯一
     * @type {string}
     * @default ''
     */
    refName: {
      type: String,
      default: '',
    },
    /**
     * 提示框主题
     * @type {string}
     * @default 'default'
     * @values 'default'|'light'
     */
    theme: {
      type: String,
      default: () => 'default',
    },
    /**
     * 提示框出现位置
     * @type {string}
     * @default 'top'
     * @values 'top'|'top-start'|'top-end'|'bottom'|'bottom-start'|'bottom-end'|'left'|'left-start'|'left-end'|'right'|'right-start'|'right-end'
     */
    placement: {
      type: String,
      default: () => 'top',
    },
    /**
     * 是否在内容溢出时显示提示框
     * @type {boolean}
     * @default true
     */
    ellipsis: {
      type: Boolean,
      default: true,
    },
    /**
     * 组件唯一标识
     * @type {string}
     * @default uuidv4()
     */
    elementId: {
      type: String,
      default: uuidv4(),
    },
  },
  data() {
    return {
      isDisabledTooltip: true, // 是否需要禁止提示
    };
  },
  methods: {
    // 移入事件: 判断内容的宽度contentWidth是否大于父级的宽度
    onMouseOver(str) {
      const parentWidth =
        this.$refs && this.$refs[str] && this.$refs[str].parentNode && this.$refs[str].parentNode.offsetWidth
          ? this.$refs[str].parentNode.offsetWidth
          : this.wrapWidth;
      const contentWidth =
        this.$refs && this.$refs[str] && this.$refs[str].offsetWidth
          ? this.$refs[str].offsetWidth
          : document.getElementById(this.elementId) &&
            document.getElementById(this.elementId).getElementsByClassName('x-tooltip__content')[0] &&
            document.getElementById(this.elementId).getElementsByClassName('x-tooltip__content')[0].offsetWidth;

      // 判断是否禁用tooltip功能
      this.isDisabledTooltip = contentWidth <= parentWidth || contentWidth - parentWidth === 1;
    },
    // 渲染只读事件
    renderReadonly() {
      return this.content !== '' && this.content !== null ? this.content : '-';
    },
  },
  render() {
    return (
      <div class="x-tooltip">
        {this.ellipsis ? (
          <el-tooltip
            theme={this.theme}
            disabled={this.isDisabledTooltip}
            content={String(this.content)}
            placement={this.placement}
            tabindex={-1}
            popper-class={`x-tooltip__popper x-tooltip__popper--${this.elementId}`}
            {...{
              props: this.$attrs,
            }}
          >
            <div
              id={this.elementId}
              class={['x-tooltip__container', ...this.className]}
              onMouseover={() => {
                this.onMouseOver(this.refName);
              }}
              {...{
                on: this.$listeners,
              }}
            >
              <span ref={this.refName} class="x-tooltip__content">
                {this.content !== '' && this.content !== null ? this.content : '-'}
              </span>
            </div>
          </el-tooltip>
        ) : (
          this.renderReadonly()
        )}
      </div>
    );
  },
};
</script>
<style lang="scss" scoped>
@import '../styles/index.scss';
.x-tooltip {
  width: 100%;
  color: inherit;
  white-space: pre-wrap;
  word-break: break-all;
  line-height: 1.5;
  padding: 10px 0;
  &__container {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &__popper {
    white-space: pre-line;
  }
}
</style>
<style lang="scss">
.x-tooltip {
  &__popper {
    max-width: 600px;
    white-space: pre-line;
  }
}
</style>
