<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-27 15:20:14
 * @FilePath: /vite-element-components/packages/tooltip/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import Preview from '@/components/preview.vue'

  export default {
    components: {
      BasicVue,Preview
    }
  };
</script>

## Tooltip 组件

文字提示组件，当文本内容超出显示范围时自动显示提示框。基于 Element UI 的 Tooltip 组件进行封装，支持自定义主题、位置和样式。

### 基础用法

基础的文字提示用法。当文本内容超出显示范围时，将自动显示提示框。

<basic-vue/>
<preview  comp-name='tooltip' demo-name='basic'/>

### 属性

| 参数      | 说明                       | 类型                | 可选值                                                                                                    | 默认值    |
| --------- | -------------------------- | ------------------- | --------------------------------------------------------------------------------------------------------- | --------- |
| content   | 显示的文字内容             | string/number/array | —                                                                                                         | ''        |
| className | 自定义样式类名             | string              | —                                                                                                         | ''        |
| refName   | 子元素引用标识             | string              | —                                                                                                         | ''        |
| theme     | 提示框主题                 | string              | default/light                                                                                             | 'default' |
| placement | 提示框出现位置             | string              | top/top-start/top-end/bottom/bottom-start/bottom-end/left/left-start/left-end/right/right-start/right-end | 'top'     |
| ellipsis  | 是否在内容溢出时显示提示框 | boolean             | —                                                                                                         | true      |

### 事件

| 事件名称 | 说明               | 回调参数 |
| -------- | ------------------ | -------- |
| show     | 当提示框显示时触发 | —        |
| hide     | 当提示框隐藏时触发 | —        |

### 插槽

| 插槽名称 | 说明                           |
| -------- | ------------------------------ |
| default  | 触发Tooltip显示的HTML元素      |
| content  | 自定义提示内容（支持HTML格式） |

### 使用示例

```html
<template>
  <x-tooltip content="默认提示内容">
    <span>鼠标悬停我查看提示</span>
    <template #content>
      <div class="custom-tip">🎉 自定义提示内容（支持图标和样式）</div>
    </template>
  </x-tooltip>
</template>
```

### 注意事项

1. 当使用 `ellipsis` 属性时，只有在内容超出容器宽度时才会显示提示框
2. 在同一页面多次获取组件实例时，需要为每个组件设置唯一的 `refName`
3. 可以通过 `className` 属性自定义组件的样式，如宽度、字体等
