<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-11 09:58:37
 * @FilePath: /vite-element-components/packages/tooltip/doc/basic.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <div style="display: flex; flex-direction: column; width: 100%; align-items: center">
      <div style="width: 200px" class="tooltip">
        <x-tooltip :content="content1" />
        <x-tooltip :content="content" />
        <x-tooltip :content="content" placement="right" @click="content1 = '我触发了点击事件'" />
        <x-tooltip :content="content" placement="bottom" />
        <x-tooltip :content="content" placement="right-end" />
        <x-tooltip :content="content" placement="left-start" />
        <x-tooltip :content="content" placement="left" />
        <x-tooltip :content="content" placement="top-end" />

        <x-tooltip :content="content2" :ellipsis="false" />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      content:
        '一二三四五六七八九十，一二三四五六七八九十，一二三四五六七八九十，一二三四五六七八九十一二三四五六七八九十，一二三四五六七八九十，一二三四五六七八九十，一二三四五六七八九十一二三四五六七八九十，一二三四五六七八九十，一二三四五六七八九十，一二三四五六七八九十',
      content1: '内容未超出不显示',
      content2: '我是内容不省略显示，我是内容不省略显示，我是内容不省略显示，我是内容不省略显示，我是内容不省略显示',
    };
  },
};
</script>
