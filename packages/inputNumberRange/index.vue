<script lang="jsx">
import XInputNumber from '../inputNumber/index.vue';

/**
 * @description 数字区间输入组件，支持区间输入，最小值需小于等于最大值
 * <AUTHOR>
 * @module components/XInputNumberRange
 */
export default {
  name: 'XInputNumberRange',
  title: '数字区间输入组件',
  components: { XInputNumber },
  props: {
    /**
     * @description 绑定值，区间数组 [min, max]
     * @type {array}
     * @default [undefined, undefined]
     */
    value: {
      type: Array,
      default: () => [undefined, undefined],
    },
    /**
     * @description 输入框占位文本，数组 [min, max]
     * @type {array}
     * @default ['最小值', '最大值']
     */
    placeholder: {
      type: Array,
      default: () => ['最小值', '最大值'],
    },
    /**
     * @description 修饰符配置对象，应用于两个输入框
     * @type {object}
     * @default {}
     */
    modifiers: {
      type: Object,
      default: () => ({}),
    },
    /**
     * @description 输入框宽度，单值或数组
     * @type {string|number|array}
     * @default '100%'
     */
    width: {
      type: [String, Number, Array],
      default: '100%',
    },
    /**
     * @description 默认值，数组 [min, max]
     * @type {array}
     * @default [undefined, undefined]
     */
    defaultValue: {
      type: Array,
      default: () => [undefined, undefined],
    },
    /**
     * @description 是否禁用
     * @type {boolean}
     * @default false
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 输入框尺寸
     * @type {string}
     * @default ''
     * @values 'medium'|'small'|'mini'
     */
    size: {
      type: String,
      default: '',
    },
    /**
     * @description 最小值限制，单值或数组
     * @type {number|array}
     */
    min: {
      type: [Number, Array],
      default: undefined,
    },
    /**
     * @description 最大值限制，单值或数组
     * @type {number|array}
     */
    max: {
      type: [Number, Array],
      default: undefined,
    },
    /**
     * @description 整数位和小数位限制，格式：整数位_小数位
     * @type {string}
     * @default ''
     */
    digits: {
      type: String,
      default: '',
    },
    /**
     * @description 后缀文本
     * @type {string}
     * @default ''
     */
    suffix: {
      type: String,
      default: '',
    },
    /**
     * @description 显示模式 edit/view
     * @type {string}
     * @default 'edit'
     */
    mode: {
      type: String,
      default: 'edit',
    },
    /**
     * @description 是否显示加减按钮
     * @type {boolean}
     * @default false
     */
    controls: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 分隔符
     * @type {string}
     * @default '~'
     */
    separator: {
      type: String,
      default: '~',
    },
  },
  data() {
    return {
      /**
       * @description 区间校验错误标志，true 时高亮输入框并显示错误提示
       */
      error: false,
    };
  },
  computed: {
    /**
     * @description 区间值，双向绑定
     */
    rangeValue: {
      get() {
        return Array.isArray(this.value) ? this.value : this.defaultValue;
      },
      set(val) {
        this.$emit('update:value', val);
        this.$emit('change', val);
      },
    },
    /**
     * @description 最小值
     */
    minValue: {
      get() {
        return this.rangeValue[0];
      },
      set(val) {
        const newVal = [val, this.rangeValue[1]];
        this.rangeValue = newVal;
      },
    },
    /**
     * @description 最大值
     */
    maxValue: {
      get() {
        return this.rangeValue[1];
      },
      set(val) {
        const newVal = [this.rangeValue[0], val];
        this.rangeValue = newVal;
      },
    },
    /**
     * @description 输入框宽度数组
     */
    widthArr() {
      if (Array.isArray(this.width)) return this.width;
      return [this.width, this.width];
    },
    /**
     * @description 最小值限制
     */
    minLimit() {
      if (Array.isArray(this.min)) return this.min[0];
      return this.min;
    },
    /**
     * @description 最大值限制
     */
    maxLimit() {
      if (Array.isArray(this.max)) return this.max[1];
      return this.max;
    },
  },
  methods: {
    /**
     * @description 输入事件处理，更新区间值并校验
     * @param {number|string} val 输入值
     * @param {number} idx 0-最小值 1-最大值
     */
    handleInput(val, idx) {
      let [min, max] = this.rangeValue;
      if (idx === 0) {
        min = val;
        if (max !== null && max !== '' && Number(min) > Number(max)) {
          max = min;
        }
      } else {
        max = val;
        if (min !== null && min !== '' && Number(max) < Number(min)) {
          min = max;
        }
      }
      const newVal = [min, max];
      this.$emit('update:value', newVal);
      this.$emit('input', newVal);
    },
    /**
     * @description 失焦事件
     */
    handleBlur(e, idx) {
      this.$emit('blur', e, this.rangeValue, idx);
    },
    /**
     * @description 聚焦事件
     */
    handleFocus(e, idx) {
      this.$emit('focus', e, this.rangeValue, idx);
    },
  },
  render() {
    return (
      <div class="x-input-number-range">
        {/* 最小值输入框 */}
        <XInputNumber
          value={this.minValue}
          placeholder={this.mode === 'edit' ? this.placeholder[0] : ''}
          modifiers={this.modifiers}
          width={this.widthArr[0]}
          defaultValue={this.defaultValue[0]}
          disabled={this.disabled || this.mode === 'disabled'}
          size={this.size}
          min={this.minLimit}
          max={this.maxLimit}
          digits={this.digits}
          suffix={this.suffix}
          mode={this.mode}
          controls={this.controls}
          onInput={(val) => this.handleInput(val, 0)}
          onBlur={(e) => this.handleBlur(e, 0)}
          onFocus={(e) => this.handleFocus(e, 0)}
        />
        {/* 分隔符 */}
        <span class="x-input-number-range__separator">{this.separator}</span>
        {/* 最大值输入框 */}
        <XInputNumber
          value={this.maxValue}
          placeholder={this.mode === 'edit' ? this.placeholder[1] : ''}
          modifiers={this.modifiers}
          width={this.widthArr[1]}
          defaultValue={this.defaultValue[1]}
          disabled={this.disabled || this.mode === 'disabled'}
          size={this.size}
          min={this.minLimit}
          max={this.maxLimit}
          digits={this.digits}
          suffix={this.suffix}
          mode={this.mode}
          controls={this.controls}
          onInput={(val) => this.handleInput(val, 1)}
          onBlur={(e) => this.handleBlur(e, 1)}
          onFocus={(e) => this.handleFocus(e, 1)}
        />
      </div>
    );
  },
};
</script>

<style lang="scss" scoped>
@import '../styles/index.scss';
.x-input-number-range {
  display: flex;
  align-items: center;
  .x-input-number-range__separator {
    margin: 0 8px;
    color: #999;
    font-size: 16px;
    user-select: none;
  }
}
</style>
