<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-27
 * @FilePath: /vite-element-components/packages/inputNumberRange/doc/doc.md
 * @Description: 数字区间输入组件文档
-->
<script>
  import BasicVue from './basic.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      Preview
    }
  };
</script>

## InputNumberRange 数字区间输入组件

基于 XInputNumber 组件封装，支持区间输入，常用于范围筛选、区间设置等场景。

### 基础用法

<basic-vue/>
<preview comp-name='inputNumberRange' demo-name='basic'/>

### 属性说明

| 参数            | 说明                                     | 类型                | 可选值                | 默认值              |
| --------------- | ---------------------------------------- | ------------------- | --------------------- | ------------------- |
| value / v-model | 绑定值，区间数组 [min, max]              | array               | —                     | [null,null]         |
| placeholder     | 输入框占位文本，数组 [min, max]          | array               | —                     | ['最小值','最大值'] |
| modifiers       | 修饰符配置对象，应用于两个输入框         | object              | —                     | {}                  |
| width           | 输入框宽度，单值或数组                   | string/number/array | —                     | '100%'              |
| defaultValue    | 默认值，数组 [min, max]                  | array               | —                     | [null,null]         |
| disabled        | 是否禁用                                 | boolean             | —                     | false               |
| size            | 输入框尺寸                               | string              | medium / small / mini | small               |
| min             | 最小值限制，单值或数组                   | number/array        | —                     | —                   |
| max             | 最大值限制，单值或数组                   | number/array        | —                     | —                   |
| digits          | 整数位和小数位限制，格式：整数位\_小数位 | string              | —                     | '9_4'               |
| suffix          | 后缀文本                                 | string              | —                     | —                   |
| mode            | 显示模式 edit/view                       | string              | edit/view             | edit                |
| controls        | 是否显示加减按钮                         | boolean             | —                     | false               |
| separator       | 分隔符                                   | string              | —                     | ~                   |

### 事件

| 事件名称 | 说明                   | 回调参数              |
| -------- | ---------------------- | --------------------- |
| change   | 在区间值改变时触发     | (value: array)        |
| input    | 在输入值时触发         | (value: array)        |
| blur     | 在输入框失去焦点时触发 | (event, value, index) |
| focus    | 在输入框获得焦点时触发 | (event, value, index) |

### 使用注意事项

1. 支持 v-model 双向绑定区间数组 [min, max]
2. 支持所有 XInputNumber 的修饰符、digits、size、mode、controls 等属性
3. 支持单独设置 min/max/width/placeholder（传数组）
4. separator 可自定义分隔符
