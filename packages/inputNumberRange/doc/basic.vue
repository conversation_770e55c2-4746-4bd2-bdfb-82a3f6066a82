<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-04 15:56:16
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-04 16:00:54
 * @FilePath: /vite-element-components/packages/inputNumberRange/doc/basic.vue
 * @Description:
-->
<template>
  <div class="component-view">
    <p>
      区间输入：
      <x-input-number-range v-model="range" :min="0" :max="100" digits="3_0" :placeholder="['最小值', '最大值']" />
    </p>
    <p>
      详情模式：
      <x-input-number-range v-model="range" mode="view" />
    </p>
    <p>
      禁用：
      <x-input-number-range v-model="range" disabled />
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      range: [10, 90],
    };
  },
};
</script>
