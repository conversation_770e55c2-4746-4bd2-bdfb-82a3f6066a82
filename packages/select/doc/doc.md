<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-26 17:55:56
 * @FilePath: /vite-element-components/packages/select/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,Preview
    }
  };
</script>

## Select 组件

基于 Element UI 的 Select 组件进行封装，支持远程搜索、懒加载、多选、上拉分页加载等功能。

### 基础用法

<basic-vue/>
<preview  comp-name='select' demo-name='basic'/>

### 属性

| 参数                  | 说明                                                          | 类型                    | 可选值            | 默认值 |
| --------------------- | ------------------------------------------------------------- | ----------------------- | ----------------- | ------ |
| value / v-model       | 绑定值                                                        | string / number / array | —                 | —      |
| multiple              | 是否多选                                                      | boolean                 | —                 | false  |
| disabled              | 是否禁用                                                      | boolean                 | —                 | false  |
| size                  | 输入框尺寸                                                    | string                  | medium/small/mini | small  |
| clearable             | 是否可以清空选项                                              | boolean                 | —                 | true   |
| collapse-tags         | 多选时是否将选中值按文字的形式展示                            | boolean                 | —                 | false  |
| placeholder           | 占位符                                                        | string                  | —                 | 请选择 |
| filterable            | 是否可搜索                                                    | boolean                 | —                 | false  |
| remote                | 是否为远程搜索                                                | boolean                 | —                 | false  |
| value-key             | 作为 value 唯一标识的键名                                     | string                  | —                 | value  |
| label-key             | 作为 label 展示的键名                                         | string                  | —                 | label  |
| reserve-keyword       | 多选且可搜索时，是否在选中一个选项后保留当前的搜索关键词      | boolean                 | —                 | true   |
| popper-class          | Select 下拉框的类名                                           | string                  | —                 | —      |
| popper-append-to-body | 是否将弹出框插入至 body 元素                                  | boolean                 | —                 | true   |
| automatic-dropdown    | 对于不可搜索的 Select，是否在输入框获得焦点后自动弹出选项菜单 | boolean                 | —                 | false  |
| readonly              | 是否只读                                                      | boolean                 | —                 | false  |
| options               | 选项数组                                                      | array                   | —                 | []     |
| on-search             | 远程搜索方法                                                  | function(query, page)   | —                 | —      |
| on-load-more          | 加载更多方法                                                  | function(query, page)   | —                 | —      |
| mode                  | 组件模式                                                      | string                  | edit/view         | edit   |
| loading               | 是否显示加载中状态                                            | boolean                 | —                 | false  |

### 事件

| 事件名称       | 说明                                     | 回调参数                      |
| -------------- | ---------------------------------------- | ----------------------------- |
| change         | 选中值发生变化时触发                     | 目前的选中值                  |
| visible-change | 下拉框出现/隐藏时触发                    | 出现则为 true，隐藏则为 false |
| remove-tag     | 多选模式下移除tag时触发                  | 移除的tag值                   |
| clear          | 可清空的单选模式下用户点击清空按钮时触发 | —                             |
| blur           | 当 input 失去焦点时触发                  | (event: Event)                |
| focus          | 当 input 获得焦点时触发                  | (event: Event)                |

### 方法

| 方法名 | 说明              | 参数 |
| ------ | ----------------- | ---- |
| focus  | 使 input 获取焦点 | —    |
| blur   | 使 input 失去焦点 | —    |
| reset  | 重置组件状态      | —    |
