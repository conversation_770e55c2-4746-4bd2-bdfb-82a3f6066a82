<template>
  <div class="component-view">
    <p>当前值: {{ inputValue }}</p>
    <p>
      普通选择框：
      <x-select v-model="inputValue" :options="options" filterable />
    </p>
    <p>
      详情选择框：
      <x-select mode="view" v-model="inputValue" :options="options" />
    </p>
    <p>
      禁用选择框：
      <x-select disabled v-model="inputValue" :options="options" />
    </p>

    <p>
      搜索+分页复选框：
      <x-select
        v-model="inputValue1"
        filterable
        multiple
        remote
        reserve-keyword
        :on-search="handleSearch"
        :on-load-more="handleLoadMore"
      />
    </p>
  </div>
</template>

<script lang="jsx">
export default {
  data() {
    return {
      inputValue: '', // 测试null值不会错误选择value为0的选项
      inputValue1: '',
      options: [
        { label: '数字零', value: 0, a: 1, b: 2, c: 3, d: 4 }, // 测试value为0的情况
        { label: '空字符串', value: '', a: 1, b: 2, c: 3, d: 4 }, // 测试value为空字符串的情况
        { label: '张三', value: 1, a: 1, b: 2, c: 3, d: 4 },
        { label: '李四', value: 2, a: 1, b: 2, c: 3, d: 4, disabled: true },
        { label: '王五', value: 3, a: 1, b: 2, c: 3, d: 4 },
      ],
      options1: [],
      popupProps: {
        'on-scroll-to-bottom': (pagination) =>
          new Promise((resolve) => {
            const res = [];

            for (let i = 1; i < pagination.limit; i++) {
              res.push({
                label: `第 ${(pagination.current - 1) * pagination.limit + i} 项`,
                value: (pagination.current - 1) * pagination.limit + i,
              });
            }

            // 直接使用滚动触底事件
            resolve(res);
          }),
      },
      remoteMethod: (cb, config) => {
        setTimeout(() => {
          cb([{ label: '张三1', value: 1 }]);
        }, 500);
      },
    };
  },
  methods: {
    handleSearch(query, page) {
      console.log('%c [  ]-77-「basic」', 'font-size:13px; background:pink; color:#bf2c9f;', query, page);
      return {
        data: [
          {
            label: `${query}1`,
            value: `${query}1`,
          },
          {
            label: `${query}2`,
            value: `${query}2`,
          },
          {
            label: `${query}3`,
            value: `${query}3`,
          },
        ],
        hasMore: false,
      };
    },
    handleLoadMore(query, page) {
      return new Promise((resolve) => {
        setTimeout(() => {
          if (page < 5) {
            resolve({
              data: new Array(15).fill(1).map((item, index) => ({
                label: `${query}${page}_${index + 1}`,
                value: `${query}${page}_${index + 1}`,
              })),
              hasMore: true,
            });
          }
          resolve({
            data: [
              {
                label: `${query}1${page}`,
                value: `${query}11${page}`,
              },
              {
                label: `${query}21${page}`,
                value: `${query}21${page}`,
              },
              {
                label: `${query}31${page}`,
                value: `${query}31${page}`,
              },
            ],
            hasMore: false,
          });
        }, 500);
      });
    },
  },
};
</script>
