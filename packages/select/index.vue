<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-18 11:46:13
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-23 17:59:09
 * @FilePath: /vite-element-components/packages/select/index.vue
 * @Description: 基于 Element UI 的 Select 组件封装，支持以下特性：
 *  1. 支持远程搜索和本地搜索
 *  2. 支持懒加载和无限滚动加载更多数据
 *  3. 支持单选和多选模式
 *  4. 支持只读模式，以文本形式展示选中值
 *  5. 支持自定义选项内容
 *  6. 支持清空、禁用等基础功能
-->
<template>
  <div class="x-select">
    <el-select
      v-if="mode !== 'view'"
      ref="select"
      v-model="inputValue"
      v-el-select-loadMore="loadMore"
      :multiple="multiple"
      :disabled="disabled || mode === 'disabled'"
      :size="size"
      :clearable="clearable"
      :collapse-tags="collapseTags"
      :placeholder="placeholder"
      :filterable="filterable"
      :remote="remote"
      :remote-method="remoteMethod"
      :reserve-keyword="reserveKeyword"
      :popper-class="popperClass"
      :popper-append-to-body="popperAppendToBody"
      :automatic-dropdown="automaticDropdown"
      @change="handleChange"
      @visible-change="handleVisibleChange"
      @remove-tag="handleRemoveTag"
      @clear="handleClear"
      @blur="handleBlur"
      @focus="handleFocus"
    >
      <el-option
        v-for="(item, index) in innerOptions"
        :key="item[valueKey] ? item[valueKey] : `option-${index}`"
        :label="item[labelKey]"
        :value="item[valueKey]"
        :disabled="item.disabled"
      >
        <slot name="option" :item="item">
          {{ item[labelKey] ? item[labelKey] : '' }}
        </slot>
      </el-option>

      <template #empty>
        <p v-if="!hasMore && !innerOptions.length" class="x-select__empty">暂无数据</p>
      </template>

      <el-option v-if="loading" :value="'__loading__'" key="__loading__" disabled>
        <p class="x-select__loading">加载中...</p>
      </el-option>
    </el-select>
    <tooltip v-else :content="getValue()" />
  </div>
</template>

<script>
import Tooltip from '../tooltip/index.vue';

/**
 * 检查值是否有效（非空字符串、非null、非undefined）
 * 注意：数字0被认为是有效值
 * @param {*} val - 要检查的值
 * @returns {boolean} - 值是否有效
 */
const valuable = (val) => val !== null && val !== undefined;

export default {
  name: 'XSelect',
  components: {
    Tooltip,
  },
  /**
   * 自定义指令
   */
  directives: {
    /**
     * 下拉框滚动加载更多数据的指令
     * BUG修复：添加错误处理和内存泄漏防护
     */
    'el-select-loadMore': {
      inserted(el, binding) {
        // 查找下拉框滚动容器
        const SELECT_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');

        // BUG修复：检查DOM元素是否存在，避免空指针异常
        if (!SELECT_DOM) {
          console.warn('XSelect: 未找到下拉框滚动容器，无法绑定滚动加载事件');
          return;
        }

        // 滚动事件处理函数
        const scrollHandler = function () {
          // 计算是否滚动到底部（允许1px的误差）
          const condition = Math.abs(this.scrollHeight - this.scrollTop - this.clientHeight) <= 1;

          // BUG修复：添加防抖机制，避免频繁触发
          if (condition && this.scrollTop > 0 && typeof binding.value === 'function') {
            binding.value();
          }
        };

        // 绑定滚动事件
        SELECT_DOM.addEventListener('scroll', scrollHandler);

        // BUG修复：在元素上存储事件处理函数引用，用于后续清理
        el._scrollHandler = scrollHandler;
        el._selectDom = SELECT_DOM;
      },

      // BUG修复：添加unbind钩子，防止内存泄漏
      unbind(el) {
        if (el._selectDom && el._scrollHandler) {
          el._selectDom.removeEventListener('scroll', el._scrollHandler);
          delete el._scrollHandler;
          delete el._selectDom;
        }
      },
    },
  },
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    /**
     * 选中的值，支持单选和多选
     * BUG修复：多选时默认值应该是数组，单选时是空字符串
     */
    value: {
      type: [String, Number, Array],
      default() {
        // 根据是否多选返回不同的默认值
        return this.multiple ? [] : '';
      },
    },
    /**
     * 是否多选
     */
    multiple: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否禁用
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * 输入框尺寸
     */
    size: {
      type: String,
      default: '',
      validator: (value) => ['', 'medium', 'small', 'mini'].includes(value),
    },
    /**
     * 是否可以清空选项
     */
    clearable: {
      type: Boolean,
      default: true,
    },
    /**
     * 多选时是否将选中值按文字的形式展示
     */
    collapseTags: {
      type: Boolean,
      default: false,
    },
    /**
     * 占位符
     */
    placeholder: {
      type: String,
      default: '请选择',
    },
    /**
     * 是否可搜索
     */
    filterable: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否为远程搜索
     */
    remote: {
      type: Boolean,
      default: false,
    },
    /**
     * 作为 value 唯一标识的键名，绑定值为对象类型时必填
     */
    valueKey: {
      type: String,
      default: 'value',
    },
    /**
     * 作为 label 显示的键名，绑定值为对象类型时必填
     */
    labelKey: {
      type: String,
      default: 'label',
    },
    /**
     * 远程搜索方法返回后是否保留当前的搜索关键词
     */
    reserveKeyword: {
      type: Boolean,
      default: true,
    },
    /**
     * Select 下拉框的类名
     */
    popperClass: {
      type: String,
      default: '',
    },
    /**
     * 是否将弹出框插入至 body 元素
     */
    popperAppendToBody: {
      type: Boolean,
      default: true,
    },
    /**
     * 对于不可搜索的 Select，是否在输入框获得焦点后自动弹出选项菜单
     */
    automaticDropdown: {
      type: Boolean,
      default: false,
    },
    /**
     * 远程搜索方法
     * BUG修复：添加参数和返回值的类型说明
     * @param {string} query - 搜索关键词
     * @param {number} page - 页码
     * @returns {Promise<{data: Array, hasMore: boolean}>} 搜索结果
     */
    onSearch: {
      type: Function,
      default: null,
    },
    /**
     * 加载更多数据的方法
     * BUG修复：添加参数和返回值的类型说明
     * @param {string} query - 搜索关键词
     * @param {number} page - 页码
     * @returns {Promise<{data: Array, hasMore: boolean}>} 加载结果
     */
    onLoadMore: {
      type: Function,
      default: null,
    },
    /**
     * 选项数据
     * BUG修复：添加数组元素结构说明
     * @type {Array<{value: any, label: string, disabled?: boolean}>}
     */
    options: {
      type: Array,
      default: () => [],
    },
    /**
     * 编辑模式
     * @type {String}
     * @default 'edit'
     */
    mode: {
      type: String,
      default: 'edit',
    },
  },
  data() {
    return {
      query: '', // 当前搜索关键词
      page: 1, // 当前页码
      loading: false, // 是否正在加载
      hasMore: true, // 是否还有更多数据
      innerOptions: [...this.options], // 内部选项数据，使用浅拷贝避免直接修改props
    };
  },
  computed: {
    inputValue: {
      get() {
        if (this.multiple) {
          if (!Array.isArray(this.value)) {
            return [];
          }
          // 过滤掉数组中的null、undefined等无效值
          return this.value.filter((item) => valuable(item));
        }

        // 单选模式下的处理
        if (this.value === null || this.value === undefined) {
          return null; // 返回null而不是空字符串，避免与value为''的选项混淆
        }

        // 如果value是空字符串，返回空字符串（这是有效的选择）
        if (this.value === '') {
          return '';
        }

        // 其他情况（包括数字0）直接返回原值
        return this.value;
      },
      set(val) {
        // BUG修复：多选模式下过滤无效值，确保不会出现[null]的情况
        let cleanValue = val;
        if (this.multiple && Array.isArray(val)) {
          cleanValue = val.filter((item) => valuable(item));
        } else if (!this.multiple) {
          // 单选模式下，保持原值，包括null、undefined、空字符串和数字0
          cleanValue = val;
        }

        this.$emit('update:value', cleanValue);
        this.$emit('change', cleanValue);
      },
    },
  },
  watch: {
    options: {
      handler(val) {
        // BUG修复：添加数组类型检查
        if (!Array.isArray(val)) {
          console.warn('XSelect: options应该是数组类型');
          return;
        }

        // 非远程搜索，或远程搜索但没有查询词时，使用传入的 options
        if (!this.remote || (this.remote && !this.query)) {
          this.innerOptions = [...val]; // 使用浅拷贝
        }
      },
      immediate: true,
    },
  },
  created() {
    // BUG修复：添加类型检查和错误处理
    if (Array.isArray(this.options) && this.options.length) {
      this.innerOptions = [...this.options]; // 使用浅拷贝
    }
    // 没有 options 但有 onLoadMore 方法时，执行首次加载
    else if (typeof this.onLoadMore === 'function') {
      this.loadMore().catch((error) => {
        console.error('XSelect: 初始化加载数据失败', error);
      });
    }
  },
  methods: {
    /**
     * 获取选中值的文本表示
     * BUG修复：添加类型检查和错误处理
     * @returns {string} 选中值的文本，多选时用逗号分隔，无选中值时返回'-'
     */
    getValue() {
      // BUG修复：确保innerOptions是数组
      if (!Array.isArray(this.innerOptions)) {
        return '-';
      }

      if (this.multiple) {
        // BUG修复：确保inputValue是数组且不为空
        if (!Array.isArray(this.inputValue) || this.inputValue.length === 0) {
          return '-';
        }

        const selected = this.inputValue
          .map((val) => this.innerOptions.find((option) => option && option[this.valueKey] === val))
          .filter((option) => option) // 过滤掉undefined
          .map((option) => option[this.labelKey]);

        return selected.length > 0 ? selected.join('，') : '-';
      }

      // 单选模式
      // BUG修复：明确检查null和undefined，但允许空字符串和数字0
      if (this.inputValue === null || this.inputValue === undefined) {
        return '-';
      }

      const option = this.innerOptions.find((option) => option && option[this.valueKey] === this.inputValue);
      return option ? option[this.labelKey] : '-';
    },
    /**
     * 处理选中值变化事件
     * BUG修复：确保输出的值类型正确，过滤无效值
     * @param {string|number|Array} val - 新的选中值
     */
    handleChange(val) {
      let cleanValue = val;

      // BUG修复：多选模式下过滤掉null、undefined等无效值
      if (this.multiple && Array.isArray(val)) {
        cleanValue = val.filter((item) => valuable(item));
      } else if (!this.multiple) {
        // 单选模式下，保持原值，包括null、undefined、空字符串和数字0
        // 这样可以确保用户的选择被正确保存
        cleanValue = val;
      }

      this.$emit('input', cleanValue);
      this.$emit('change', cleanValue);
    },
    handleVisibleChange(visible) {
      this.$emit('visible-change', visible);
    },
    handleRemoveTag(tag) {
      this.$emit('remove-tag', tag);
    },
    handleClear() {
      this.$emit('clear');
      this.reset();
    },
    handleBlur(event) {
      this.$emit('blur', event);
    },
    handleFocus(event) {
      this.$emit('focus', event);
    },
    /**
     * 远程搜索方法
     * BUG修复：添加错误处理和参数验证
     * @param {string} query - 搜索关键词
     * @returns {Promise<void>}
     */
    async remoteMethod(query) {
      try {
        if (this.remote && typeof this.onSearch === 'function' && query) {
          this.query = query;
          this.page = 1;
          this.loading = true;

          const result = await this.onSearch(query, this.page);

          // BUG修复：验证返回数据格式
          if (!result || typeof result !== 'object') {
            throw new Error('onSearch方法应该返回包含data和hasMore字段的对象');
          }

          const { data, hasMore } = result;

          // BUG修复：验证data是数组
          if (!Array.isArray(data)) {
            throw new Error('onSearch返回的data字段应该是数组');
          }

          this.innerOptions = data;
          this.hasMore = Boolean(hasMore);

          // 如果还有更多数据且配置了加载更多方法，继续加载
          if (hasMore && typeof this.onLoadMore === 'function') {
            this.page += 1;
            await this.loadMore();
          }
        } else if (this.filterable && !this.remote) {
          // 本地搜索逻辑
          if (!Array.isArray(this.options)) {
            console.warn('XSelect: options应该是数组类型');
            return;
          }

          this.innerOptions = this.options.filter((item) => {
            if (!item || typeof item[this.labelKey] === 'undefined') {
              return false;
            }
            return item[this.labelKey].toString().toLowerCase().includes(query.toLowerCase());
          });
        } else if (this.remote && !query) {
          // 远程搜索但无查询词时，重置状态并重新加载分页数据
          this.reset();
        }
      } catch (error) {
        console.error('XSelect: 远程搜索失败', error);
        this.innerOptions = [];
      } finally {
        this.loading = false;
      }
    },
    /**
     * 加载更多数据
     * BUG修复：添加错误处理和参数验证
     * @returns {Promise<void>}
     */
    async loadMore() {
      if (!this.loading && this.hasMore && typeof this.onLoadMore === 'function') {
        this.loading = true;

        try {
          const result = await this.onLoadMore(this.query, this.page);

          // BUG修复：验证返回数据格式
          if (!result || typeof result !== 'object') {
            throw new Error('onLoadMore方法应该返回包含data和hasMore字段的对象');
          }

          const { data, hasMore } = result;

          // BUG修复：验证data是数组
          if (!Array.isArray(data)) {
            throw new Error('onLoadMore返回的data字段应该是数组');
          }

          // 追加新数据到现有选项中，避免重复数据
          const existingValues = new Set(this.innerOptions.map((item) => item[this.valueKey]));
          const newData = data.filter((item) => !existingValues.has(item[this.valueKey]));

          this.innerOptions = [...this.innerOptions, ...newData];
          this.hasMore = Boolean(hasMore);

          if (hasMore) {
            this.page += 1;
          }
        } catch (error) {
          console.error('XSelect: 加载更多数据失败', error);
          this.hasMore = false; // 出错时停止继续加载
        } finally {
          this.loading = false;
        }
      }
    },
    /**
     * 重置组件状态
     * BUG修复：添加错误处理和状态同步
     */
    reset() {
      this.page = 1;
      this.query = '';
      this.hasMore = true;
      this.loading = false; // BUG修复：重置时清除loading状态

      // BUG修复：根据multiple属性设置正确的默认值
      const newValue = this.multiple ? [] : '';

      this.inputValue = newValue;

      // BUG修复：同步更新父组件的值
      this.$emit('input', newValue);
      this.$emit('change', newValue);

      this.innerOptions = [];

      // 重置后重新加载数据
      if (typeof this.onLoadMore === 'function') {
        this.loadMore().catch((error) => {
          console.error('XSelect: 重置后加载数据失败', error);
        });
      } else if (Array.isArray(this.options) && this.options.length) {
        this.innerOptions = [...this.options]; // 使用浅拷贝
      }
    },
  },
};
</script>

<style lang="scss">
@import '../styles/index.scss';
.x-select {
  width: 100%;
  .el-select {
    width: 100%;
  }
  &__loading,
  &__empty,
  &__more {
    padding: 4px;
    margin: 0;
    text-align: center;
    color: #999;
    font-size: 14px;
    line-height: 1;
  }

  &__more {
    cursor: pointer;
    &:hover {
      color: var(--brand-6, #0f45ea);
    }
  }

  // 新增：禁用状态下的选项样式
  .el-select-dropdown__item.is-disabled {
    &:hover {
      background-color: transparent;
    }
    .x-select__loading,
    .x-select__more {
      padding: 0;
    }
  }
}
</style>
