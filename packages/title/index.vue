<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-06 15:40:37
 * @FilePath: /vite-element-components/packages/title/index.vue
 * @Description:
-->
<script lang="jsx">
/**
 * 标题组件
 * @component XTitle
 * @description 标题组件
 */
export default {
  name: 'XTitle',
  title: '普通文本组件',
  props: {
    /**
     * 显示的文本内容
     * @type {string|number}
     * @default ''
     */
    label: {
      type: [String, Number],
      default: '',
    },
    tips: {
      type: String,
      default: '',
    },
  },
  render() {
    return (
      <div class="x-title">
        {this.label}
        {this.tips && <span class="x-title-tips">{this.tips}</span>}
      </div>
    );
  },
};
</script>
<style lang="scss" scoped>
@import '../styles/index.scss';
.x-title {
  font-size: 14px;
  line-height: 20px;
  padding-bottom: 20px;
  position: relative;
  &::before {
    display: inline-block;
    content: '';
    width: 4px;
    height: 20px;
    border-radius: 2px;
    background-color: var(--brand-6, #0f45ea);
    margin-right: 6px;
    vertical-align: top;
  }
  &-tips {
    font-size: 12px;
    margin-left: 5px;
    line-height: 20px;
    color: #737a94;
  }
}
</style>
