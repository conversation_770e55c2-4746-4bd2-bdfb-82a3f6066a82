<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 11:46:08
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-26 17:57:17
 * @FilePath: /vite-element-components/packages/title/doc/doc.md
 * @Description:
-->
<script>
  import BasicVue from './basic.vue';
  import Preview from '@/components/preview.vue';

  export default {
    components: {
      BasicVue,
      Preview
    }
  };
</script>

# Title 标题组件

用于展示标题

## 基础用法

基础的文本展示用法。

<basic-vue/>
<preview comp-name='title' demo-name='basic'/>

## 组件属性

| 参数  | 说明           | 类型          | 可选值 | 默认值 |
| ----- | -------------- | ------------- | ------ | ------ |
| label | 显示的文本内容 | string/number | —      | ''     |

## 样式

组件使用了以下CSS变量，可以通过覆盖这些变量来自定义样式：

| CSS变量名 | 说明             | 默认值  |
| --------- | ---------------- | ------- |
| --brand-6 | 标题前缀条的颜色 | #0f45ea |

## 注意事项

- 标题组件主要用于页面内容的分区标题，建议在需要明确区分内容块的场景下使用
- 组件左侧会自动添加一个醒目的竖条样式，可以通过CSS变量自定义其颜色
- 组件会自动添加下方间距(margin-bottom: 20px)，无需额外设置
