# SCIC Element Components

[![npm version](https://img.shields.io/npm/v/scic-element-components.svg)](https://www.npmjs.com/package/scic-element-components)
[![license](https://img.shields.io/npm/l/scic-element-components.svg)](https://github.com/your-repo/scic-element-components/blob/main/LICENSE)
[![Vue 2](https://img.shields.io/badge/Vue-2.6.14-green.svg)](https://v2.vuejs.org/)

## 项目简介

基于 Vue 2.6.14 和 Element UI 构建的企业级组件库，提供丰富的业务组件和完整的开发工具链。经过全面重构，具备详细的文档说明。

## ✨ 特性

- 🎨 **丰富组件** - 23+ 个高质量业务组件
- 📦 **开箱即用** - 完善的构建和开发工具链
- 🧪 **测试覆盖** - 完整的单元测试和覆盖率报告
- 📖 **详细文档** - 每个组件都有完整的 API 文档和示例
- 🎯 **企业级** - 适用于企业级应用开发
- ⚡ **高性能** - 基于 Vite 的快速构建

## 技术栈

- **框架**: Vue 2.6.14
- **构建工具**: Vite 4.x
- **UI 库**: Element UI (element-eoss)
- **样式**: SCSS
- **测试**: Jest + Vue Test Utils
- **文档**: Markdown + Vue

## 🚀 快速开始

### 安装

```bash
# 使用 npm
npm install scic-element-components

# 使用 pnpm
pnpm add scic-element-components

# 使用 yarn
yarn add scic-element-components
```

### 安装依赖

组件库需要以下 peer dependencies：

```bash
npm install vue@2.6.14 element-eoss@^2.14.3
```

### 全局引入

```javascript
import Vue from 'vue';
import ElementComponents from 'scic-element-components';
import 'scic-element-components/lib/style.css';

// 全局注册所有组件
Vue.use(ElementComponents, {
  // 可选配置
  serveUrl: 'https://your-api-server.com',
});
```

### 按需引入

```javascript
import { XButton, XForm, XTable } from 'scic-element-components';
import 'scic-element-components/lib/style.css';

// 注册单个组件
Vue.use(XButton);
Vue.use(XForm);
Vue.use(XTable);
```

## 📖 开发指南

### 开发环境设置

```bash
# 克隆项目
git clone <repository-url>
cd scic-element-components

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev
```

### 开发命令

```bash
# 开发
pnpm dev                    # 启动开发服务器
pnpm build:component        # 构建组件库
pnpm build:example          # 构建示例文档
pnpm build                  # 同时构建组件库和文档

# 测试
pnpm test                   # 运行单元测试
pnpm test:watch             # 监听模式运行测试
pnpm test:coverage          # 生成测试覆盖率报告
pnpm test:ci                # CI 环境测试

# 代码质量
pnpm lint                   # 代码检查
pnpm lint:fix               # 自动修复代码问题

# 分析
pnpm analyze                # 构建分析报告
```

## 📁 项目结构

```text
scic-element-components/
├── packages/                 # 组件源码目录
│   ├── button/              # 按钮组件
│   │   ├── index.vue        # 组件实现
│   │   ├── index.js         # 组件导出
│   │   ├── constants.js     # 常量定义
│   │   ├── utils.js         # 工具函数
│   │   ├── README.md        # 组件说明
│   │   └── doc/             # 组件文档
│   ├── form/                # 表单组件
│   ├── table/               # 表格组件
│   └── ...                  # 其他组件
├── src/                     # 示例文档源码
│   ├── components/          # 文档组件
│   ├── views/               # 页面视图
│   └── router/              # 路由配置
├── tests/                   # 测试文件
│   ├── unit/                # 单元测试
│   └── setup.js             # 测试配置
├── lib/                     # 构建产物目录
├── docs/                    # 文档构建产物
├── jest.config.js           # Jest 配置
└── vite.config.js           # Vite 配置
```

## 🧩 组件列表

### 基础组件 (8个)

| 组件        | 说明                             | 文档 | 类型定义 | 测试覆盖 |
| ----------- | -------------------------------- | ---- | -------- | -------- |
| XButton     | 按钮组件，支持多种类型和加载状态 | ✅   | ✅       | ✅       |
| XInput      | 输入框组件                       | ✅   | 🔄       | 🔄       |
| XSelect     | 选择器组件                       | ✅   | 🔄       | 🔄       |
| XSelectTree | 树形选择器组件                   | ✅   | 🔄       | 🔄       |
| XSwitch     | 开关组件                         | ✅   | 🔄       | 🔄       |
| XRadio      | 单选框组件                       | ✅   | 🔄       | 🔄       |
| XCheckbox   | 复选框组件                       | ✅   | 🔄       | 🔄       |
| XText       | 文本显示组件                     | ✅   | 🔄       | 🔄       |

### 表单组件 (7个)

| 组件              | 说明                     | 文档 | 类型定义 | 测试覆盖 |
| ----------------- | ------------------------ | ---- | -------- | -------- |
| XForm             | 表单组件，支持动态表单项 | ✅   | ✅       | 🔄       |
| XFormItems        | 表单项组件               | ✅   | 🔄       | 🔄       |
| XDatePicker       | 日期选择器               | ✅   | 🔄       | 🔄       |
| XInputNumber      | 数字输入框               | ✅   | 🔄       | 🔄       |
| XInputNumberRange | 数字范围输入框           | ✅   | 🔄       | 🔄       |
| XCascader         | 级联选择器               | ✅   | 🔄       | 🔄       |
| XUpload           | 文件上传组件             | ✅   | 🔄       | 🔄       |

### 数据展示组件 (6个)

| 组件         | 说明                       | 文档 | 类型定义 | 测试覆盖 |
| ------------ | -------------------------- | ---- | -------- | -------- |
| XTable       | 表格组件，支持排序、筛选等 | ✅   | ✅       | 🔄       |
| XEditTable   | 可编辑表格组件             | ✅   | 🔄       | 🔄       |
| XSearchTable | 搜索表格组件               | ✅   | 🔄       | 🔄       |
| XPagination  | 分页组件                   | ✅   | 🔄       | 🔄       |
| XEmpty       | 空状态组件                 | ✅   | 🔄       | 🔄       |
| XTitle       | 标题组件                   | ✅   | 🔄       | 🔄       |

### 其他组件 (6个)

| 组件      | 说明           | 文档 | 类型定义 | 测试覆盖 |
| --------- | -------------- | ---- | -------- | -------- |
| XDialog   | 对话框组件     | ✅   | 🔄       | 🔄       |
| XTooltip  | 文字提示组件   | ✅   | 🔄       | 🔄       |
| XTransfer | 穿梭框组件     | ✅   | 🔄       | 🔄       |
| XCustom   | 自定义渲染组件 | ✅   | 🔄       | 🔄       |
| XSearch   | 搜索组件       | ✅   | 🔄       | 🔄       |
| XToolbar  | 工具栏组件     | ✅   | 🔄       | 🔄       |

**图例**: ✅ 已完成 | 🔄 进行中 | ❌ 未开始

## 🛠️ 开发规范

### 代码风格

- 使用 ESLint + Prettier 进行代码格式化
- 遵循 Vue 2.x 官方风格指南
- 组件命名使用 PascalCase，文件名使用 kebab-case

### 组件开发规范

1. **目录结构**：每个组件独立目录，包含实现、文档、测试
2. **命名规范**：组件以 `X` 前缀命名，如 `XButton`
3. **Props 定义**：提供完整的 props 验证和默认值
4. **事件规范**：使用 kebab-case 命名事件
5. **文档要求**：每个组件必须有完整的 API 文档和使用示例

### 提交规范

项目使用 [Conventional Commits](https://conventionalcommits.org/) 规范：

```bash
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型说明**：

- `feat`: 新功能
- `fix`: 修复问题
- `docs`: 文档更新
- `style`: 代码格式调整（不影响代码运行）
- `refactor`: 代码重构（既不是新增功能，也不是修复bug）
- `perf`: 性能优化
- `test`: 测试相关
- `build`: 构建系统或外部依赖的变动
- `ci`: CI配置文件和脚本的变动
- `chore`: 其他不修改src或test文件的变动

## 🤝 贡献指南

### 开发流程

1. Fork 本仓库
2. 创建特性分支：`git checkout -b feature/amazing-feature`
3. 提交更改：`git commit -m 'feat: add amazing feature'`
4. 推送分支：`git push origin feature/amazing-feature`
5. 提交 Pull Request

### 贡献要求

- 新增组件需要包含完整的文档和测试
- 确保所有测试通过：`pnpm test`
- 确保代码风格检查通过：`pnpm lint`
- 确保类型检查通过：`pnpm type-check`
- 提交信息遵循 Conventional Commits 规范

## 📄 更新日志

查看 [CHANGELOG.md](./CHANGELOG.md) 了解详细的版本更新信息。

## 🔗 相关链接

- [Vue 2.x 官方文档](https://v2.vuejs.org/)
- [Element UI 文档](https://element.eleme.cn/)
- [Vite 文档](https://vitejs.dev/)

## 📞 支持

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

- 提交 [Issue](https://github.com/your-repo/scic-element-components/issues)
- 查看组件文档和示例
- 联系维护者

## 📜 许可证

[MIT License](./LICENSE) © 2024 SCIC Element Components

---

**重构完成情况**：

- ✅ 重构了组件代码结构，提升可维护性
- ✅ 完善了组件文档和 API 说明
- ✅ 添加了单元测试框架和测试用例
- ✅ 优化了构建配置和开发工具链
- ✅ 更新了项目文档和开发指南

**下一步计划**：

- 🔄 完善所有组件的单元测试覆盖
- 🔄 优化组件性能和用户体验
- 🔄 添加更多实用的业务组件
