{
  "extends": ["airbnb-base", "prettier", "plugin:vue/essential"],
  "env": {
    "browser": true,
    "node": true,
    "jest": true,
    "es6": true,
  },
  "globals": {
    "cy": "readonly",
  },
  "plugins": ["vue"],
  "parserOptions": {
    "sourceType": "module",
    "allowImportExportEverywhere": true,
    "ecmaFeatures": {
      "jsx": true,
    },
  },
  "rules": {
    "import/no-extraneous-dependencies": 0,
    "import/extensions": 0,
    "import/no-unresolved": 0,
    "import/prefer-default-export": 0,
    "camelcase": 0,
    "class-methods-use-this": 0,
    "new-cap": 0,
    "no-async-promise-executor": 0,
    "no-empty": 0,
    "no-new": 1,
    "no-shadow": 0,
    "no-console": 0,
    "no-underscore-dangle": 0,
    "no-confusing-arrow": 0,
    "no-plusplus": [
      "error",
      {
        "allowForLoopAfterthoughts": true,
      },
    ],
    "no-param-reassign": 0,
    "no-restricted-syntax": 0,
    "func-style": 0,
    "func-names": 0,
    "prefer-default-export": 0,
    "max-len": 0,
    "consistent-return": 0,
  },
  "overrides": [
    {
      "files": ["*.vue"],
      "rules": {
        "vue/return-in-computed-property": 1,
        "vue/order-in-components": 2,
        "vue/component-name-in-template-casing": ["error", "kebab-case"],
        "vue/attribute-hyphenation": ["error", "always"],
        "vue/require-default-prop": 0,
        "import/order": "off",
      },
    },
    {
      "files": ["src/*", "*.js"],
      "rules": {
        "no-var-requires": 0,
        "no-console": 0,
        "no-unused-expressions": 0,
        "import/order": "off",
      },
    },
  ],
}
