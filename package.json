{"name": "scic-element-components", "description": "基于element-eoss研发的业务组件库", "version": "1.0.27", "author": "<PERSON><PERSON> <<EMAIL>>", "private": false, "main": "./lib/scic-element-components.js", "fileName": "scic-element-components", "license": "MIT", "type": "module", "files": ["lib/*", "CHANGELOG.md"], "scripts": {"dev": "vite  --host 0.0.0.0 --port 5010", "build:component": "vite build --mode lib", "build:example": "vite build", "build": "pnpm build:component && pnpm build:example", "preview": "vite preview", "analyze": "ANALYZE=true npm run build:component", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "clean:cache": "rimraf .eslintcache && rimraf node_modules && pnpm install", "lint": "eslint --ext .js,.vue packages", "lint:fix": "eslint --fix --ext .js,.vue packages", "commit": "git add . && git-cz", "prepare": "husky install", "publish:auto": "sh ./publish.sh && npm run changelog"}, "peerDependencies": {"element-eoss": "^2.14.3", "vue": "2.6.14"}, "dependencies": {"dayjs": "^1.11.10", "element-eoss": "^2.14.3", "highlight.js": "^11.9.0", "lodash": "^4.17.21", "scic-element-components": "^1.0.15", "sortablejs": "^1.15.6", "uuid": "^9.0.0", "vue": "2.6.14", "vue-dompurify-html": "^2.5.2", "vue-print-nb": "^1.7.5", "vue-router": "^3.3.3", "vxe-pc-ui": "^3.7.30", "vxe-table": "^3.17.5", "xe-utils": "^3.7.8"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@types/lodash": "^4.17.7", "@types/node": "^18.18.4", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "autoprefixer": "^10.4.16", "conventional-changelog-cli": "^4.1.0", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-vue": "^7.8.0", "esm": "^3.2.25", "git-cz": "4.8.0", "husky": "^8.0.3", "lint-staged": "^13.3.0", "postcss": "^8.1.0", "prettier": "^3.4.1", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.32.12", "sass-loader": "13.2.0", "unplugin-vue-components": "^0.22.4", "vite": "^4.5.2", "vite-plugin-chunk-split": "^0.5.0", "vite-plugin-lazy-import": "^1.0.7", "vite-plugin-md": "^0.22.5", "vite-plugin-vue2": "^2.0.3", "vue-markdown": "^2.2.4", "vue-template-compiler": "2.6.14"}, "lint-staged": {"*.{js,jsx,vue}": ["prettier --write packages", "pnpm lint:fix"], "*.{html,sass,scss,less}": ["prettier --write packages"], "*.md": ["prettier --write packages"]}}